import React, { useContext, useRef } from 'react';
import { TreeContext } from '@/context/TreeContext';
import ArboristTreeNode from '@/components/Tree/ArboristTreeNode';
import TreeSearchPanel from '../TreeViewHeader';
import { useTreeHeight } from '@/hooks/useTreeHeight';

interface TreeViewProps {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
}

const TreeView: React.FC<TreeViewProps> = ({ searchTerm, setSearchTerm }) => {
  const searchPanelRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const treeContext = useContext(TreeContext);
  if (!treeContext) {
    throw new Error('TreeView must be used within a TreeProvider');
  }
  const { data, handleComponentSelect, isTreeVisible } = treeContext;

  // Use the custom hook to calculate tree height
  const treeHeight = useTreeHeight({
    searchPanelRef,
    minHeight: 300,
    maxHeightRatio: 0.8,
    paddingBottom: 24,
    headerHeight: 60,
  });

  return (
    <div
      ref={containerRef}
      className={`flex flex-col w-full ml-4 overflow-x-hidden h-full ${isTreeVisible ? '' : 'hidden'}`}
    >
      <div ref={searchPanelRef}>
        <TreeSearchPanel searchTerm={searchTerm} setSearchTerm={setSearchTerm} />
      </div>

      <div className="flex-1 min-h-0">
        <ArboristTreeNode
          path={[]}
          data={data}
          searchTerm={searchTerm ? searchTerm : ''}
          onComponentSelect={handleComponentSelect}
          calculatedHeight={treeHeight}
        />
      </div>
    </div>
  );
};

export default React.memo(TreeView);
