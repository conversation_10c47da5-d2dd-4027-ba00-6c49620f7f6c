image: node:18

stages:
  - security
  - publish

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_PIPELINE_SOURCE == 'web'
    - if: $CI_COMMIT_TAG =~ /^(?P<major>0|[1-9]\d*)\.(?P<minor>0|[1-9]\d*)\.(?P<patch>0|[1-9]\d*)$/
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_BRANCH == 'develop'
    - if: $CI_PIPELINE_SOURCE == 'pipeline'

# Global cache configuration for React project
cache:
  key: "${CI_COMMIT_REF_SLUG}"
  paths:
    # Dependencies
    - node_modules/
    - .npm/
    # Build output
    - build/
    - dist/
    # React/webpack cache
    - .cache/
    # Package manager lock file for cache validation
    - package-lock.json
  policy: pull-push

security:
  stage: security
  script:
    - npm install
    - npm audit || true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

publish:
  stage: publish
  needs:
    - optional: true
      job: security
  script:
    # Install dependencies
    - npm ci --production=false
    # Build for production
    - npm run build
    # Optional: can add environment-specific configuration here if needed
    # - cp .env.production .env
  variables:
    NODE_ENV: production
  artifacts:
    paths:
      - "build/*"
      - "dist/*"
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_TAG =~ /^(?P<major>0|[1-9]\d*)\.(?P<minor>0|[1-9]\d*)\.(?P<patch>0|[1-9]\d*)$/
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_BRANCH == 'develop'
    - if: $CI_PIPELINE_SOURCE == 'pipeline'