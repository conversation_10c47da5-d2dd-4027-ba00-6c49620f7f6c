$fontWeights = @("400", "600", "700")
$fontStyles = @("normal")
$formats = @("woff2", "woff")
$fontDir = ".\src\assets\fonts\titillium-web"

# Create directory if it doesn't exist
if (-not (Test-Path $fontDir)) {
    New-Item -ItemType Directory -Force -Path $fontDir
}

$fonts = @(
    @{
        weight = "400"
        style = "normal"
        woff2 = "https://fonts.gstatic.com/s/titilliumweb/v15/NaPecZTIAOhVxoMyOr9n_E7fdMPmDaZRbrw.woff2"
        woff = "https://fonts.gstatic.com/s/titilliumweb/v15/NaPecZTIAOhVxoMyOr9n_E7fdMPmDQ.woff"
    },
    @{
        weight = "600"
        style = "normal"
        woff2 = "https://fonts.gstatic.com/s/titilliumweb/v15/NaPDcZTIAOhVxoMyOr9n_E7ffBzCGIVzY5abuWIGxA.woff2"
        woff = "https://fonts.gstatic.com/s/titilliumweb/v15/NaPDcZTIAOhVxoMyOr9n_E7ffBzCGItzYw.woff"
    },
    @{
        weight = "700"
        style = "normal"
        woff2 = "https://fonts.gstatic.com/s/titilliumweb/v15/NaPDcZTIAOhVxoMyOr9n_E7ffHjDGIVzY5abuWIGxA.woff2"
        woff = "https://fonts.gstatic.com/s/titilliumweb/v15/NaPDcZTIAOhVxoMyOr9n_E7ffHjDGItzYw.woff"
    }
)

foreach ($font in $fonts) {
    # Download WOFF2 file
    $outputFile = "${fontDir}/titillium-web-$($font.weight)-$($font.style).woff2"
    Write-Host "Downloading $($font.woff2) to ${outputFile}"
    try {
        Invoke-WebRequest -Uri $font.woff2 -OutFile $outputFile
    } catch {
        Write-Host "Error downloading $($font.woff2): $_"
    }
    
    # Download WOFF file
    $outputFile = "${fontDir}/titillium-web-$($font.weight)-$($font.style).woff"
    Write-Host "Downloading $($font.woff) to ${outputFile}"
    try {
        Invoke-WebRequest -Uri $font.woff -OutFile $outputFile
    } catch {
        Write-Host "Error downloading $($font.woff): $_"
    }
}

Write-Host "All font files downloaded successfully!"
