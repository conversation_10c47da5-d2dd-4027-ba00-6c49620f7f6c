// src/utils/errors.ts
import { AxiosError } from 'axios';

interface ErrorResponse {
  message?: string;
  error?: string;
}

export class ApiError extends <PERSON>rror {
  constructor(
    message: string,
    public readonly context: string,
    public readonly originalError: unknown
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export class TransformationError extends Error {
  constructor(
    message: string,
    public readonly context: string,
    public readonly originalData: unknown
  ) {
    super(message);
    this.name = 'TransformationError';
  }
}

export const getErrorMessage = (error: unknown): string => {
  if (error && typeof error === 'object' && 'isAxiosError' in error) {
    const axiosError = error as AxiosError<ErrorResponse>;
    const responseMessage = axiosError.response?.data?.message;
    if (responseMessage && typeof responseMessage === 'string') {
      return responseMessage;
    }
    return axiosError.message || 'Unknown API error';
  }

  if (error instanceof Error) {
    return error.message;
  }

  return 'Unknown error occurred';
};
