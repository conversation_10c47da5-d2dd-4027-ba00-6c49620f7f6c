import KeyValueFieldArea, {
  FieldProps,
  FieldType,
} from '@/components/shared/KeyValueFieldArea/KeyValueFieldArea';
import { displayTextForExecutionConfiguration } from '@/utils/jobs';
import { Job } from '@/utils/types';
import { t } from 'i18next';

interface JobDetailsProps {
  job: Job;
}

const JobDetails: React.FC<JobDetailsProps> = ({ job }) => {
  const fields: FieldProps[] = [
    { label: t('jobs.id'), value: job.id, fieldType: FieldType.idField },
    {
      label: t('jobs.versionVsBackup'),
      value: job.latestResult.versionVsBackup,
      fieldType: FieldType.compareResultField,
    },
    {
      label: t('jobs.backupVsBackup'),
      value: job.latestResult.backupVsBackup,
      fieldType: FieldType.compareResultField,
    },
    { label: t('jobs.uploadType'), value: job.uploadType },
    {
      label: t('jobs.execution'),
      value: displayTextForExecutionConfiguration(job.executionConfiguration),
    },
    {
      label: t('jobs.onError'),
      value: job.isRepeated
        ? t('jobs.repeated', { minutes: job.repeatDelay, repeats: job.numberOfRepeats })
        : t('jobs.notRepeated'),
    },
    { label: t('jobs.nextStart'), value: '' },
    {
      label: t('jobs.whoToNotify'),
      value: job.whoToNotify === undefined ? '' : job.whoToNotify,
      fieldType: FieldType.largeTextField,
    },
  ];

  return (
    <div className={'details-container pt-4 pb-2'}>
      <KeyValueFieldArea fields={fields} />
    </div>
  );
};

export default JobDetails;
