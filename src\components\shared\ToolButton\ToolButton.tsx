import dropDownIndicatorPrimarySVG from '@/assets/svgs/dark/angle-down.svg';
import dropDownIndicatorSecondarySVG from '@/assets/svgs/dark/angle-down.svg';

import { colorForLockState } from '@/utils/lockState';
import { LockState } from '@/utils/types';

export enum ToolButtonStyle {
  PrimaryBlue,
  PrimaryYellow,
  Secondary,
  Locked,
  Unlocked,
  UnderDevelopment,
  None,
}

interface ToolButtonProps {
  type?: ToolButtonStyle;
  text?: string;
  onClick?: any;
  icon?: string;
  active?: boolean;
  size?: number;
  dropDownIndicator?: boolean;
}

const ToolButton: React.FC<ToolButtonProps> = ({
  type = ToolButtonStyle.None,
  text,
  icon,
  size = 8,
  onClick = () => {},
  dropDownIndicator = false,
  active = true,
}) => {
  let colorStyles: string;
  let iconColorStyles = '';
  let dropDown = dropDownIndicatorPrimarySVG;

  if (!active) {
    colorStyles = 'text-inverted-light bg-secondary-hover';
  } else {
    switch (type) {
      case ToolButtonStyle.None:
      default: {
        colorStyles = 'hover:hover:bg-hover';
        dropDown = dropDownIndicatorPrimarySVG;
        break;
      }
      case ToolButtonStyle.PrimaryBlue: {
        colorStyles =
          'bg-primary text-inverted-light hover:text-inverted-light hover:bg-secondary-hover';
        dropDown = dropDownIndicatorPrimarySVG;
        break;
      }
      case ToolButtonStyle.PrimaryYellow: {
        colorStyles =
          'bg-secondary text-primary hover:text-inverted-light hover:bg-secondary-hover';
        dropDown = dropDownIndicatorSecondarySVG;
        break;
      }
      case ToolButtonStyle.Secondary: {
        colorStyles =
          'bg-background text-primary hover:text-inverted-light hover:bg-secondary-hover';
        dropDown = dropDownIndicatorSecondarySVG;
        break;
      }
      case ToolButtonStyle.Locked: {
        colorStyles = 'bg-secondary-background text-primary hover:bg-hover';
        iconColorStyles = 'bg-' + colorForLockState(LockState.locked);
        dropDown = dropDownIndicatorSecondarySVG;
        break;
      }
      case ToolButtonStyle.Unlocked: {
        colorStyles = 'bg-secondary-background text-primary hover:bg-hover';
        iconColorStyles = 'bg-' + colorForLockState(LockState.unlocked);
        dropDown = dropDownIndicatorSecondarySVG;
        break;
      }
      case ToolButtonStyle.UnderDevelopment: {
        colorStyles = 'bg-secondary-background text-primary hover:bg-hover';
        iconColorStyles = 'bg-' + colorForLockState(LockState.underDevelopment);
        dropDown = dropDownIndicatorSecondarySVG;
        break;
      }
    }
  }

  const w = 'w-' + size;
  const h = 'h-' + size;

  return (
    <div
      onClick={active ? onClick : undefined}
      className={`flex items-center justify-center uppercase whitespace-nowrap ${h} ${colorStyles} ${active ? 'cursor-pointer' : ''}`}
    >
      {!icon && text && <span className={'w-3'} />}
      {icon && <img className={`${h} ${w} p-1.5 ${iconColorStyles}`} src={icon} />}
      {text && <span className="ml-2 mr-2">{text}</span>}
      {dropDownIndicator && (
        <img src={dropDown} className={'w-2.5 h-4 mx-1.5'} alt="dropdown-indicator" />
      )}
      {!dropDownIndicator && text && <span className={'w-3'} />}
    </div>
  );
};

export default ToolButton;
