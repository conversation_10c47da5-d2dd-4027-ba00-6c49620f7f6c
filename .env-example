# Environment variables for local development.
# Copy this file to .env and update the values for your specific environment.

# Base URL for the application's API endpoints (direct backend communication)
VITE_API_URL=http://localhost:5256/api/

# API request timeout in milliseconds
VITE_API_TIMEOUT=10000

# URL for the OAuth authentication server (direct communication)
VITE_OAUTH_URL=https://**************:64023/

# URL for the storage service (direct communication)
VITE_STORAGE_URL=http://localhost:5157/

# Set to true to enable experimental features, false to disable
VITE_EXPERIMENTAL=true

