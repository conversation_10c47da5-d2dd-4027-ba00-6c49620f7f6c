// src/hooks/queries/useRootId.ts
import { useQuery } from '@tanstack/react-query';
import { getRootId } from '@/services/api/index';
import { useAuth } from '@/context/AuthContext';

interface UseRootIdOptions {
  enabled?: boolean;
}

/**
 * Hook for fetching the root component ID
 * This hook honors the authentication state before making API calls
 *
 * @param options - Additional query options
 */
export const useRootId = (options: UseRootIdOptions = {}) => {
  const { enabled: customEnabled = true } = options;
  const { isAuthenticated, isTokenReady } = useAuth();

  return useQuery<string, Error>({
    queryKey: ['rootId'],
    queryFn: () => getRootId(),
    // CRITICAL FIX: Wait for both authentication AND token to be ready
    enabled: isAuthenticated && isTokenReady && customEnabled,
    staleTime: 30 * 60 * 1000, // 30 minutes - root ID rarely changes
    gcTime: 60 * 60 * 1000, // 1 hour
    retry: (failureCount, error) => {
      // Only retry network errors, not 4xx errors
      if (error.message?.includes('Network Error')) {
        return failureCount < 2;
      }
      return false;
    },
  });
};

// Type helper for components
export type UseRootIdResult = ReturnType<typeof useRootId>;
