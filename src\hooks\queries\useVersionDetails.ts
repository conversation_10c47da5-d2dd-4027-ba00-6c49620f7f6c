import { useQuery } from '@tanstack/react-query';
import { Version } from '@/utils/types';
import { fetchVersionDetails } from '@/services/api/index';
import { useAuth } from '@/context/AuthContext';

interface UseVersionDetailsOptions {
  enabled?: boolean;
}

/**
 * Hook for fetching version details for a component with pagination
 * This hook honors the authentication state before making API calls
 *
 * @param componentId - ID of the component to fetch versions for
 * @param pageNumber - Page number (default: 1)
 * @param pageSize - Page size (default: 20)
 * @param options - Additional query options
 */
export const useVersionDetails = (
  componentId: string | null,
  pageNumber = 1,
  pageSize = 20,
  options: UseVersionDetailsOptions = {}
) => {
  const { enabled: customEnabled = true } = options;
  const { isAuthenticated, isTokenReady } = useAuth();

  return useQuery({
    queryKey: ['versionDetails', componentId, pageNumber, pageSize],
    queryFn: () => {
      if (!componentId) {
        throw new Error('Component ID is required for useVersionDetails hook');
      }
      // Validate the component ID parameter
      if (componentId.trim() === '') {
        throw new Error('Empty component ID provided to useVersionDetails hook');
      }
      return fetchVersionDetails(componentId, pageNumber, pageSize);
    },
    // CRITICAL FIX: Wait for both authentication AND token to be ready
    enabled: !!componentId && isAuthenticated && isTokenReady && customEnabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // Only retry network errors, not 4xx errors
      if (error.message?.includes('Network Error')) {
        return failureCount < 3;
      }
      return false;
    },
    placeholderData: previousData => previousData,
  });
};

// Type helper for components
export type UseVersionDetailsResult = ReturnType<typeof useVersionDetails>;
