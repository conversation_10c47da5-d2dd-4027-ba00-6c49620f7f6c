{
  "compilerOptions": {
    "baseUrl": "./src", // This remains the same
    "paths": {
      "@/*": ["*"] // Simplifies the alias (removes the extra ./)
    },
    "outDir": "./build",
    "target": "es6", // Use es6 for modern JavaScript features (optional)
    "lib": ["dom", "dom.iterable", "esnext", "webworker"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "typeRoots": ["./node_modules/@types", "./src/types"], // Keep this as is
    "types": ["vite/client", "vite-plugin-svgr/client", "node"] // Include Vite types
  },
  "include": ["src", "vite-env.d.ts"] // Include vite-env.d.ts for custom types
}
