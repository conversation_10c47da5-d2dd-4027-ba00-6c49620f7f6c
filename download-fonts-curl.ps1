$fontUrls = @(
    # Regular (400)
    "https://fonts.gstatic.com/s/titilliumweb/v15/NaPecZTIAOhVxoMyOr9n_E7fdMPmDaZRbrw.woff2",
    "https://fonts.gstatic.com/s/titilliumweb/v15/NaPecZTIAOhVxoMyOr9n_E7fdMPmDQ.woff",
    
    # Semi-Bold (600)
    "https://fonts.gstatic.com/s/titilliumweb/v15/NaPDcZTIAOhVxoMyOr9n_E7ffBzCGIVzY5abuWIGxA.woff2",
    "https://fonts.gstatic.com/s/titilliumweb/v15/NaPDcZTIAOhVxoMyOr9n_E7ffBzCGItzYw.woff",
    
    # Bold (700)
    "https://fonts.gstatic.com/s/titilliumweb/v15/NaPDcZTIAOhVxoMyOr9n_E7ffHjDGIVzY5abuWIGxA.woff2",
    "https://fonts.gstatic.com/s/titilliumweb/v15/NaPDcZTIAOhVxoMyOr9n_E7ffHjDGItzYw.woff"
)

$outputFiles = @(
    ".\src\assets\fonts\titillium-web\titillium-web-400-normal.woff2",
    ".\src\assets\fonts\titillium-web\titillium-web-400-normal.woff",
    ".\src\assets\fonts\titillium-web\titillium-web-600-normal.woff2",
    ".\src\assets\fonts\titillium-web\titillium-web-600-normal.woff",
    ".\src\assets\fonts\titillium-web\titillium-web-700-normal.woff2",
    ".\src\assets\fonts\titillium-web\titillium-web-700-normal.woff"
)

# Create font directory if it doesn't exist
$fontDir = ".\src\assets\fonts\titillium-web"
if (-not (Test-Path $fontDir)) {
    New-Item -ItemType Directory -Force -Path $fontDir
}

for ($i = 0; $i -lt $fontUrls.Count; $i++) {
    Write-Host "Downloading $($fontUrls[$i]) to $($outputFiles[$i])"
    
    try {
        # Using curl for more reliable downloading
        curl -L -o $outputFiles[$i] $fontUrls[$i]
    } catch {
        Write-Host "Error downloading $($fontUrls[$i]): $_"
    }
}

Write-Host "Font download completed. Please check the font files in $fontDir"
