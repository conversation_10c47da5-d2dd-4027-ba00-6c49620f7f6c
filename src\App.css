@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-color: #0b4a82;
  --secondary-color: #edf2f6;

  /* Font Sizes */
  --font-family: 'Titillium Web', sans-serif;
  --font-size-small: 12px;
  --font-size-medium: 20px;
  --font-size-large: 24px;

  --button-padding: 12px 24px;
}

.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* App.css */
.toast-default {
  background-color: #0b4a82;
  color: white;
  font-size: var(--font-medium);
  padding: 0;
  margin: 0;
  border-radius: 0;
  box-shadow: none;
  display: revert;
  text-align: center;
}

/* Define default styles for toast body content */
.toast-body {
  font-family: Arial, sans-serif;
  /* Set a default font */
  line-height: 1.5;
  /* Adjust line height */
  margin: 0;
  /* Remove margin from the toast body */
}

a {
  color: var(--theme-primary-light-blue);
  text-decoration: underline;
}
