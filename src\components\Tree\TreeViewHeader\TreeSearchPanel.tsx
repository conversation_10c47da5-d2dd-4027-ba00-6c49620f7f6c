import SearchInput from '@/components/shared/SearchInput/SearchInput';
import { TreeContext } from '@/context/TreeContext';
import { useTranslation } from 'react-i18next';
import { useContext, useEffect, useRef, useState } from 'react';
import { fetchVersionDetails } from '@/services/api/index';
import { ComponentDetails, TreeNodeType } from '@/utils/types';
import { useNavigate } from 'react-router-dom';
import ComponentTypeIcon from '@/components/Tree/TreeNodeComponentIcon/ComponentTypeIcon';
import { ROUTES } from '@/constants/routes';
import { VersionDetailsCurrentPage } from '@/components/VersionDetails/types';
import { useComponentSearch } from '@/hooks/queries/useComponentSearch';
import { Features, isFeatureActive } from '@/utils/featureFlags';

interface TreeSearchPanelProps {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
}

/**
 * TreeSearchPanel - A component that provides search functionality for the tree
 * Displays search results in a dropdown and handles navigation when selecting results
 */
const TreeSearchPanel: React.FC<TreeSearchPanelProps> = ({ searchTerm, setSearchTerm }) => {
  const { t } = useTranslation();
  const treeContext = useContext(TreeContext);
  const [showDropdown, setShowDropdown] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ left: 0, top: 0, width: 0 });
  const searchContainerRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  if (!treeContext) {
    throw new Error('TreeContext must be used within a TreeProvider');
  }

  const {
    isTreeVisible,
    setSelectedNode,
    setSelectedDetails,
    handleComponentSelect,
    setCurrentDetailsPage,
    updateTreeWithDirectory,
  } = treeContext;

  // Update dropdown position whenever it's shown
  useEffect(() => {
    if (showDropdown && searchContainerRef.current) {
      const rect = searchContainerRef.current.getBoundingClientRect();
      setDropdownPosition({
        left: rect.left,
        top: rect.bottom + window.scrollY,
        width: rect.width,
      });
    }
  }, [showDropdown]);

  useEffect(() => {
    // Close dropdown when clicking outside
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchContainerRef.current &&
        !searchContainerRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Show dropdown when input is focused and searchTerm is not empty
  const handleSearchRequest = () => {
    if (searchTerm) {
      setShowDropdown(true);
    }
  };

  // Show dropdown whenever searchTerm is non-empty
  useEffect(() => {
    if (searchTerm) {
      setShowDropdown(true);
    } else {
      setShowDropdown(false);
    }
  }, [searchTerm]);

  // Use the React Query hook for search
  const {
    data: searchResults = [],
    isLoading: isSearching,
    refetch: executeSearch,
    error,
  } = useComponentSearch(searchTerm, {
    enabled: false, // Don't run automatically on every search term change
  });

  // This effect runs when searchTerm changes
  useEffect(() => {
    // Create a timeout even if searchTerm is empty to ensure proper cleanup
    const delaySearch = setTimeout(() => {
      if (searchTerm) {
        executeSearch();
      }
    }, 300); // Debounce search for 300ms

    // Always return cleanup function
    return () => clearTimeout(delaySearch);
  }, [searchTerm, executeSearch]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    if (!value) {
      setShowDropdown(false);
    }
  };

  // Handler for component selection
  const handleComponentClick = (result: TreeNodeType) => {
    // Close the dropdown and clear search first
    setShowDropdown(false);
    setSearchTerm('');

    // Update the context immediately with the selected node
    if (setSelectedNode) {
      setSelectedNode(result);
    }

    // Use the path or create one
    const path = result.path || [result.id];

    // Navigate to tree view first to avoid flash
    navigate(ROUTES.TREE);

    // Get the parent directory ID for components to rebuild tree with parent as root
    const parentId = result.parentId;

    if (parentId && updateTreeWithDirectory) {
      // For components, first rebuild the tree with the parent directory as root
      const parentDirectoryInfo = {
        id: parentId,
        name: 'Parent Directory', // This will be updated when the API fetches the actual name
        type: 'Directory',
        path: path.slice(0, -1), // Remove the component ID from the path
        description: '',
        componentType: null,
      };

      // Update the tree with parent directory as root, then select the component
      updateTreeWithDirectory(parentId, parentDirectoryInfo)
        .then(() => {
          // After tree is rebuilt, fetch component details and select it
          fetchVersionDetails(result.id)
            .then(response => {
              // Create component details
              const componentDetails: ComponentDetails = {
                name: result.name,
                id: result.id,
                componentType: result.componentType,
                versions: response.versions, // Access versions from the response
                jobs: [],
                localVersion: null,
                backupJobConfigured: false,
                masterData: [],
              };

              // Use handleComponentSelect to update both details and path
              if (handleComponentSelect) {
                handleComponentSelect(componentDetails, path);
              }

              // Set current page to ChangeHistory
              if (setCurrentDetailsPage) {
                setCurrentDetailsPage(VersionDetailsCurrentPage.ChangeHistory);
              }
            })
            .catch(error => {
              // Error handling is handled by the query client
            });
        })
        .catch(error => {
          // Error handling is handled by the query client
        });
    } else {
      // Fallback to old behavior if no parent ID or updateTreeWithDirectory not available
      fetchVersionDetails(result.id)
        .then(response => {
          // Create component details
          const componentDetails: ComponentDetails = {
            name: result.name,
            id: result.id,
            componentType: result.componentType,
            versions: response.versions, // Access versions from the response
            jobs: [],
            localVersion: null,
            backupJobConfigured: false,
            masterData: [],
          };

          // Use handleComponentSelect which will update both selectedDetails and selectedPath
          if (handleComponentSelect) {
            handleComponentSelect(componentDetails, path);
          }

          // Set current page to ChangeHistory
          if (setCurrentDetailsPage) {
            setCurrentDetailsPage(VersionDetailsCurrentPage.ChangeHistory);
          }
        })
        .catch(error => {
          // Error handling is handled by the query client
        });
    }
  };

  // Handler for directory selection
  const handleDirectoryClick = (result: TreeNodeType) => {
    // Close the dropdown and clear search first
    setShowDropdown(false);
    setSearchTerm('');

    // Update the context immediately with the selected node
    if (setSelectedNode) {
      setSelectedNode(result);
    }

    // Use the path or create one
    const path = result.path || [result.id];

    // Navigate to tree view first to avoid flash
    navigate(ROUTES.TREE);

    // For directories, update the tree to make this directory the root
    if (updateTreeWithDirectory) {
      // Pass the directory information to avoid an extra API call
      const directoryInfo = {
        id: result.id,
        name: result.name,
        type: result.type,
        path: path,
        description: '',
        componentType: null,
      };

      // Update the tree with the directory as root AFTER navigation
      // This prevents any flash/flicker during the transition
      requestAnimationFrame(() => {
        updateTreeWithDirectory(result.id, directoryInfo)
          .then(() => {
            // Set the selected path after tree is rebuilt
            if (treeContext.setSelectedPath) {
              treeContext.setSelectedPath(path);
            }
          })
          .catch(() => {});
      });
    }
  };

  // Render dropdown directly in the component instead of using a portal
  const renderDropdown = () => {
    // Don't show dropdown if it's not supposed to be visible
    if (!showDropdown) return null;

    if (error) {
      return (
        <div className="px-4 py-3 text-center text-red-500">
          <div className="flex items-center justify-center mb-1">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              className="h-5 w-5 mr-1"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span>
              {typeof error === 'string'
                ? error
                : error instanceof Error
                  ? error.message
                  : 'Error searching'}
            </span>
          </div>
        </div>
      );
    }

    // Group search results by type
    const componentResults = searchResults.filter(result => result.type === 'Component');
    const directoryResults = searchResults.filter(result => result.type !== 'Component');

    // Show dropdown with results or no results message
    return (
      <div
        className="shadow-lg rounded-md border border-border-color max-h-80 overflow-y-auto bg-background fixed z-[999]"
        style={{
          left: `${dropdownPosition.left}px`,
          top: `${dropdownPosition.top}px`,
          width: `${dropdownPosition.width}px`,
        }}
      >
        {error ? (
          <div className="px-4 py-3 text-center text-gray-500">
            <div className="flex items-center justify-center w-full text-red-500 p-4">
              {(error as { message?: string })?.message || 'Error loading tree data'}
            </div>
          </div>
        ) : searchResults.length > 0 ? (
          <div className="py-1">
            {/* Components Section */}
            {componentResults.length > 0 && (
              <>
                <div className="px-4 py-2 bg-gray-100 font-medium text-gray-700">Components</div>
                {componentResults.map(result => (
                  <div
                    key={result.id}
                    className="px-4 py-2 hover:bg-hover cursor-pointer pl-6"
                    onClick={() => {
                      // Close the dropdown and clear search first
                      setShowDropdown(false);
                      setSearchTerm('');

                      // Update the context immediately with the selected node
                      if (setSelectedNode) {
                        setSelectedNode(result);
                      }

                      // Use the path or create one
                      const path = result.path || [result.id];

                      // Fetch versions and update context
                      fetchVersionDetails(result.id)
                        .then(response => {
                          // Create component details
                          const componentDetails: ComponentDetails = {
                            name: result.name,
                            id: result.id,
                            componentType: result.componentType,
                            versions: response.versions, // Use the versions array from the response
                            jobs: [],
                            localVersion: null,
                            backupJobConfigured: false,
                            masterData: [],
                          };

                          // Update context with details
                          if (setSelectedDetails) {
                            setSelectedDetails(componentDetails);
                          }

                          // Update path
                          if (treeContext.setSelectedPath) {
                            treeContext.setSelectedPath(path);
                          }

                          // Use the handleComponentSelect function
                          if (handleComponentSelect) {
                            handleComponentSelect(componentDetails, path);
                          }

                          // Set current page to ChangeHistory
                          if (setCurrentDetailsPage) {
                            setCurrentDetailsPage(VersionDetailsCurrentPage.ChangeHistory);
                          }
                        })
                        .catch(() => {});

                      // Navigate to tree view
                      navigate(ROUTES.TREE);
                    }}
                  >
                    <div className="flex items-center">
                      <div className="w-6 h-6 mr-2 flex-shrink-0 flex items-center justify-center">
                        {result.componentType && result.componentType.icon ? (
                          <ComponentTypeIcon data={result.componentType.icon} size={4} />
                        ) : (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                            className="h-5 w-5"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>
                        )}
                      </div>
                      <div className="flex flex-col">
                        <span className="font-medium">{result.name}</span>
                        <span className="text-tiny text-gray-500">{result.displayPath}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </>
            )}

            {/* Directories Section */}
            {directoryResults.length > 0 && (
              <>
                <div className="px-4 py-2 bg-gray-100 font-medium text-gray-700">Directories</div>
                {directoryResults.map(result => (
                  <div
                    key={result.id}
                    className="px-4 py-2 hover:bg-hover cursor-pointer pl-6"
                    onClick={() => handleDirectoryClick(result)}
                  >
                    <div className="flex items-center">
                      <div className="w-6 h-6 mr-2 flex-shrink-0 flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          className="h-5 w-5"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
                          />
                        </svg>
                      </div>
                      <div className="flex flex-col">
                        <span className="font-medium">{result.name}</span>
                        <span className="text-tiny text-gray-500">{result.displayPath}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </>
            )}
          </div>
        ) : (
          <div className="px-4 py-3 text-center text-gray-500">
            <div className="flex items-center justify-center mb-1">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                className="h-5 w-5 mr-1 text-gray-400"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span>No results found</span>
            </div>
            <div className="text-tiny">Try a different search term</div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`flex-grow-0 overflow-visible ${isTreeVisible ? '' : 'hidden'}`}>
      <div className={'text-2xl mr-large h-[60px] mt-8 uppercase'}>
        <span className={'font-bold'}>{t('tree.project')}</span>&nbsp;
        {t('tree.tree')}
      </div>
      <div className={'pt-medium pb-medium mr-large relative'} ref={searchContainerRef}>
        {isFeatureActive(Features.TreeSearch) && (
          <SearchInput
            value={searchTerm}
            onSearch={setSearchTerm}
            placeholder={t('tree.searchPlaceholder')}
            onDropdownClick={handleSearchRequest}
          />
        )}
        {/* Render dropdown directly */}
        {renderDropdown()}
      </div>
    </div>
  );
};

export default TreeSearchPanel;
