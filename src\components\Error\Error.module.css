@import url('https://fonts.googleapis.com/css2?family=Titillium+Web:wght@400;700&display=swap');

body,
html {
  margin: 0;
  padding: 0;
  height: 100%;
}
.loginContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh; /* Full viewport height */
  background-color: #1f4a8e; /* Background color */
}

.title {
  color: white;
  font-size: var(--font-xxlarge);
  margin-bottom: 20px;
  text-align: center;
}

.buttonContainer {
  display: flex;
  flex-direction: column;
  align-items: center; /* Center items horizontally */
  width: 100%; /* Full width */
}

.button {
  padding: 15px 30px;
  font-size: var(--font-medium);
  color: #1f4a8e;
  background-color: white;
  border: none;
  cursor: pointer;
  align-self: center;
  font-family: inherit; /* Inherit the font-family from the parent */
  transition: background-color 0.3s;
}

.logoText {
  font-weight: 100;
  text-align: center;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .loginContainer {
    padding: 10rem 2rem;
  }

  .title {
    font-size: var(--font-xlarge);
    text-align: center;
  }

  .button {
    padding: 10px 20px;
    font-size: var(--font-medium);
  }
}

@media (max-width: 480px) {
  .loginContainer {
    padding: 8rem 1rem;
  }

  .title {
    font-size: var(--font-large);
  }

  .button {
    padding: 8px 16px;
    font-size: var(--font-small);
  }
}
