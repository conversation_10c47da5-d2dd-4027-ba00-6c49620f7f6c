#!/bin/bash

# Simple nginx deployment script for Octoplant Web Client
set -e

echo "🚀 Starting simple nginx deployment for Octoplant Web Client..."

# Configuration
WEB_DIR="/var/www/octoplant-web"
NGINX_SITE="octoplant-web"
BUILD_DIR="build"

# Check if running as root or with sudo
if [ "$EUID" -ne 0 ]; then
    echo "❌ Please run this script with sudo"
    exit 1
fi

# Step 1: Install nginx if not installed
if ! command -v nginx &> /dev/null; then
    echo "📦 Installing nginx..."
    apt update
    apt install -y nginx
    systemctl enable nginx
    systemctl start nginx
    echo "✅ nginx installed and started"
else
    echo "✅ nginx is already installed"
fi

# Step 2: Create web directory
echo "📁 Creating web directory..."
mkdir -p $WEB_DIR
echo "✅ Web directory created: $WEB_DIR"

# Step 3: Build the application
echo "🔨 Building the application..."
if [ ! -f "package.json" ]; then
    echo "❌ package.json not found. Please run this script from the project root directory."
    exit 1
fi

# Install dependencies and build
npm install
npm run build

if [ ! -d "$BUILD_DIR" ]; then
    echo "❌ Build directory not found. Build may have failed."
    exit 1
fi

echo "✅ Application built successfully"

# Step 4: Deploy files
echo "📋 Deploying files..."
rm -rf $WEB_DIR/*
cp -r $BUILD_DIR/* $WEB_DIR/

# Set proper permissions
chown -R www-data:www-data $WEB_DIR
find $WEB_DIR -type f -exec chmod 644 {} \;
find $WEB_DIR -type d -exec chmod 755 {} \;

echo "✅ Files deployed to $WEB_DIR"

# Step 5: Configure nginx
echo "⚙️  Configuring nginx..."

# Copy nginx configuration
cp nginx-simple.conf /etc/nginx/sites-available/$NGINX_SITE

# Enable the site
if [ ! -L "/etc/nginx/sites-enabled/$NGINX_SITE" ]; then
    ln -s /etc/nginx/sites-available/$NGINX_SITE /etc/nginx/sites-enabled/
    echo "✅ nginx site enabled"
else
    echo "✅ nginx site already enabled"
fi

# Disable default site if it exists
if [ -L "/etc/nginx/sites-enabled/default" ]; then
    rm /etc/nginx/sites-enabled/default
    echo "✅ Default nginx site disabled"
fi

# Test nginx configuration
echo "🧪 Testing nginx configuration..."
if nginx -t; then
    echo "✅ nginx configuration is valid"
else
    echo "❌ nginx configuration test failed"
    exit 1
fi

# Step 6: Restart nginx
echo "🔄 Restarting nginx..."
systemctl restart nginx

if systemctl is-active --quiet nginx; then
    echo "✅ nginx restarted successfully"
else
    echo "❌ nginx failed to start"
    systemctl status nginx
    exit 1
fi

# Step 7: Show status
echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📊 Status:"
echo "  - Web directory: $WEB_DIR"
echo "  - nginx configuration: /etc/nginx/sites-available/$NGINX_SITE"
echo "  - Application URL: http://localhost (or your server IP)"
echo ""
echo "🔧 Next steps:"
echo "  1. Update server_name in nginx config if using a domain"
echo "  2. Configure SSL certificate for HTTPS"
echo "  3. Update backend API URLs if they're different"
echo ""
echo "📝 Useful commands:"
echo "  - Check nginx status: sudo systemctl status nginx"
echo "  - View nginx logs: sudo tail -f /var/log/nginx/octoplant-web.access.log"
echo "  - Test nginx config: sudo nginx -t"
echo "  - Reload nginx: sudo systemctl reload nginx"
