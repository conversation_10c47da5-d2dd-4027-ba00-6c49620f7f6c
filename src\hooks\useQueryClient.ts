import { QueryClient, QueryCache } from '@tanstack/react-query';

// Extracted constants for configuration
const STALE_TIME = 5 * 60 * 1000; // 5 minutes
const CACHE_TIME = 10 * 60 * 1000; // 10 minutes
const RETRY_COUNT = 1;

// Cache size management constants
const MAX_DIRECTORY_CHILDREN_QUERIES = 50;
const PRUNE_BATCH_SIZE = 5;

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: true,
      retry: RETRY_COUNT,
      staleTime: STALE_TIME,
      gcTime: CACHE_TIME,
      refetchOnMount: true,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
      onError: err => {
        console.error('Query error:', err);
      },
    },
    mutations: {
      retry: false,
      onError: err => {
        console.error('Mutation error:', err);
      },
    },
  },
  queryCache: new QueryCache({
    onSuccess: (data, query) => {
      // Only manage directory children cache size
      if (Array.isArray(query.queryKey) && query.queryKey[0] === 'directoryChildren') {
        // Get all directory children queries
        const queriesData = queryClient.getQueriesData({ queryKey: ['directoryChildren'] });

        if (queriesData.length > MAX_DIRECTORY_CHILDREN_QUERIES) {
          console.log(
            `[Cache] Pruning directory children cache: ${queriesData.length}/${MAX_DIRECTORY_CHILDREN_QUERIES}`
          );

          // Find oldest queries based on dataUpdatedAt timestamp
          const queriesToRemove = queriesData
            .map(([queryKey, _]) => ({
              queryKey,
              updatedAt: queryClient.getQueryState(queryKey)?.dataUpdatedAt || 0,
            }))
            .sort((a, b) => a.updatedAt - b.updatedAt)
            .slice(0, PRUNE_BATCH_SIZE);

          // Remove oldest queries
          queriesToRemove.forEach(({ queryKey }) => {
            console.log(`[Cache] Removing query: ${JSON.stringify(queryKey)}`);
            queryClient.removeQueries({ queryKey });
          });
        }
      }
    },
  }),
});
