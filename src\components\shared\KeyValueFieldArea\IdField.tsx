import { CopyButton } from '../ToolButton/CopyButton';
import FieldLabel from './FieldLabel';

// Reusable component for ID fields with copy button
interface IdFieldProps {
  label: string;
  value: string;
  icon?: string;
  onCopy: () => string;
}

const IdField: React.FC<IdFieldProps> = ({ label, value, icon, onCopy }) => (
  <div className="flex gap-2">
    <FieldLabel label={label} icon={icon} />
    <div className="flex gap-2 mb-1">
      <span>{value}</span>
      <CopyButton size={7} handleCopy={onCopy} />
    </div>
  </div>
);

export default IdField;
