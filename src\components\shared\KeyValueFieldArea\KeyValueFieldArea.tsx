import IdField from './IdField';
import CompareResultField from './CompareResultField';
import LargeTextField from './LargeTextField';
import FieldLabel from './FieldLabel';
import { t } from 'i18next';

// Reusable Field component for data fields

export enum FieldType {
  field,
  idField,
  compareResultField,
  largeTextField,
}

export interface FieldProps {
  label: string;
  icon?: string;
  value: any;
  fieldType?: FieldType;
}

const Field: React.FC<FieldProps> = ({ label, value, fieldType = FieldType.field, icon }) => {
  switch (fieldType) {
    case FieldType.field:
    default:
      return (
        <div className="flex gap-2">
          <FieldLabel label={label} icon={icon} />
          <div className="mb-1">{value ? value : '\u00a0'}</div>
        </div>
      );
    case FieldType.idField:
      return <IdField label={label} value={value} onCopy={() => value || ''} />;
    case FieldType.compareResultField:
      return <CompareResultField label={label} value={value} />;
    case FieldType.largeTextField:
      return <LargeTextField label={label} value={value} />;
  }
};

interface KeyValueFieldAreaProps {
  fields: FieldProps[];
  columnCount?: number;
}

const KeyValueFieldArea: React.FC<KeyValueFieldAreaProps> = ({ fields, columnCount = 4 }) => {
  return (
    <div className="border p-4 rounded">
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-${columnCount} gap-4`}>
        {fields.map(
          (field, index) =>
            field.value != undefined && (
              <Field
                key={index}
                label={field.label}
                icon={field.icon}
                value={field.value || t('common.notAvailable')}
                fieldType={field.fieldType}
              />
            )
        )}
      </div>
    </div>
  );
};

export default KeyValueFieldArea;
