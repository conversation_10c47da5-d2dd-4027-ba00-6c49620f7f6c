import { storageClient } from '../httpClient';

/**
 * Downloads a file for a specific component version
 * @param componentId - The ID of the component
 * @param version - The version number
 * @returns Promise with the blob data
 */
export const downloadVersionFile = async (componentId: string, version: string): Promise<Blob> => {
  if (!componentId || !version) {
    throw new Error('Component ID and version are required for downloading a file');
  }

  const path = `download/${componentId}/${version}`;
  const response = await storageClient.get(path, {
    responseType: 'blob',
    headers: {
      Accept: 'application/octet-stream',
      'Content-Type': 'application/json',
    },
  });

  return new Blob([response.data], { type: 'application/octet-stream' });
};

/**
 * Initiates a download of a blob as a file
 * @param blob - The blob data to download
 * @param filename - The name to give the downloaded file
 */
export const downloadBlobAsFile = (blob: Blob, filename: string): void => {
  if (!(blob instanceof Blob)) {
    throw new Error('Invalid blob object provided for download');
  }

  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename || 'download.zip';
  link.style.display = 'none';

  document.body.appendChild(link);
  link.click();

  setTimeout(() => {
    if (document.body.contains(link)) {
      document.body.removeChild(link);
    }
    window.URL.revokeObjectURL(url);
  }, 100);
};
