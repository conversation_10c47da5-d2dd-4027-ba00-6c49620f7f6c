import React from 'react';
import { Header } from '@/components';
import { TreeContext } from '@/context/TreeContext';
import { useAuth } from '@/context/AuthContext';

interface MainLayoutProps {
  children?: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const { isLoading } = useAuth();
  const treeContext = React.useContext(TreeContext);

  return (
    <div className="flex flex-col h-screen">
      {/* Header always renders immediately */}
      <Header />

      <div className="flex-grow flex flex-col">
        {/* Show welcome content during authentication */}
        {isLoading ? (
          <div className="flex-grow flex-center">
            <div className="text-center">
              <h1 className="text-2xl font-semibold mb-4">Welcome to Octoplant</h1>
              <p className="text-border-color-dark">Loading your workspace...</p>
            </div>
          </div>
        ) : (
          children
        )}
      </div>
    </div>
  );
};

export default MainLayout;
