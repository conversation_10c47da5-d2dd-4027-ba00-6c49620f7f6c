import { GenericTable, HeaderCell } from '@/components/shared/GenericTable/GenericTable';
import { Job, JobResult } from '@/utils/types';
import { t } from 'i18next';
import { useCallback, useState } from 'react';
import EventLogTable from '../EventLogTable/EventLogTable';
import { CompareResultIcon } from '@/components/shared/CompareResultIcon/CompareResultIcon';
import React from 'react';
import { DateFormatOptions, DateToString } from '@/utils/dateTime';
import { HLineWithIcon } from '@/components/shared/HLine/HLine';
import LatestResultsIcon from '@/assets/svgs/dark/results.svg';
import LastStartedIcon from '@/assets/svgs/dark/last-started.svg';
import BackupIcon from '@/assets/svgs/dark/backup.svg';
import VersionIcon from '@/assets/svgs/dark/version.svg';
import LocationIcon from '@/assets/svgs/dark/location.svg';
import CloseIcon from '@/assets/svgs/dark/angle-up.svg';
import { PagingButtons } from '@/components/shared/PagingButtons/PagingButtons';
import { useJobResults } from '@/hooks/queries/useJobResults';
import Loader from '@/components/shared/Loader/Loader';

const jobResultsPageSize = 5;

interface EventLogTableFrameProps {
  result?: JobResult;
  hideClick: () => void;
}

const EventLogTableFrame: React.FC<EventLogTableFrameProps> = ({ result, hideClick }) => {
  return (
    <div>
      <HLineWithIcon icon={CloseIcon} clickHandler={hideClick} />
      {result && <EventLogTable result={result} />}
    </div>
  );
};

interface JobResultsTableProps {
  job: Job;
}

const JobResultsTable: React.FC<JobResultsTableProps> = ({ job }) => {
  const [eventLogVisible, setEventLogVisible] = useState<boolean>(false);
  const [selectedJobResult, setSelectedJobResult] = useState<JobResult>();
  const [pageNumber, setPageNumber] = useState<number>(1);

  // Safety check - ensure valid job ID
  const validJobId = job.id && typeof job.id === 'string' ? job.id : '';
  const isEnabled = validJobId.trim().length > 0;

  const {
    data: resultsData,
    isLoading,
    error,
  } = useJobResults(validJobId ?? null, {
    pageNumber: pageNumber,
    pageSize: jobResultsPageSize,
    enabled: isEnabled,
  });

  const hideEventLog = useCallback(() => {
    setEventLogVisible(false);
  }, []);

  const handleRowClick = useCallback((result: JobResult) => {
    setSelectedJobResult(result);
    setEventLogVisible(true);
  }, []);

  const dataAvailable = !isLoading && !error && resultsData;
  const totalPages = dataAvailable ? Math.ceil(resultsData.totalRecords / jobResultsPageSize) : 0;

  const columns = [
    {
      header: () => <HeaderCell title={t('jobs.results')} icon={LatestResultsIcon} />,
      id: 'latestResults',
      cell: (info: any) => {
        const result = info.row.original;
        return (
          <div className={'flex items-left gap-medium'}>
            <CompareResultIcon size={6} compareResult={result.result.versionVsBackup} />
            <CompareResultIcon size={6} compareResult={result.result.backupVsBackup} />
          </div>
        );
      },
    },
    {
      header: () => <HeaderCell title={t('jobs.executedOn')} icon={LastStartedIcon} />,
      id: 'executedOn',
      cell: (info: any) => {
        return DateToString(info.row.original.timestamp);
      },
    },
    {
      header: () => <HeaderCell title={t('jobs.backup')} icon={BackupIcon} />,
      accessorKey: 'backup',
    },
    {
      header: () => <HeaderCell title={t('jobs.location')} icon={LocationIcon} />,
      accessorKey: 'location',
    },
    {
      header: () => <HeaderCell title={t('version.version')} icon={VersionIcon} />,
      accessorKey: 'version',
    },
  ];

  return (
    <div className="min-h-[20vh] min-w-[37vw]">
      {dataAvailable && (
        <GenericTable
          className="max-h-[23vh] scroll-box"
          data={resultsData.results}
          columns={columns}
          onRowClick={handleRowClick}
          highlightCurrentRow={true}
        />
      )}
      {!dataAvailable && !isLoading && <div>{t('jobs.noJobResults')}</div>}
      {isLoading && <Loader />}
      {eventLogVisible && (
        <EventLogTableFrame result={selectedJobResult} hideClick={hideEventLog} />
      )}
      <PagingButtons
        buttons={3}
        currentPage={pageNumber}
        pages={totalPages}
        clickHandler={setPageNumber}
      />
    </div>
  );
};
export default JobResultsTable;
