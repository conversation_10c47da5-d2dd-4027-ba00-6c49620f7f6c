// Extracting the common types to be reused across the app

import { ComponentDetails } from '@/utils/types';
import CompareResult from '@/utils/compareResult';

export interface ComponentDetailsProps {
  details: ComponentDetails;
}

export interface EditStateProps {
  checkInPossible: boolean;
  time: Date;
}

export interface VersionBoxProps {
  versionAvailable: boolean;
  versionNumber: number;
  time: Date;
  className: string;
  image: string;
}

export interface BackupBoxProps {
  serverVsBackup: CompareResult;
  backupVsPreviousBackup: CompareResult;
  backupJobConfigured: boolean;
}
