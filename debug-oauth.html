<!DOCTYPE html>
<html>
<head>
    <title>OAuth Debug Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        button { padding: 10px 15px; margin: 5px; }
    </style>
</head>
<body>
    <h1>OAuth Configuration Debug Tool</h1>
    
    <div class="section">
        <h2>Environment Check</h2>
        <div id="env-check"></div>
    </div>
    
    <div class="section">
        <h2>OAuth Endpoints Test</h2>
        <button onclick="testOAuthEndpoints()">Test OAuth Endpoints</button>
        <div id="oauth-test"></div>
    </div>
    
    <div class="section">
        <h2>API Endpoints Test</h2>
        <button onclick="testApiEndpoints()">Test API Endpoints</button>
        <div id="api-test"></div>
    </div>
    
    <div class="section">
        <h2>Current Configuration</h2>
        <div id="config-display"></div>
    </div>

    <script>
        // Display environment and configuration
        function displayConfig() {
            const config = {
                'Current URL': window.location.href,
                'Origin': window.location.origin,
                'Expected OAuth Base': `${window.location.origin}/oauth/`,
                'Expected API Base': `${window.location.origin}/api/`,
                'User Agent': navigator.userAgent
            };
            
            document.getElementById('config-display').innerHTML = 
                '<pre>' + JSON.stringify(config, null, 2) + '</pre>';
        }
        
        // Test OAuth endpoints
        async function testOAuthEndpoints() {
            const results = document.getElementById('oauth-test');
            results.innerHTML = '<p>Testing OAuth endpoints...</p>';
            
            const tests = [
                { name: 'OAuth Discovery', url: '/oauth/.well-known/openid_configuration' },
                { name: 'OAuth Authorize', url: '/oauth/v1/oauth2/authorize' },
                { name: 'OAuth Token', url: '/oauth/v1/oauth2/token' }
            ];
            
            let html = '';
            
            for (const test of tests) {
                try {
                    const response = await fetch(test.url, { method: 'HEAD' });
                    const status = response.status;
                    const statusClass = status < 400 ? 'success' : 'error';
                    html += `<p class="${statusClass}">${test.name}: ${status} ${response.statusText}</p>`;
                } catch (error) {
                    html += `<p class="error">${test.name}: ${error.message}</p>`;
                }
            }
            
            results.innerHTML = html;
        }
        
        // Test API endpoints
        async function testApiEndpoints() {
            const results = document.getElementById('api-test');
            results.innerHTML = '<p>Testing API endpoints...</p>';
            
            const tests = [
                { name: 'API Health', url: '/api/health' },
                { name: 'API Root', url: '/api/' }
            ];
            
            let html = '';
            
            for (const test of tests) {
                try {
                    const response = await fetch(test.url, { method: 'HEAD' });
                    const status = response.status;
                    const statusClass = status < 400 ? 'success' : 'error';
                    html += `<p class="${statusClass}">${test.name}: ${status} ${response.statusText}</p>`;
                } catch (error) {
                    html += `<p class="error">${test.name}: ${error.message}</p>`;
                }
            }
            
            results.innerHTML = html;
        }
        
        // Environment check
        function checkEnvironment() {
            const envDiv = document.getElementById('env-check');
            let html = '';
            
            // Check if we're running on the expected domain
            if (window.location.hostname === 'localhost') {
                html += '<p class="success">✓ Running on localhost</p>';
            } else {
                html += `<p class="warning">⚠ Running on ${window.location.hostname} (expected localhost)</p>`;
            }
            
            // Check if we're running on HTTP
            if (window.location.protocol === 'http:') {
                html += '<p class="success">✓ Using HTTP protocol</p>';
            } else {
                html += `<p class="warning">⚠ Using ${window.location.protocol} (expected http:)</p>`;
            }
            
            // Check port
            const port = window.location.port;
            if (port === '80' || port === '') {
                html += '<p class="success">✓ Using port 80</p>';
            } else {
                html += `<p class="warning">⚠ Using port ${port} (expected 80)</p>`;
            }
            
            envDiv.innerHTML = html;
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            displayConfig();
            checkEnvironment();
        });
    </script>
</body>
</html>
