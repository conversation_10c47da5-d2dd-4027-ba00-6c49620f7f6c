import { t } from 'i18next';
import ToolButton from './ToolButton';
import openFolderSVG from '@/assets/svgs/dark/folder-open.svg';

interface SelectDirectoryButtonProps {
  callback: (selectedDirectory: string) => void;
  size?: number;
}

const SelectDirectoryButton: React.FC<SelectDirectoryButtonProps> = ({ callback, size }) => {
  const handleFileSelect = (e: React.MouseEvent) => {
    e.preventDefault();
    /*ToDo: I am lost here*/
    callback('Selected Directory');
  };

  return (
    <ToolButton
      size={size}
      text={t('common.select')}
      onClick={handleFileSelect}
      icon={openFolderSVG}
    />
  );
};

export default SelectDirectoryButton;
