import { useCallback, useContext } from 'react';
import { TreeContext } from '@/context/TreeContext';

/**
 * Custom hook for working with tree nodes
 * Provides a centralized way to access and manipulate tree node state
 *
 * @param nodeId - The ID of the node
 * @returns Object with node state and methods
 */
export function useTreeNode(nodeId: string) {
  const treeContext = useContext(TreeContext);

  if (!treeContext) {
    throw new Error('useTreeNode must be used within a TreeProvider');
  }

  // Get expanded state from context
  const isExpanded = treeContext.isNodeExpanded(nodeId);

  // Create a memoized toggle function
  const toggleExpand = useCallback(() => {
    if (process.env.NODE_ENV !== 'production') {
      console.log(`[useTreeNode] Toggling node ${nodeId}, current state:`, isExpanded);
    }
    treeContext.toggleNodeExpansion(nodeId);
  }, [nodeId, isExpanded, treeContext]);

  // Create a memoized expand function
  const expand = useCallback(() => {
    if (!isExpanded) {
      if (process.env.NODE_ENV !== 'production') {
        console.log(`[useTreeNode] Expanding node ${nodeId}`);
      }
      treeContext.expandNode(nodeId);
    }
  }, [nodeId, isExpanded, treeContext]);

  // Create a memoized collapse function
  const collapse = useCallback(() => {
    if (isExpanded) {
      if (process.env.NODE_ENV !== 'production') {
        console.log(`[useTreeNode] Collapsing node ${nodeId}`);
      }
      treeContext.collapseNode(nodeId);
    }
  }, [nodeId, isExpanded, treeContext]);

  return {
    isExpanded,
    toggleExpand,
    expand,
    collapse,
  };
}
