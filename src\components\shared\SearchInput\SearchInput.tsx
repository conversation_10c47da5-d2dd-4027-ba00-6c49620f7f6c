import React from 'react';

interface SearchInputProps {
  placeholder?: string;
  onSearch?: (value: string) => void;
  value?: string;
  onDropdownClick?: () => void;
}

const SearchInput: React.FC<SearchInputProps> = ({
  placeholder,
  onSearch,
  value,
  onDropdownClick,
}) => {
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (onSearch) {
      onSearch(event.target.value);
    }
  };

  return (
    <div className="flex items-center rounded-br-sm relative w-full h-10 text-border-color-dark placeholder:text-border-color-dark bg-inherit">
      <span className="h-full border-l-4 border-primary" />
      <input
        type="text"
        className="grow bg-inherit outline-0 text-medium p-small ml-small uppercase border-b-2 border-border-color m-0 border-0 h-full"
        placeholder={placeholder}
        onChange={handleInputChange}
        value={value}
      />
      <button
        className="h-full w-10 bg-border-color flex-center rounded-tr-sm rounded-br-sm hover:bg-hover"
        onClick={onDropdownClick}
      >
        <svg
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
          className="w-5 h-5 fill-none stroke-border-color-dark"
        >
          <path d="M7 10l5 5 5-5" className="stroke-background" fill="none" strokeWidth="2" />
        </svg>
      </button>
    </div>
  );
};

export default SearchInput;
