import { useQuery } from '@tanstack/react-query';
import { compareVersions } from '@/services/api/index';

export const useVersionComparison = (baseVersionId: string, compareVersionId: string) => {
  const {
    data: comparison,
    isLoading,
    error,
  } = useQuery(
    ['versionComparison', baseVersionId, compareVersionId],
    () => compareVersions(baseVersionId, compareVersionId),
    {
      enabled: !!baseVersionId && !!compareVersionId,
      staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
      cacheTime: 30 * 60 * 1000, // Cache for 30 minutes
      retry: 1, // Only retry once as comparison can be expensive
    }
  );

  return {
    comparison,
    isLoading,
    error,
  };
};
