# Simple nginx Deployment Guide for Octoplant Web Client

This guide provides a quick and simple way to deploy your React application on nginx.

## Quick Start

### Prerequisites
- Ubuntu/Debian server (or any Linux with nginx support)
- Node.js 18+ installed
- sudo access

### 1. Setup Environment
```bash
# Make setup script executable
chmod +x setup-environment.sh

# Run environment setup
./setup-environment.sh
```

This will prompt you for:
- OAuth server URL (default: http://localhost:64023)
- API server URL (default: http://localhost:5256)
- Storage server URL (default: http://localhost:5157)
- Domain name (default: localhost)

### 2. Deploy to nginx
```bash
# Make deployment script executable
chmod +x deploy-nginx-simple.sh

# Run deployment (requires sudo)
sudo ./deploy-nginx-simple.sh
```

### 3. Access Your Application
- Open browser and go to: `http://your-server-ip` or `http://your-domain`
- The application should load and work with your backend services

## What the Scripts Do

### setup-environment.sh
- Creates `.env.production` with your backend URLs
- Updates `nginx-simple.conf` with your domain and backend URLs
- Prepares configuration for deployment

### deploy-nginx-simple.sh
- Installs nginx (if not already installed)
- Builds your React application
- Copies built files to `/var/www/octoplant-web`
- Configures nginx with the provided configuration
- Starts/restarts nginx service

### nginx-simple.conf
- Basic nginx configuration for serving your React app
- Proxy configuration for your backend APIs:
  - `/api/v1/oauth2/` → OAuth server
  - `/api/Files/` → Storage server  
  - `/api/` → API Gateway
- SPA routing support (serves index.html for all routes)
- Basic security headers
- Static file caching

## File Structure After Deployment

```
/var/www/octoplant-web/          # Your React app files
├── index.html
├── static/
│   ├── css/
│   └── js/
├── favicon.ico
└── manifest.json

/etc/nginx/sites-available/octoplant-web    # nginx configuration
/etc/nginx/sites-enabled/octoplant-web      # symlink to above

/var/log/nginx/octoplant-web.access.log     # Access logs
/var/log/nginx/octoplant-web.error.log      # Error logs
```

## Useful Commands

### nginx Management
```bash
# Check nginx status
sudo systemctl status nginx

# Start nginx
sudo systemctl start nginx

# Stop nginx
sudo systemctl stop nginx

# Restart nginx
sudo systemctl restart nginx

# Reload configuration (without stopping)
sudo systemctl reload nginx

# Test configuration
sudo nginx -t
```

### Logs
```bash
# View access logs
sudo tail -f /var/log/nginx/octoplant-web.access.log

# View error logs
sudo tail -f /var/log/nginx/octoplant-web.error.log

# View nginx service logs
sudo journalctl -u nginx -f
```

### Troubleshooting
```bash
# Check if nginx is running
sudo systemctl is-active nginx

# Check nginx configuration syntax
sudo nginx -t

# Check which ports nginx is listening on
sudo netstat -tlnp | grep nginx

# Check if your backend services are running
curl http://localhost:5256/api/health
curl http://localhost:64023/v1/oauth2/.well-known/openid_configuration
```

## Common Issues and Solutions

### 1. nginx fails to start
- Check configuration: `sudo nginx -t`
- Check if port 80 is already in use: `sudo netstat -tlnp | grep :80`
- Check nginx error logs: `sudo journalctl -u nginx`

### 2. Application loads but API calls fail
- Check if backend services are running
- Verify backend URLs in nginx configuration
- Check nginx error logs for proxy errors

### 3. 404 errors on page refresh
- Ensure SPA routing is configured correctly in nginx
- Check that `try_files $uri $uri/ /index.html;` is present

### 4. Static files not loading
- Check file permissions: `ls -la /var/www/octoplant-web/`
- Ensure nginx has read access to files
- Check nginx error logs

## Adding HTTPS (Optional)

### Using Let's Encrypt (Recommended)
```bash
# Install certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d your-domain.com

# Test automatic renewal
sudo certbot renew --dry-run
```

### Manual SSL Certificate
1. Copy your certificate files to the server
2. Update nginx configuration to include SSL settings
3. Restart nginx

## Updating Your Application

To update your application after making changes:

```bash
# Build new version
npm run build

# Copy new files
sudo rm -rf /var/www/octoplant-web/*
sudo cp -r build/* /var/www/octoplant-web/

# Set permissions
sudo chown -R www-data:www-data /var/www/octoplant-web
sudo find /var/www/octoplant-web -type f -exec chmod 644 {} \;
sudo find /var/www/octoplant-web -type d -exec chmod 755 {} \;

# No need to restart nginx for static file updates
```

## Performance Tips

1. **Enable gzip compression** (already included in config)
2. **Use HTTP/2** by adding SSL and updating listen directive
3. **Configure caching** for static assets (already included)
4. **Monitor logs** for performance issues

## Security Considerations

1. **Use HTTPS** in production
2. **Update nginx regularly** for security patches
3. **Configure firewall** to only allow necessary ports
4. **Monitor access logs** for suspicious activity
5. **Keep backend services secure** and updated

---

**Note**: This is a simple configuration for getting started. For production use, consider additional security measures, monitoring, and backup strategies.
