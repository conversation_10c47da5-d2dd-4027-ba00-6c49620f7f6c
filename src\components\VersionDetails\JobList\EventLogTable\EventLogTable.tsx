import { GenericTable, HeaderCell } from '@/components/shared/GenericTable/GenericTable';
import { StatusIconWithText } from '@/components/shared/StatusIcon/StatusIcon';
import { DateFormatOptions, DateToString } from '@/utils/dateTime';
import { JobResult } from '@/utils/types';
import { t } from 'i18next';
import { useMemo } from 'react';

import TimeStampIcon from '@/assets/svgs/dark/timestamp.svg';
import EventTypeIcon from '@/assets/svgs/dark/event-type.svg';
import EventTextIcon from '@/assets/svgs/dark/event-text.svg';

interface EventLogTableProps {
  result: JobResult;
}

const EventLogTable: React.FC<EventLogTableProps> = ({ result }) => {
  const columns = useMemo(
    () => [
      {
        header: () => <HeaderCell title={t('events.type')} icon={EventTypeIcon} />,
        accessorKey: 'type',
        cell: (info: any) => {
          return <StatusIconWithText status={info.row.original.type} />;
        },
      },
      {
        header: () => <HeaderCell title={t('events.occured')} icon={TimeStampIcon} />,
        accessorKey: 'timnestamp',
        cell: (info: any) => {
          return DateToString(info.row.original.timestamp);
        },
      },
      {
        header: () => <HeaderCell title={t('events.message')} icon={EventTextIcon} />,
        accessorKey: 'text',
      },
    ],
    []
  );

  const dataAvailable = result.eventLog && result.eventLog.length > 0;
  return (
    <>
      {dataAvailable && (
        <GenericTable
          onRowClick={() => {}}
          className="max-h-[20vh] scroll-box"
          data={result.eventLog}
          tableIsFixed={false}
          columns={columns}
        />
      )}
      {!dataAvailable && <div>{t('jobs.noEventLog')}</div>}
    </>
  );
};

export default EventLogTable;
