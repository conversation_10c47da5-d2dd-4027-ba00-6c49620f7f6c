import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Job } from '@/utils/types';
import { useJobs } from '@/hooks/queries/useJobs';
import { JobResultsTable } from './JobResultsTable';
import { LoadingSpinner } from '@/components/shared/LoadingSpinner';
import { ErrorBoundary } from '@/components/Error/ErrorHandler';

interface JobListProps {
  componentId: string;
}

export const JobList: React.FC<JobListProps> = ({ componentId }) => {
  const { t } = useTranslation();
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const { jobs, isLoading, error, refetch } = useJobs(componentId);

  const handleRowClick = useCallback((job: Job) => {
    setSelectedJob(job);
  }, []);

  if (error) {
    return <div className="p-4 text-error">{t('jobs.error.loading')}</div>;
  }

  if (isLoading) {
    return (
      <div className="flex-center h-full">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="flex h-full flex-col gap-4">
        <div className="flex-none">
          <h2 className="text-xlarge font-semibold">{t('jobs.title')}</h2>
        </div>
        <div className="flex-1">
          <JobResultsTable
            jobs={jobs}
            selectedJobId={selectedJob?.id}
            onRowClick={handleRowClick}
          />
        </div>
      </div>
    </ErrorBoundary>
  );
};
