import { useContext } from 'react';
import { TreeContext } from '../../context/TreeContext';
import { LockState } from '@/utils/types';
import LockStateMenu from './LockStateMenu';
import { Features, isFeatureActive } from '@/utils/featureFlags';
import ComponentTypeIcon from '../Tree/TreeNodeComponentIcon/ComponentTypeIcon';

const ComponentHeader: React.FC = () => {
  const treeContext = useContext(TreeContext);
  if (!treeContext) {
    throw new Error('TreeDetails must be used within a TreeProvider');
  }

  const { selectedDetails } = treeContext;

  return (
    <div className="grid grid-cols-2 ml-2 mt-4 mb-small">
      <div>
        <span className="flex text-xxlarge leading-normal">
          {selectedDetails?.componentType && selectedDetails.componentType.icon && (
            <div className="w-9 h-9 mt-auto mb-auto mr-2 flex-center">
              <ComponentTypeIcon data={selectedDetails.componentType.icon} size={10} />
            </div>
          )}
          {selectedDetails?.name}
        </span>
      </div>

      {isFeatureActive(Features.LockState) && (
        <div className="flex-end pb-small">
          <LockStateMenu
            state={LockState.unlocked}
            user="Abdullah"
            message="Hands off, my work, not yours. mine!"
          />
        </div>
      )}
    </div>
  );
};

export default ComponentHeader;
