// src/hooks/queries/useComponentSearch.ts
import { useQuery } from '@tanstack/react-query';
import { searchComponentsTree } from '@/services/api/index';
import { TreeNodeType } from '@/utils/types';
import { useAuth } from '@/context/AuthContext';

interface UseComponentSearchOptions {
  enabled?: boolean;
  keepPreviousData?: boolean;
}

/**
 * Hook for searching components in the tree
 * This hook honors the authentication state before making API calls
 *
 * @param searchTerm - Term to search for
 * @param options - Additional query options
 */
export const useComponentSearch = (
  searchTerm: string | undefined,
  options: UseComponentSearchOptions = {}
) => {
  const { enabled: customEnabled = true, keepPreviousData = true } = options;
  const { isAuthenticated, isTokenReady } = useAuth();

  return useQuery<TreeNodeType[], Error>({
    queryKey: ['componentSearch', searchTerm],
    queryFn: () => {
      if (!searchTerm) {
        return Promise.resolve([]);
      }
      // Validate the search term parameter
      if (searchTerm.trim() === '') {
        throw new Error('Empty search term provided to useComponentSearch hook');
      }
      return searchComponentsTree(searchTerm);
    },
    // CRITICAL FIX: Wait for both authentication AND token to be ready
    enabled: isAuthenticated && isTokenReady && !!searchTerm && customEnabled,
    staleTime: 2 * 60 * 1000, // 2 minutes - search results change more frequently
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Only retry network errors, not 4xx errors
      if (error.message?.includes('Network Error')) {
        return failureCount < 2;
      }
      return false;
    },
    placeholderData: keepPreviousData ? previousData => previousData : undefined,
  });
};

// Type helper for components
export type UseComponentSearchResult = ReturnType<typeof useComponentSearch>;
