// src/services/error-handling.ts
/**
 * Centralized error handling for API calls
 *
 * This module provides consistent error handling patterns for all API
 * operations in the application. It includes error classes, detection
 * utilities, and standardized error handling functions.
 */

import axios from 'axios';

/**
 * Custom API error class with additional properties
 *
 * @example
 * throw new ApiError('Failed to fetch component data', 404, { detail: 'Component not found' });
 */
export class ApiError extends Error {
  status?: number;
  responseData?: any;

  constructor(message: string, status?: number, responseData?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.responseData = responseData;
  }
}

/**
 * Checks if an error is an unauthorized error (401)
 *
 * @param error - The error to check
 * @returns True if the error is an unauthorized error (401)
 *
 * @example
 * if (isUnauthorizedError(error)) {
 *   // Handle unauthorized error
 * }
 */
export const isUnauthorizedError = (error: unknown): boolean => {
  return error instanceof Error && 'response' in error && (error as any).response?.status === 401;
};

/**
 * Centralized error handler for API calls
 *
 * @param error - The caught error
 * @param action - Description of the operation that failed (e.g., 'fetching component')
 * @param id - Optional ID of the entity being operated on
 * @returns Either throws an appropriate error or returns a fallback value
 *
 * @example
 * try {
 *   // API call
 * } catch (error) {
 *   return handleApiError(error, 'fetching component', componentId);
 * }
 */
export const handleApiError = <T>(error: unknown, action: string, id?: string): T | never => {
  // Special handling for 404s on job results - return empty results instead of throwing
  if (
    action === 'getting jobs results' &&
    (error as any).isAxiosError &&
    (error as any).response?.status === 404
  ) {
    return { results: [], totalRecords: 0 };
  }

  // Log with consistent format and include response details if available
  const responseStatus = axios.isAxiosError(error) ? error.response?.status : undefined;
  const responseData = axios.isAxiosError(error) ? error.response?.data : undefined;
  console.error(
    `[API] Error ${action}${id ? ` for ${id}` : ''}${responseStatus ? ` (${responseStatus})` : ''}:`,
    error.message || error,
    responseData ? '\nResponse data:' : '',
    responseData || ''
  );

  // Handle unauthorized errors consistently
  if (isUnauthorizedError(error)) {
    throw new Error('unauthorized');
  }

  // Create a more informative error for consistent handling
  if (axios.isAxiosError(error)) {
    throw new ApiError(
      `Failed ${action}${id ? ` for ${id}` : ''}: ${error.message}`,
      error.response?.status,
      error.response?.data
    );
  } else if (error instanceof Error) {
    throw new ApiError(`Failed ${action}${id ? ` for ${id}` : ''}: ${error.message}`);
  } else {
    throw new ApiError(`Unknown error ${action}${id ? ` for ${id}` : ''}: ${String(error)}`);
  }
};
