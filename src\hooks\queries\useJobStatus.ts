import { useQuery } from '@tanstack/react-query';
import { fetchJobStatus } from '@/services/api/index';
import { JobStatus } from '@/utils/jobs';

/**
 * Custom hook to fetch and cache jobs for a component
 * @param jobId - The ID of the job  to fetch the status for
 * @param enabled - Whether the query should run automatically
 */
export const useJobStatus = (jobId: string | null, enabled = true) => {
  return useQuery<JobStatus, Error>({
    queryKey: ['jobStatus', jobId],
    queryFn: () => {
      if (!jobId) {
        throw new Error('No Job ID provided to useJobsStatus hook');
      }
      // Validate the job ID parameter
      if (jobId.trim() === '') {
        throw new Error('Empty job ID provided to useJobStatus hook');
      }
      return fetchJobStatus(jobId);
    },
    enabled: !!jobId && enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};
