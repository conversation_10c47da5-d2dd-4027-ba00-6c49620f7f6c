c# nginx Deployment Guide for Windows - Octoplant Web Client

This guide provides step-by-step instructions for deploying your React application on nginx for Windows.

## Prerequisites

- Windows 10/11 or Windows Server
- Node.js 18+ installed
- PowerShell (Administrator privileges required)
- nginx for Windows

## Quick Start

### 1. Install nginx (Choose one method)

#### Option A: Using Chocolatey (Recommended)
```powershell
# Install Chocolatey if not already installed
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# Install nginx
choco install nginx
```

#### Option B: Manual Installation
1. Download nginx for Windows from http://nginx.org/en/download.html
2. Extract to `C:\nginx`
3. Ensure `C:\nginx\nginx.exe` exists

### 2. Setup Environment
```powershell
# Run environment setup
.\setup-environment-windows.ps1
```

This will prompt you for:
- OAuth server URL (default: http://localhost:64023)
- API server URL (default: http://localhost:5256)
- Storage server URL (default: http://localhost:5157)
- Domain name (default: localhost)

### 3. Deploy to nginx
```powershell
# Run deployment script as Administrator
.\deploy-nginx-windows.ps1
```

### 4. Access Your Application
- Open browser and go to: `http://localhost` or `http://your-domain`

## What the Scripts Do

### setup-environment-windows.ps1
- Creates `.env.production` with your backend URLs
- Updates `nginx-windows.conf` with your domain and backend URLs
- Checks if nginx is installed

### deploy-nginx-windows.ps1
- Checks nginx installation
- Builds your React application
- Copies built files to `C:\nginx\html\octoplant-web`
- Configures nginx with the provided configuration
- Starts nginx service

### nginx-windows.conf
- Windows-specific nginx configuration
- Proxy configuration for your backend APIs
- SPA routing support
- Static file caching
- Basic security headers

## File Structure After Deployment

```
C:\nginx\
├── nginx.exe
├── conf\
│   ├── nginx.conf                    # Main nginx config
│   └── octoplant-web.conf           # Your app config
├── html\
│   └── octoplant-web\               # Your React app files
│       ├── index.html
│       ├── static\
│       │   ├── css\
│       │   └── js\
│       ├── favicon.ico
│       └── manifest.json
└── logs\
    ├── access.log
    ├── error.log
    ├── octoplant-web.access.log
    └── octoplant-web.error.log
```

## nginx Management Commands

### Starting/Stopping nginx
```powershell
# Start nginx
cd C:\nginx
.\nginx.exe

# Stop nginx
taskkill /f /im nginx.exe

# Reload configuration (without stopping)
cd C:\nginx
.\nginx.exe -s reload

# Test configuration
cd C:\nginx
.\nginx.exe -t
```

### Using nginx as a Windows Service (Optional)
```powershell
# Install nginx as a service using NSSM
choco install nssm

# Install nginx service
nssm install nginx C:\nginx\nginx.exe

# Start the service
net start nginx

# Stop the service
net stop nginx
```

## Viewing Logs

### PowerShell Commands
```powershell
# View access logs
Get-Content C:\nginx\logs\octoplant-web.access.log -Tail 20

# View error logs
Get-Content C:\nginx\logs\octoplant-web.error.log -Tail 20

# Monitor logs in real-time
Get-Content C:\nginx\logs\octoplant-web.access.log -Wait -Tail 10

# Check nginx processes
Get-Process -Name "nginx"
```

## Troubleshooting

### 1. nginx fails to start
```powershell
# Check if port 80 is in use
netstat -an | findstr :80

# Test nginx configuration
cd C:\nginx
.\nginx.exe -t

# Check Windows Event Viewer for errors
```

### 2. Application loads but API calls fail
- Verify backend services are running
- Check nginx error logs
- Test backend URLs directly:
```powershell
curl http://localhost:5256/api/health
curl http://localhost:64023/v1/oauth2/.well-known/openid_configuration
```

### 3. Permission issues
- Ensure PowerShell is running as Administrator
- Check file permissions in `C:\nginx\html\octoplant-web`
- Verify nginx has read access to files

### 4. Configuration issues
```powershell
# Validate nginx configuration
cd C:\nginx
.\nginx.exe -t

# Check configuration file syntax
Get-Content C:\nginx\conf\octoplant-web.conf
```

## Updating Your Application

To update your application after making changes:

```powershell
# Stop nginx
taskkill /f /im nginx.exe

# Build new version
npm run build

# Copy new files
Remove-Item C:\nginx\html\octoplant-web\* -Recurse -Force
Copy-Item build\* C:\nginx\html\octoplant-web\ -Recurse -Force

# Start nginx
cd C:\nginx
.\nginx.exe
```

## Adding HTTPS (Optional)

### 1. Obtain SSL Certificate
- Use Let's Encrypt with win-acme
- Or use a commercial SSL certificate

### 2. Update nginx Configuration
Add to `nginx-windows.conf`:
```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate C:/nginx/ssl/your-cert.crt;
    ssl_certificate_key C:/nginx/ssl/your-key.key;
    
    # ... rest of configuration
}

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

## Windows Firewall Configuration

```powershell
# Allow nginx through Windows Firewall
New-NetFirewallRule -DisplayName "nginx HTTP" -Direction Inbound -Protocol TCP -LocalPort 80 -Action Allow
New-NetFirewallRule -DisplayName "nginx HTTPS" -Direction Inbound -Protocol TCP -LocalPort 443 -Action Allow
```

## Performance Tips for Windows

1. **Disable Windows Defender real-time scanning** for nginx directories (if safe to do so)
2. **Use SSD storage** for better I/O performance
3. **Configure nginx worker processes** based on CPU cores
4. **Monitor Windows Performance Counters** for nginx processes

## Common Windows-Specific Issues

### 1. Path Separators
- Use forward slashes `/` in nginx configuration
- Windows paths like `C:\nginx` become `C:/nginx` in config

### 2. File Locking
- Stop nginx completely before updating files
- Use `taskkill /f /im nginx.exe` to force stop if needed

### 3. Antivirus Interference
- Add nginx directory to antivirus exclusions
- Whitelist nginx.exe process

### 4. Windows Updates
- nginx may need restart after Windows updates
- Check nginx status after system reboots

## Backup and Recovery

### Backup Script (PowerShell)
```powershell
# Create backup
$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$backupPath = "C:\nginx-backup-$timestamp"

Copy-Item C:\nginx\html\octoplant-web $backupPath -Recurse
Copy-Item C:\nginx\conf\octoplant-web.conf "$backupPath\octoplant-web.conf"

Write-Host "Backup created at: $backupPath"
```

### Recovery Script (PowerShell)
```powershell
param($BackupPath)

# Stop nginx
taskkill /f /im nginx.exe

# Restore files
Remove-Item C:\nginx\html\octoplant-web\* -Recurse -Force
Copy-Item "$BackupPath\*" C:\nginx\html\octoplant-web\ -Recurse -Force

# Start nginx
cd C:\nginx
.\nginx.exe

Write-Host "Recovery completed from: $BackupPath"
```

---

**Note**: This configuration is optimized for Windows environments. For production use, consider additional security measures and monitoring solutions.
