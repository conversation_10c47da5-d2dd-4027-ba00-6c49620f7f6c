import { t } from 'i18next';
import { VersionDetailsCurrentPage } from './types';

export const VersionDetailsCurrentPageTitle = (page: VersionDetailsCurrentPage): string => {
  switch (page) {
    case VersionDetailsCurrentPage.JobList:
      return t('details.jobs');
    case VersionDetailsCurrentPage.Details:
      return t('details.details');
    case VersionDetailsCurrentPage.ChangeHistory:
    default:
      return t('details.changeHistory');
  }
};
