import { Log, UserManagerSettings, WebStorageStateStore } from 'oidc-client-ts';

// Only log errors in production mode
const isProduction = import.meta.env.PROD;
Log.setLogger(console);
Log.setLevel(isProduction ? Log.ERROR : Log.WARN);

const url = window.location.origin;

// For nginx deployment, use proxy endpoints instead of direct backend URLs
const getOAuthBaseUrl = () => {
  // Check if we're using environment variable (for nginx proxy) or default
  const envUrl = import.meta.env.VITE_OAUTH_URL;
  if (envUrl) {
    // Environment variable is set (nginx deployment)
    return envUrl.endsWith('/') ? envUrl : `${envUrl}/`;
  }
  // Fallback to direct backend URL for development
  return 'https://localhost:64023/';
};

// OAuth API URL - will be either proxy or direct depending on environment
const OAUTH_API_URL = `${getOAuthBaseUrl()}v1/oauth2/`;

// Direct access to OAuth endpoints
export const settings: UserManagerSettings = {
  // Core settings
  authority: getOAuthBaseUrl(),
  metadata: {
    issuer: getOAuthBaseUrl(),
    authorization_endpoint: `${OAUTH_API_URL}authorize`,
    token_endpoint: `${OAUTH_API_URL}token`,
    jwks_uri: `${OAUTH_API_URL}jwks`,
    end_session_endpoint: `${OAUTH_API_URL}end_session`,
    response_types_supported: ['code'],
    subject_types_supported: ['public'],
    userinfo_endpoint: `${OAUTH_API_URL}userinfo`,
  },

  // Client configuration
  client_id: 'octoplant-client',
  client_secret: 'd45b593b-3060-4d73-8a9a-f111241011ee',
  redirect_uri: `${url}/signin-callback`,
  post_logout_redirect_uri: url,
  response_type: 'code',
  scope: 'roles openid',

  // Feature configuration - enhanced with automatic renewal
  loadUserInfo: true,
  userStore: new WebStorageStateStore({ store: window.localStorage }),
  monitorSession: true,

  // Enable both automatic silent renewal and custom token refresh for redundancy
  automaticSilentRenew: true,
  silentRequestTimeoutInSeconds: 10, // 10 seconds timeout for silent renewal
  accessTokenExpiringNotificationTimeInSeconds: 60, // Notify 60 seconds before expiration
  checkSessionIntervalInSeconds: 10, // Check session every 10 seconds
  silent_redirect_uri: `${url}/silent-callback`,
};
