import { Log, UserManagerSettings, WebStorageStateStore } from 'oidc-client-ts';

// Only log errors in production mode
const isProduction = import.meta.env.PROD;
Log.setLogger(console);
Log.setLevel(isProduction ? Log.ERROR : Log.WARN);

const url = window.location.origin;

// For nginx deployment, use proxy endpoints instead of direct backend URLs
const getOAuthBaseUrl = () => {
  // Check if we're using environment variable (for nginx proxy)
  const envUrl = import.meta.env.VITE_OAUTH_URL;
  if (envUrl) {
    // Environment variable is set (nginx deployment)
    // Make sure it ends with / for proper URL construction
    return envUrl.endsWith('/') ? envUrl : `${envUrl}/`;
  }
  // Fallback to direct backend URL for development
  return 'https://localhost:64023/';
};

// OAuth API URL construction - handle nginx proxy path correctly
const getOAuthApiUrl = () => {
  const baseUrl = getOAuthBaseUrl();
  
  // If using nginx proxy, the path is already included
  if (baseUrl.includes('/oauth/')) {
    // Nginx will rewrite /oauth/* to /* so we don't need v1/oauth2 prefix
    return baseUrl;
  }
  
  // Direct connection needs the full path
  return `${baseUrl}v1/oauth2/`;
};

const OAUTH_BASE_URL = getOAuthBaseUrl();
const OAUTH_API_URL = getOAuthApiUrl();

// Direct access to OAuth endpoints
export const settings: UserManagerSettings = {
  // Core settings
  authority: OAUTH_BASE_URL,
  metadata: {
    issuer: OAUTH_BASE_URL,
    authorization_endpoint: `${OAUTH_API_URL}authorize`,
    token_endpoint: `${OAUTH_API_URL}token`,
    jwks_uri: `${OAUTH_API_URL}jwks`,
    end_session_endpoint: `${OAUTH_API_URL}end_session`,
    response_types_supported: ['code'],
    subject_types_supported: ['public'],
    userinfo_endpoint: `${OAUTH_API_URL}userinfo`,
  },

  // Client configuration
  client_id: 'octoplant-client',
  client_secret: 'd45b593b-3060-4d73-8a9a-f111241011ee',
  redirect_uri: `${url}/signin-callback`,
  post_logout_redirect_uri: url,
  response_type: 'code',
  scope: 'roles openid',

  // Feature configuration
  loadUserInfo: true,
  userStore: new WebStorageStateStore({ store: window.localStorage }),
  monitorSession: true,

  // Temporarily disable automatic renewal to avoid loops
  automaticSilentRenew: false, // Changed to false
  silentRequestTimeoutInSeconds: 10,
  accessTokenExpiringNotificationTimeInSeconds: 60,
  checkSessionIntervalInSeconds: 30, // Increased from 10 to 30
  silent_redirect_uri: `${url}/silent-callback`,
  
  // Add these for better debugging
  revokeTokensOnSignout: true,
  filterProtocolClaims: true,
  loadUserInfo: true,
};

// Export for debugging
export const debugUrls = () => {
  console.log('OAuth Configuration URLs:', {
    base: OAUTH_BASE_URL,
    api: OAUTH_API_URL,
    authority: settings.authority,
    authorize: settings.metadata?.authorization_endpoint,
    token: settings.metadata?.token_endpoint,
    jwks: settings.metadata?.jwks_uri,
    userinfo: settings.metadata?.userinfo_endpoint,
  });
};