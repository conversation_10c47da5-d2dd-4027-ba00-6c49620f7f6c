import CloseIconDark from '@/assets/svgs/dark/close.svg';
import CloseIconLight from '@/assets/svgs/dark/close.svg';

export enum CloseButtonStyle {
  dark = 'dark',
  light = 'light',
}

interface CloseButtonProps {
  style?: CloseButtonStyle;
  onClick: () => void;
}

const CloseButton: React.FC<CloseButtonProps> = ({ style = CloseButtonStyle.dark, onClick }) => {
  const icon = style === CloseButtonStyle.dark ? CloseIconDark : CloseIconLight;
  const borderColor =
    style === CloseButtonStyle.dark ? 'border-primary' : 'border-primary-inverted';
  return (
    <button className={'absolute top-2 right-2 p-2'} onClick={onClick} aria-label="Close Overlay">
      <img src={icon} alt="Close" className={`w-6 h-6 p-1 border ${borderColor} rounded-sm`} />
    </button>
  );
};
export default CloseButton;
