import { useTranslation } from 'react-i18next';
import { Job } from '@/utils/types';
import ToolButton, { ToolButtonStyle } from '@/components/shared/ToolButton/ToolButton';
import { JobStatus } from '@/utils/jobs';
import { LoadingSpinnerSmall } from '@/components/shared/LoadingSpinner/LoadingSpinner';
import { useJobAction } from '@/hooks/queries/useJobAction';
import { useJobStatusData } from '@/hooks/useJobStatusData';
import { toast } from 'react-toastify';
import { useState } from 'react';
import { Features, isFeatureImplemented } from '@/utils/featureFlags';

interface JobStartStopButtonProps {
  job: Job;
}

enum StartOrStop {
  Start,
  Stop,
}

const executionState2StartOrStop = (state: JobStatus): StartOrStop => {
  const stateTranslations: Record<JobStatus, StartOrStop> = {
    [JobStatus.Waiting]: StartOrStop.Start,
    [JobStatus.Running]: StartOrStop.Stop,
  };
  return stateTranslations[state] || StartOrStop.Start;
};

const getDisplayText = (status: JobStatus, t: (key: string) => string): string => {
  const startOrStop = executionState2StartOrStop(status);
  const stateTranslations: Record<StartOrStop, string> = {
    [StartOrStop.Start]: 'jobs.start',
    [StartOrStop.Stop]: 'jobs.stop',
  };
  return t(stateTranslations[startOrStop]);
};

const getStyle = (status: JobStatus): ToolButtonStyle => {
  const startOrStop = executionState2StartOrStop(status);
  const stateTranslations: Record<StartOrStop, ToolButtonStyle> = {
    [StartOrStop.Start]: ToolButtonStyle.PrimaryYellow,
    [StartOrStop.Stop]: ToolButtonStyle.PrimaryBlue,
  };
  return stateTranslations[startOrStop];
};

const JobStartStopButton: React.FC<JobStartStopButtonProps> = ({ job }) => {
  const { t } = useTranslation();
  const { status: actualStatus, refresh } = useJobStatusData(job.id);
  const jobAction = useJobAction();
  const [optimisticDisplayStatus, setOptimisticDisplayStatus] = useState<JobStatus | null>(null);

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();

    const actionToPerform = actualStatus === JobStatus.Running ? 'stop' : 'start';
    if (actionToPerform === 'start') {
      setOptimisticDisplayStatus(JobStatus.Running);
      // Defer mutation to allow optimistic state to render first
      setTimeout(() => {
        jobAction.mutate(
          {
            apiJobId: job.id.replaceAll('-', '').toUpperCase(),
            originalJobId: job.id,
            action: actionToPerform,
          },
          {
            onSuccess: data => {
              console.log(`[JobStartStop] Successfully ${actionToPerform}ed job ${job.id}`, data);
              setTimeout(() => {
                console.log(`[JobStartStop] Delayed refresh for job ${job.id} after start`);
                refresh();
              }, 3000);
            },
            onError: error => {
              toast.error(t('jobs.failedToStart'));
              console.error(`[JobStartStop] Failed to ${actionToPerform} job ${job.id}:`, error);
              setOptimisticDisplayStatus(null);
            },
          }
        );
      }, 0);
    } else if (actionToPerform === 'stop') {
      if (!isFeatureImplemented(Features.StopJobs)) {
        console.log('Not implemented');
        toast.error(t('jobs.stopNotImplemented'));
        return;
      }

      setOptimisticDisplayStatus(JobStatus.Waiting);
      // Defer mutation for stop as well for consistency, though less critical
      setTimeout(() => {
        jobAction.mutate(
          {
            apiJobId: job.id.replaceAll('-', '').toUpperCase(),
            originalJobId: job.id,
            action: actionToPerform,
          },
          {
            onSuccess: data => {
              console.log(`[JobStartStop] Successfully ${actionToPerform}ed job ${job.id}`, data);
              refresh();
            },
            onError: error => {
              toast.error(t('jobs.failedToStop'));
              console.error(`[JobStartStop] Failed to ${actionToPerform} job ${job.id}:`, error);
              setOptimisticDisplayStatus(null);
            },
          }
        );
      }, 0);
    }
  };

  if (actualStatus === JobStatus.Running && optimisticDisplayStatus === JobStatus.Running)
    setOptimisticDisplayStatus(JobStatus.Waiting);

  const waitingForJobStart =
    optimisticDisplayStatus === JobStatus.Running && actualStatus === JobStatus.Waiting;
  const waitingForJobFinish =
    optimisticDisplayStatus === JobStatus.Waiting && actualStatus === JobStatus.Running;
  const waitingForStatusChange =
    optimisticDisplayStatus !== null && optimisticDisplayStatus != actualStatus;

  let displayStatus = JobStatus.Waiting;
  if (waitingForJobStart === true || waitingForJobFinish === true) {
    displayStatus = JobStatus.Running;
  }

  let canClick = true;

  if (job.isDeactivated || jobAction.isPending) {
    canClick = false;
  } else if (waitingForJobStart) {
    // Keep button disabled after clicking Start until the job completes and returns to Waiting state
    canClick = false;
  }
  // Show spinner when job action is pending or when waiting for job status to update after clicking Start
  const showSpinner =
    jobAction.isPending || waitingForStatusChange || waitingForJobFinish || waitingForJobStart;

  console.log(
    `[JobStartStopButton] Rendering for job ${job.id}. Actual: ${actualStatus}, Optimistic: ${optimisticDisplayStatus}, Display: ${displayStatus}, CanClick: ${canClick}, ShowSpinner: ${showSpinner}`
  );

  return (
    <div className="flex gap-2 items-center">
      <ToolButton
        type={getStyle(displayStatus)}
        text={getDisplayText(displayStatus, t)}
        active={canClick}
        onClick={canClick ? handleClick : undefined}
      />
      {showSpinner && <LoadingSpinnerSmall size={6} />}
    </div>
  );
};

export default JobStartStopButton;
