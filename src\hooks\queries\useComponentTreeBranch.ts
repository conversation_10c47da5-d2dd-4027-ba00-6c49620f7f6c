import { useQuery } from '@tanstack/react-query';
import { TreeNodeType } from '@/utils/types';
import { createTree } from '@/services/api/index';
import { useAuth } from '@/context/AuthContext';

interface UseComponentTreeBranchOptions {
  enabled?: boolean;
}

/**
 * Hook for fetching the initial component tree structure
 * This hook honors the authentication state before making API calls
 */
export const useComponentTreeBranch = (options: UseComponentTreeBranchOptions = {}) => {
  const { enabled: customEnabled = true } = options;
  const { isAuthenticated, isTokenReady } = useAuth();

  return useQuery<TreeNodeType[], Error>({
    queryKey: ['componentTreeBranch'],
    queryFn: () => createTree(),
    // CRITICAL FIX: Wait for both authentication AND token to be ready
    enabled: isAuthenticated && isTokenReady && customEnabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // Only retry network errors, not 4xx errors
      if (error.message?.includes('Network Error')) {
        return failureCount < 3;
      }
      return false;
    },
  });
};

// Type helper for components
export type UseComponentTreeBranchResult = ReturnType<typeof useComponentTreeBranch>;
