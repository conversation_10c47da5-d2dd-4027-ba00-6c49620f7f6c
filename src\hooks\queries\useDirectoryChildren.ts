import {
  useQuery,
  useInfiniteQuery,
  useQueryClient,
  type QueryClient,
  type UseQueryOptions,
  type UseInfiniteQueryOptions,
  type InfiniteData,
  type QueryKey,
} from '@tanstack/react-query';
import { fetchTreeNodes, fetchMoreTreeNodes } from '@/services/api/index';
import { TreeNodeType } from '@/utils/types';
import { useAuth } from '@/context/AuthContext';
import { handleApiError, isUnauthorizedError } from '@/services/error-handling';

interface DirectoryChildrenResult {
  nodes: TreeNodeType[];
  hasMorePages: boolean;
  totalCount: number;
  nextPage?: number;
}

interface UseDirectoryChildrenOptions {
  pageNumber?: number;
  pageSize?: number;
  enabled?: boolean;
  queryKeyPrefix?: string;
  incrementalLoading?: boolean;
  staleTime?: number;
  gcTime?: number;
  retryCount?: number;
}

interface UseDirectoryChildrenReturn {
  data: TreeNodeType[];
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  isFetching: boolean;
  hasNextPage: boolean;
  totalCount: number;
  fetchNextPage?: () => Promise<any>;
  refetch: () => Promise<unknown>;
}

/**
 * Hook for fetching directory children from the component tree with support for incremental loading
 *
 * @param id - Directory ID to fetch children for (null for root)
 * @param options - Query options including pagination and incremental loading
 * @returns Object containing query state and data
 *
 * @example
 * // Basic usage
 * const { data, isLoading, isError } = useDirectoryChildren('root-id');
 *
 * // With pagination
 * const { data, hasNextPage, fetchNextPage } = useDirectoryChildren('folder-id', {
 *   pageSize: 20,
 *   incrementalLoading: true
 * });
 */
export const useDirectoryChildren = (
  id: string | null,
  options: UseDirectoryChildrenOptions = {}
): UseDirectoryChildrenReturn => {
  const {
    pageNumber = 1,
    pageSize = 50,
    enabled: customEnabled = true,
    queryKeyPrefix = 'directoryChildren',
    incrementalLoading = false,
    staleTime = 5 * 60 * 1000, // 5 minutes
    gcTime = 10 * 60 * 1000, // 10 minutes
    retryCount = 3,
  } = options;

  const { isAuthenticated, isTokenReady } = useAuth();
  const queryClient = useQueryClient();
  const nodeId = id || ''; // For root level, use empty string

  // Common query options for both query types
  const commonQueryOptions: Partial<
    UseQueryOptions<DirectoryChildrenResult, Error, DirectoryChildrenResult, QueryKey>
  > = {
    staleTime,
    gcTime,
    retry: (failureCount: number, error: Error) => {
      // Don't retry unauthorized errors
      if (isUnauthorizedError(error)) return false;

      // Only retry on network errors
      if (error.message?.includes('Network Error')) {
        return failureCount < retryCount;
      }
      return false;
    },
  };

  // Standard single-page query (used when incrementalLoading is false)
  const standardQuery = useQuery<DirectoryChildrenResult>({
    queryKey: [queryKeyPrefix, id, pageNumber, pageSize] as const,
    queryFn: async () => {
      try {
        if (pageNumber < 1) {
          throw new Error('Page number must be at least 1');
        }
        if (pageSize < 1 || pageSize > 100) {
          throw new Error('Page size must be between 1 and 100');
        }

        return await fetchTreeNodes(nodeId, pageNumber, pageSize);
      } catch (error) {
        return handleApiError(error, 'fetching directory children', id || 'root');
      }
    },
    // CRITICAL FIX: Wait for both authentication AND token to be ready
    enabled: isAuthenticated && isTokenReady && customEnabled && !incrementalLoading,
    placeholderData: previousData => previousData,
    ...commonQueryOptions,
  });

  // Infinite query for incremental loading (used when incrementalLoading is true)
  const infiniteQuery = useInfiniteQuery<
    DirectoryChildrenResult,
    Error,
    InfiniteData<DirectoryChildrenResult, number>,
    readonly unknown[],
    number
  >({
    queryKey: [queryKeyPrefix, 'infinite', id, pageSize] as const,
    initialPageParam: 1,
    queryFn: async ({ pageParam }): Promise<DirectoryChildrenResult> => {
      try {
        // For the first page, just fetch normally
        if (pageParam === 1) {
          const result = await fetchTreeNodes(nodeId, 1, pageSize);
          return {
            ...result,
            nextPage: 2,
          };
        }

        // For subsequent pages, get the existing data from the query client
        const queryData = queryClient.getQueryData<{
          pages: DirectoryChildrenResult[];
          pageParams: number[];
        }>([queryKeyPrefix, 'infinite', id, pageSize]);

        const existingNodes = queryData?.pages.flatMap(page => page.nodes) || [];
        const currentPage = pageParam - 1;

        // Fetch more nodes and combine with existing nodes
        return await fetchMoreTreeNodes(nodeId, existingNodes, currentPage, pageSize);
      } catch (error) {
        return handleApiError(error, 'fetching more directory children', id || 'root');
      }
    },
    getNextPageParam: lastPage => (lastPage.hasMorePages ? lastPage.nextPage || 1 : undefined),
    // CRITICAL FIX: Wait for both authentication AND token to be ready
    enabled: isAuthenticated && isTokenReady && customEnabled && incrementalLoading,
    staleTime,
    gcTime,
    retry: (failureCount: number, error: Error) => {
      // Don't retry unauthorized errors
      if (isUnauthorizedError(error)) return false;

      // Only retry on network errors
      if (error.message?.includes('Network Error')) {
        return failureCount < retryCount;
      }
      return false;
    },
  });

  // Return a unified interface based on which query is active
  if (incrementalLoading) {
    // For incremental loading mode, return the infinite query data and controls
    const pages = infiniteQuery.data?.pages || [];
    const allNodes = pages.flatMap((page: DirectoryChildrenResult) => page.nodes) || [];
    const totalCount = pages[0]?.totalCount || 0;
    const hasMorePages = infiniteQuery.hasNextPage || false;

    return {
      data: allNodes,
      isLoading: infiniteQuery.isLoading,
      isError: infiniteQuery.isError,
      error: infiniteQuery.error || null,
      isFetching: infiniteQuery.isFetching,
      hasNextPage: hasMorePages,
      fetchNextPage: infiniteQuery.fetchNextPage,
      totalCount,
      refetch: infiniteQuery.refetch,
    };
  }

  // For standard mode, return the regular query data
  return {
    data: standardQuery.data?.nodes || [],
    isLoading: standardQuery.isLoading,
    isError: standardQuery.isError,
    error: standardQuery.error || null,
    isFetching: standardQuery.isFetching,
    hasNextPage: standardQuery.data?.hasMorePages || false,
    totalCount: standardQuery.data?.totalCount || 0,
    refetch: standardQuery.refetch,
  };
};

// Deprecated hook removed in code cleanup

// Type helpers for components
export type UseDirectoryChildrenResult = ReturnType<typeof useDirectoryChildren>;
