import React from 'react';
import { APP_VERSION, getVersionBadgeColor, getVersionDisplayName } from '@/utils/version';

interface VersionDisplayProps {
  className?: string;
  showBadge?: boolean;
  showFullName?: boolean;
}

const VersionDisplay: React.FC<VersionDisplayProps> = ({
  className = '',
  showBadge = false,
  showFullName = false,
}) => {
  const displayName = showFullName ? getVersionDisplayName(APP_VERSION) : `v${APP_VERSION}`;
  const badgeColor = getVersionBadgeColor(APP_VERSION);

  if (showBadge) {
    return (
      <span
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white ${badgeColor} ${className}`}
      >
        {displayName}
      </span>
    );
  }

  return <span className={`text-medium ${className}`}>{displayName}</span>;
};

export default VersionDisplay;
