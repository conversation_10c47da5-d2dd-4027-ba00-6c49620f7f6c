# PowerShell script to remove Electron files and clean up the project

Write-Host "🧹 Removing Electron files and cleaning up project..." -ForegroundColor Green

# Step 1: Kill any running Electron processes
Write-Host "🛑 Stopping any running Electron processes..." -ForegroundColor Cyan
try {
    Get-Process -Name "*Electron*" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
    Get-Process -Name "*My Electron App*" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
    Write-Host "✅ Electron processes stopped" -ForegroundColor Green
} catch {
    Write-Host "ℹ️  No Electron processes were running" -ForegroundColor Yellow
}

# Step 2: Force remove dist directory
Write-Host "🗑️  Removing dist directory..." -ForegroundColor Cyan
try {
    if (Test-Path "dist") {
        # Try normal removal first
        Remove-Item "dist" -Recurse -Force -ErrorAction SilentlyContinue
        
        # If it still exists, try harder
        if (Test-Path "dist") {
            # Use robocopy to clear the directory (Windows trick)
            $tempDir = New-TemporaryFile | ForEach-Object { Remove-Item $_; New-Item -ItemType Directory -Path $_ }
            robocopy $tempDir.FullName "dist" /MIR /NFL /NDL /NJH /NJS /NC /NS /NP
            Remove-Item $tempDir -Force -Recurse
            Remove-Item "dist" -Force -Recurse -ErrorAction SilentlyContinue
        }
        
        if (-not (Test-Path "dist")) {
            Write-Host "✅ dist directory removed successfully" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Some files in dist directory could not be removed (may be in use)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "ℹ️  dist directory doesn't exist" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  Error removing dist directory: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 3: Clean npm cache and node_modules (optional but recommended)
Write-Host "🧽 Cleaning npm cache..." -ForegroundColor Cyan
try {
    npm cache clean --force
    Write-Host "✅ npm cache cleaned" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Could not clean npm cache" -ForegroundColor Yellow
}

# Step 4: Reinstall dependencies
Write-Host "📦 Reinstalling dependencies..." -ForegroundColor Cyan
try {
    Remove-Item "node_modules" -Recurse -Force -ErrorAction SilentlyContinue
    Remove-Item "package-lock.json" -Force -ErrorAction SilentlyContinue
    npm install
    Write-Host "✅ Dependencies reinstalled" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to reinstall dependencies: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 5: Test build
Write-Host "🔨 Testing build..." -ForegroundColor Cyan
try {
    npm run build
    if (Test-Path "build") {
        Write-Host "✅ Build successful! Files are in 'build' directory" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Build completed but 'build' directory not found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Build failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 Electron cleanup completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Summary:" -ForegroundColor Cyan
Write-Host "  ✅ Electron build configuration removed from package.json" -ForegroundColor White
Write-Host "  ✅ dist directory removed (Electron artifacts)" -ForegroundColor White
Write-Host "  ✅ Dependencies cleaned and reinstalled" -ForegroundColor White
Write-Host "  ✅ Project is now web-only" -ForegroundColor White
Write-Host ""
Write-Host "🚀 Next steps:" -ForegroundColor Cyan
Write-Host "  1. Run: .\setup-simple.bat" -ForegroundColor White
Write-Host "  2. Run: .\deploy-nginx-windows.ps1" -ForegroundColor White
