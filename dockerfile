# Stage 1: Build the React app
FROM node:14.17.0 as builder
 
# Set the working directory inside the container
WORKDIR /app
 
# Copy the certificate file into the image
COPY ./certificates/AUVESY-RootCA.crt /usr/local/share/ca-certificates/AUVESY-RootCA.crt
 
# Update the CA certificates to include the new certificate
RUN update-ca-certificates
 
# Configure npm to use the certificate
RUN npm config set cafile /usr/local/share/ca-certificates/AUVESY-RootCA.crt
 
# Set environment variables to handle network and certificate issues
ENV NODE_TLS_REJECT_UNAUTHORIZED=0
ENV NPM_CONFIG_STRICT_SSL=false
 
# Copy package.json and package-lock.json to the working directory
COPY package*.json ./
 
# Install production dependencies only
RUN npm install --only=production --unsafe-perm
 
# Copy the rest of the application code to the working directory
COPY . .
 
# Build the React app
RUN npm run build
 
# Stage 2: Serve the React app with Nginx
FROM nginx:1.21.1-alpine
 
# Copy built files from the builder stage
COPY --from=builder /app/build /usr/share/nginx/html
 
# Remove the user directive in the Nginx configuration if it exists
RUN sed -i '/user\s*nginx;/d' /etc/nginx/nginx.conf
 
# Create necessary directories and set permissions
RUN mkdir -p /var/cache/nginx/client_temp && \
    chmod -R 755 /var/cache/nginx && \
    chown -R nginx:nginx /var/cache/nginx
 
# Set up a non-root user (optional, if needed)
RUN adduser -D -H -s /sbin/nologin myuser && \
    chown -R myuser:myuser /usr/share/nginx/html
 
# Expose port 80
EXPOSE 80
 
# Start Nginx server
CMD ["nginx", "-g", "daemon off;"]