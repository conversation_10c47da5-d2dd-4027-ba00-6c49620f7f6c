// src/utils/transformers.ts
// Contains functions for transforming API responses to domain models
import { ApiTreeResponse, VersionApiResponse } from '@/types/api.types';
import { TreeNodeType, Version, Job, JobResult } from '@/utils/types';
import { TransformationError } from './errors';
import { transformApiResult } from './compareResult';
import { StringToDate } from './dateTime';
import { transformApiExecutionConfiguration } from './jobs';

/**
 * Transforms API tree response to domain TreeNodeType array
 * @param apiResponse - The API response containing tree node data
 * @returns Array of TreeNodeType objects
 */
export const transformApiTreeToTreeNodes = (apiResponse: ApiTreeResponse): TreeNodeType[] => {
  try {
    if (!apiResponse?.nodes || !Array.isArray(apiResponse.nodes)) {
      console.warn('[Transformers] Empty or invalid nodes array in API response');
      return [];
    }

    return apiResponse.nodes.map(node => {
      const { id, objectId, name, parentId, nodeType, icon, path } = node;
      return {
        id: nodeType === 'Component' ? objectId : id,
        name,
        description: '', // Default empty as it's no longer in the response
        type: nodeType,
        parentId,
        componentType: icon ? { id: '', name: '', icon } : null,
        displayPath: path,
        path: [], // Will be populated by the tree component
      } as TreeNodeType;
    });
  } catch (error) {
    console.error('[Transformers] Error transforming tree data:', error);
    throw new TransformationError(
      'Failed to transform API tree response',
      'transformApiTreeToTreeNodes',
      apiResponse
    );
  }
};

/**
 * Transforms API version response to domain Version array
 * @param apiResponse - The API response containing version data
 * @param componentId - Optional component ID to use if not provided in the response
 * @returns Array of Version objects
 */
export const transformApiVersionsToVersions = (
  apiResponse: VersionApiResponse,
  componentId: string
): Version[] => {
  try {
    if (!apiResponse?.versions || !Array.isArray(apiResponse.versions)) {
      console.warn('[Transformers] Empty or invalid versions array in API response');
      return [];
    }

    return apiResponse.versions.map((version, index) => ({
      componentId: version?.componentId?.toString() || componentId || '',
      username: version?.username?.toString() || '',
      version: parseInt(version?.version?.toString() || '0', 10),
      versionIdentifier: version?.versionIdentifier?.toString() || '',
      changeReason: version?.changeReason?.toString() || '',
      comment: version?.comment?.toString() || '',
      timestamp: StringToDate(version.timestamp),
      index,
    }));
  } catch (error) {
    console.error('[Transformers] Error transforming version data:', error);
    throw new TransformationError(
      'Failed to transform API version response',
      'transformApiVersionsToVersions',
      apiResponse
    );
  }
};

/**
 * Creates a directory root node with its children
 * @param directoryData - The directory data from the API
 * @param children - The children of the directory
 * @returns Array with a single TreeNodeType representing the directory as root
 */
export const createDirectoryRootNode = (
  directoryData: any,
  children: TreeNodeType[] = []
): TreeNodeType[] => {
  try {
    if (!directoryData?.id) {
      console.warn('[Transformers] Invalid directory data');
      return [];
    }

    return [
      {
        id: directoryData.id,
        name: directoryData.name || 'Unknown Directory',
        description: directoryData.description || '',
        type: directoryData.nodeType || 'Directory',
        parentId: '',
        componentType: directoryData.componentType || null,
        path: directoryData.path || [],
        children,
        isOpen: true,
      },
    ];
  } catch (error) {
    console.error('[Transformers] Error creating directory root node:', error);
    throw new TransformationError(
      'Failed to create directory root node',
      'createDirectoryRootNode',
      directoryData
    );
  }
};

/**
 * Transforms job with results from the API
 * @param job - The job data with results from the API
 * @returns Job with transformed data
 */
export const transformJob = (job: any): Job => {
  try {
    return {
      ...job,
      lastExecution: StringToDate(job.lastExecution) || null,
      executionConfiguration: transformApiExecutionConfiguration(job.executionConfiguration),
      latestResult: {
        versionVsBackup: transformApiResult(job.latestServerVsBackupResult || 'None', false),
        backupVsBackup: transformApiResult(job.latestBackupVsBackupResult || 'None', false),
      },
    };
  } catch (error) {
    console.error('[Transformers] Error transforming job dates:', error);
    throw new TransformationError('Failed to transform job dates', 'transformJobDates', job);
  }
};

/**
 * Transforms job results data from the API
 * @param result - The job result data from the API
 * @returns Transformed job result with proper date objects
 */
export const transformJobResult = (result: any): JobResult | undefined => {
  try {
    if (!result) {
      return undefined;
    }

    return {
      id: result.id,
      timestamp: StringToDate(result.timestamp),
      version: result.serverVersion,
      backup: result.backupDirectory.substring(result.backupDirectory.lastIndexOf('\\') + 1),
      location: result.location,
      result: {
        versionVsBackup: transformApiResult(
          result.serverVsBackupResult,
          result.serverVsBackupHasWarnings == true
        ),
        backupVsBackup: transformApiResult(
          result.prevBackupVsBackupResult,
          result.prevBackupVsBackupHasWarnings == true
        ),
      },

      // Check for both eventLog and events to handle different API response formats
      eventLog: result.eventLog
        ? result.eventLog.map((entry: any) => ({
            ...entry,
            timestamp: StringToDate(entry.timestamp),
          }))
        : result.events
          ? result.events.map((entry: any) => ({
              ...entry,
              timestamp: StringToDate(entry.timestamp),
            }))
          : [],
    };
  } catch (error) {
    console.error('[Transformers] Error transforming job results:', error);
    throw new TransformationError('Failed to transform job results', 'transformJobResults', result);
  }
};
