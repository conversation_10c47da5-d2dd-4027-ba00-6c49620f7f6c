import React, { useEffect } from 'react';
import Loader from '../shared/Loader/Loader';

// This is a simple module-level variable to track loading state
// It will persist across component unmounts
let isLoading = false;
let message = 'Loading...';
let loadingElement: HTMLDivElement | null = null;

/**
 * Shows a fixed loading screen that sits outside the React component tree
 * This ensures it stays visible during route transitions
 */
export const showFixedLoadingScreen = (loadingMessage = 'Loading...') => {
  message = loadingMessage;

  // If already showing, just update the message
  if (isLoading && loadingElement) {
    const messageEl = loadingElement.querySelector('.loading-message');
    if (messageEl) {
      messageEl.textContent = message;
    }
    return;
  }

  isLoading = true;

  // Create loading overlay if it doesn't exist
  if (!loadingElement) {
    loadingElement = document.createElement('div');
    loadingElement.className =
      'fixed inset-0 z-50 flex flex-col items-center justify-center bg-white';
    loadingElement.style.position = 'fixed';
    loadingElement.style.top = '0';
    loadingElement.style.left = '0';
    loadingElement.style.right = '0';
    loadingElement.style.bottom = '0';
    loadingElement.style.display = 'flex';
    loadingElement.style.flexDirection = 'column';
    loadingElement.style.alignItems = 'center';
    loadingElement.style.justifyContent = 'center';
    loadingElement.style.backgroundColor = 'white';
    loadingElement.style.zIndex = '9999';

    // Add the spinner (simple CSS spinner since we can't use React components)
    const spinner = document.createElement('div');
    spinner.className = 'loading-spinner';
    spinner.style.width = '48px';
    spinner.style.height = '48px';
    spinner.style.borderRadius = '50%';
    spinner.style.border = '3px solid #f3f3f3';
    spinner.style.borderTop = '3px solid #0B4A82';
    spinner.style.animation = 'spin 1s linear infinite';

    // Add animation keyframes
    const style = document.createElement('style');
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);

    // Add message
    const messageEl = document.createElement('div');
    messageEl.className = 'loading-message';
    messageEl.style.marginTop = '16px';
    messageEl.style.color = '#4b5563';
    messageEl.style.fontWeight = '500';
    messageEl.textContent = message;

    // Append elements
    loadingElement.appendChild(spinner);
    loadingElement.appendChild(messageEl);
    document.body.appendChild(loadingElement);
  }
};

/**
 * Hides the fixed loading screen
 */
export const hideFixedLoadingScreen = () => {
  if (isLoading && loadingElement) {
    // Small delay before removing to ensure smooth transition
    setTimeout(() => {
      if (loadingElement) {
        document.body.removeChild(loadingElement);
        loadingElement = null;
      }
      isLoading = false;
    }, 100);
  }
};

/**
 * A React component that shows/hides the fixed loading screen based on props
 */
interface FixedLoadingScreenProps {
  show: boolean;
  message?: string;
}

const FixedLoadingScreen: React.FC<FixedLoadingScreenProps> = ({
  show,
  message = 'Loading...',
}) => {
  useEffect(() => {
    if (show) {
      showFixedLoadingScreen(message);
    } else {
      hideFixedLoadingScreen();
    }

    // Cleanup on unmount
    return () => {
      if (show) {
        hideFixedLoadingScreen();
      }
    };
  }, [show, message]);

  // This component doesn't render anything in the DOM
  return null;
};

export default FixedLoadingScreen;
