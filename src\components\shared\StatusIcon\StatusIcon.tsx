import { EventLogType } from '@/utils/types';
import {
  backgroundColorForEventLogType,
  symbolForEventLogType,
  displayTextForEventLogType,
  foregroundColorForEventLogType,
} from './utils';
import React from 'react';

export interface StatusIconProps {
  status: EventLogType;
  size?: number;
}

export const StatusIcon: React.FC<StatusIconProps> = React.memo(({ status, size = 7 }) => {
  return (
    <span
      className={`flex text-inverted-light shadow items-center justify-center w-${size} h-${size} sizeDefinition ${backgroundColorForEventLogType(status)} ${foregroundColorForEventLogType(status)}`}
    >
      {symbolForEventLogType(status)}
    </span>
  );
});

export const StatusIconWithText: React.FC<StatusIconProps> = ({ status, size = 6 }) => (
  <div className={'grid grid-cols-[auto_1fr] gap-small'}>
    <StatusIcon size={size} status={status} />
    {displayTextForEventLogType(status)}
  </div>
);
