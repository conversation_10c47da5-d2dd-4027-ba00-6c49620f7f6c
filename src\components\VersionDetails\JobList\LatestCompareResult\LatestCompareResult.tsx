import {
  CompareResultIcon,
  CompareResultIconBoxWithText,
  TextPosition,
} from '@/components/shared/CompareResultIcon/CompareResultIcon';
import CompareResult, {
  displayTextForCompareResult,
  textColorForCompareResult,
} from '@/utils/compareResult';
import { CompareResults } from '@/utils/types';

export enum WhatResult {
  versionVsBackup,
  backupVsPreviousBackup,
}

interface CompareResultCellProps {
  compareResult: CompareResult;
  deactivated: boolean;
  withText?: boolean;
}

export interface SingleLatestResultProps {
  results: CompareResults;
  deactivated: boolean;
  what: WhatResult;
  withText?: boolean;
}

export interface LatestResultsProps {
  deactivated: boolean;
  results: CompareResults;
}

const CompareResultCell: React.FC<CompareResultCellProps> = ({
  compareResult,
  deactivated,
  withText = true,
}) => {
  const compareResultCell = withText ? (
    <CompareResultIconBoxWithText
      size={9}
      compareResult={deactivated ? CompareResult.Deactivated : compareResult}
      textPosition={TextPosition.right}
    />
  ) : (
    <CompareResultIcon
      compareResult={deactivated ? CompareResult.Deactivated : compareResult}
      size={6}
    />
  );
  return compareResultCell;
};

export const CompareResultAsTextCell: React.FC<CompareResultCellProps> = ({
  compareResult,
  deactivated,
}) => {
  return (
    <div
      className={
        'uppercase font-bold ' +
        textColorForCompareResult(deactivated ? CompareResult.Deactivated : compareResult)
      }
    >
      {displayTextForCompareResult(deactivated ? CompareResult.Deactivated : compareResult)}
    </div>
  );
};

export const LatestCompareResult: React.FC<SingleLatestResultProps> = ({
  deactivated,
  results,
  what,
  withText = true,
}) => {
  const resultTable = () => {
    if (results == null) {
      return (
        <CompareResultCell
          deactivated={deactivated}
          withText={withText}
          compareResult={CompareResult.Unknown}
        />
      );
    } else {
      if (what === WhatResult.versionVsBackup) {
        return (
          <CompareResultCell
            deactivated={deactivated}
            withText={withText}
            compareResult={results.versionVsBackup}
          />
        );
      } else {
        return (
          <CompareResultCell
            deactivated={deactivated}
            withText={withText}
            compareResult={results.backupVsBackup}
          />
        );
      }
    }
  };

  return resultTable();
};

export const LatestCompareResultAsText: React.FC<SingleLatestResultProps> = ({
  results,
  deactivated,
  what,
}) => {
  const resultTable = () => {
    if (results == null) {
      return (
        <CompareResultAsTextCell deactivated={deactivated} compareResult={CompareResult.Unknown} />
      );
    } else {
      if (what === WhatResult.versionVsBackup) {
        return (
          <CompareResultAsTextCell
            deactivated={deactivated}
            compareResult={results.versionVsBackup}
          />
        );
      } else {
        return (
          <CompareResultAsTextCell
            deactivated={deactivated}
            compareResult={results.backupVsBackup}
          />
        );
      }
    }
  };

  return resultTable();
};

export const LatestCompareResultsWithText: React.FC<LatestResultsProps> = ({
  deactivated,
  results,
}) => {
  return (
    <div className={'flex items-center gap-small'}>
      <LatestCompareResult
        withText={true}
        deactivated={deactivated}
        results={results}
        what={WhatResult.versionVsBackup}
      />
      <LatestCompareResult
        withText={true}
        deactivated={deactivated}
        results={results}
        what={WhatResult.backupVsPreviousBackup}
      />
    </div>
  );
};

export const LatestCompareResults: React.FC<LatestResultsProps> = ({ deactivated, results }) => {
  return (
    <div className={'flex items-center gap-medium'}>
      <LatestCompareResult
        withText={false}
        deactivated={deactivated}
        results={results}
        what={WhatResult.versionVsBackup}
      />
      <LatestCompareResult
        deactivated={deactivated}
        withText={false}
        results={results}
        what={WhatResult.backupVsPreviousBackup}
      />
    </div>
  );
};
