import { RefObject, useCallback, useEffect, useState } from 'react';

interface UseTreeHeightOptions {
  searchPanelRef?: RefObject<HTMLElement>;
  minHeight?: number;
  maxHeightRatio?: number;
  paddingBottom?: number;
  headerHeight?: number;
}

/**
 * Custom hook to calculate optimal tree height based on screen size and other UI elements
 *
 * @param options Configuration options for height calculation
 * @returns Calculated tree height in pixels
 */
export const useTreeHeight = (options: UseTreeHeightOptions = {}) => {
  const {
    searchPanelRef,
    minHeight = 300,
    maxHeightRatio = 0.8,
    paddingBottom = 24,
    headerHeight = 60,
  } = options;

  const [treeHeight, setTreeHeight] = useState(minHeight);

  const calculateTreeHeight = useCallback(() => {
    const screenHeight = window.innerHeight;
    const searchPanelHeight = searchPanelRef?.current?.offsetHeight || 120;
    const toggleButtonSpace = 20; // Space for toggle button area

    // Calculate available height: screen - header - search panel - padding - toggle space
    const availableHeight =
      screenHeight - headerHeight - searchPanelHeight - paddingBottom - toggleButtonSpace;

    // Ensure minimum height and respect maximum height ratio
    const maxAllowedHeight = screenHeight * maxHeightRatio;
    const calculatedHeight = Math.max(minHeight, Math.min(availableHeight, maxAllowedHeight));

    // Only update if the height has changed significantly (avoid unnecessary re-renders)
    setTreeHeight(prevHeight => {
      const heightDifference = Math.abs(prevHeight - calculatedHeight);
      return heightDifference > 10 ? calculatedHeight : prevHeight;
    });
  }, [searchPanelRef, minHeight, maxHeightRatio, paddingBottom, headerHeight]);

  // Recalculate height on mount and window resize
  useEffect(() => {
    calculateTreeHeight();

    const handleResize = () => {
      calculateTreeHeight();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [calculateTreeHeight]);

  // Recalculate when search panel height changes
  useEffect(() => {
    if (!searchPanelRef?.current) return;

    const resizeObserver = new ResizeObserver(() => {
      calculateTreeHeight();
    });

    resizeObserver.observe(searchPanelRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [calculateTreeHeight, searchPanelRef]);

  return treeHeight;
};

/**
 * Custom hook to calculate optimal tree width based on container size
 *
 * @param containerRef Reference to the tree container element
 * @returns Optimal tree width in pixels
 */
export const useTreeWidth = (containerRef?: RefObject<HTMLElement>) => {
  const [treeWidth, setTreeWidth] = useState(394); // Default safe width

  const calculateTreeWidth = useCallback(() => {
    // If we have a container ref, measure its actual width
    const containerWidth = containerRef?.current?.offsetWidth || 420; // Default to 420px

    // Reserve space for scrollbar and padding
    const scrollbarWidth = 12; // From CSS
    const paddingRight = 8; // From CSS padding-right
    const borderWidth = 2; // 1px each side
    const safetyMargin = 6; // Extra margin for safety

    const reservedSpace = scrollbarWidth + paddingRight + borderWidth + safetyMargin;
    const optimalWidth = containerWidth - reservedSpace;

    // Ensure minimum width and reasonable maximum
    const finalWidth = Math.max(300, Math.min(optimalWidth, containerWidth - 20));
    setTreeWidth(finalWidth);
  }, [containerRef]);

  // Recalculate on mount and window resize
  useEffect(() => {
    calculateTreeWidth();

    const handleResize = () => {
      calculateTreeWidth();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [calculateTreeWidth]);

  // Recalculate when container size changes
  useEffect(() => {
    if (!containerRef?.current) return;

    const resizeObserver = new ResizeObserver(() => {
      calculateTreeWidth();
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [calculateTreeWidth, containerRef]);

  return treeWidth;
};
