// src/services/tokenRefresh.ts
import axios from 'axios';
import { setAuthToken } from './httpClient';
import { User, UserManager } from 'oidc-client-ts';
import { settings } from '@/authSettings';

interface RefreshTokenResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
  refresh_token?: string;
}

// Create a user manager instance for token operations
const userManager = new UserManager(settings);

// Refresh cooldown mechanism to prevent excessive refreshes
let lastRefreshTime = 0;
const REFRESH_COOLDOWN_MS = 10000; // 10 seconds minimum between refreshes

// Token expiration threshold in seconds
const TOKEN_REFRESH_THRESHOLD = 300; // Refresh if less than 5 minutes remaining

/**
 * Refreshes the authentication token using the refresh_token endpoint
 * @returns Promise with the new token information
 */
export const refreshToken = async (): Promise<RefreshTokenResponse | null> => {
  try {
    // Check if we've refreshed recently
    const now = Date.now();
    if (now - lastRefreshTime < REFRESH_COOLDOWN_MS) {
      console.log(
        `[TokenRefresh] Skipping refresh - cooldown period (${Math.floor((now - lastRefreshTime) / 1000)}s / ${REFRESH_COOLDOWN_MS / 1000}s)`
      );
      return null;
    }

    console.log('[TokenRefresh] Attempting to refresh token');

    // Get the current user to extract the refresh token
    const currentUser = await userManager.getUser();
    if (!currentUser || !currentUser.refresh_token) {
      console.error('[TokenRefresh] No user or refresh token available');
      return null;
    }

    // Check if token is still valid for a reasonable time
    const expiresAt = currentUser.expires_at || 0;
    const nowInSeconds = Math.floor(Date.now() / 1000);
    const timeUntilExpiration = expiresAt - nowInSeconds;

    console.log(
      `[TokenRefresh] Token validity check: expires in ${timeUntilExpiration}s, threshold is ${TOKEN_REFRESH_THRESHOLD}s`
    );

    // If token is already expired, we definitely need to refresh
    if (timeUntilExpiration <= 0) {
      console.warn(
        `[TokenRefresh] Token has already expired ${Math.abs(timeUntilExpiration)} seconds ago`
      );
    }
    // If token expires in more than 5 minutes, no need to refresh
    else if (timeUntilExpiration > TOKEN_REFRESH_THRESHOLD) {
      console.log(
        `[TokenRefresh] Token still valid for ${timeUntilExpiration} seconds, skipping refresh`
      );
      return null;
    }

    // We'll use the most reliable approach - the same endpoint that oidc-client-ts uses
    console.log('[TokenRefresh] Using direct access to token endpoint');

    // Form data approach - this is what oidc-client-ts uses internally
    const formData = new URLSearchParams();
    formData.append('client_id', settings.client_id as string);
    if (settings.client_secret) {
      formData.append('client_secret', settings.client_secret);
    }
    formData.append('grant_type', 'refresh_token');
    formData.append('refresh_token', currentUser.refresh_token);

    // Use the token endpoint from settings, ensuring it's properly formatted
    const tokenEndpoint = settings.metadata?.token_endpoint;
    if (!tokenEndpoint) {
      throw new Error('Token endpoint not configured');
    }

    console.log('[TokenRefresh] Token endpoint:', tokenEndpoint);

    // Create a one-time axios instance specifically for this request
    // This avoids any baseURL issues with existing clients
    const response = await axios.post<RefreshTokenResponse>(
      // Replace the full URL if it's relative (starting with /)
      tokenEndpoint.startsWith('/') ? `${window.location.origin}${tokenEndpoint}` : tokenEndpoint,
      formData,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );

    console.log('[TokenRefresh] Token refresh successful');

    // Update the auth token in the HTTP client
    if (response.data.access_token) {
      setAuthToken(response.data.access_token);
    }

    // Update last refresh time
    lastRefreshTime = Date.now();

    return response.data;
  } catch (error) {
    console.error('[TokenRefresh] Token refresh failed:', error);
    throw error;
  }
};

/**
 * Updates the user with a new token and stores it
 * @param tokenResponse - The response from the token refresh endpoint
 * @returns Promise that resolves when the user is updated
 */
export const updateUserWithNewToken = async (
  tokenResponse: RefreshTokenResponse
): Promise<User | null> => {
  try {
    // Get the current user directly - we'll use a direct approach
    const currentUser = await userManager.getUser();
    if (!currentUser) {
      throw new Error('No current user found');
    }

    // Instead of using complex mechanisms, directly update the token properties
    // This is the most reliable approach when the server has issues
    currentUser.access_token = tokenResponse.access_token;
    if (tokenResponse.refresh_token) {
      currentUser.refresh_token = tokenResponse.refresh_token;
    }

    // Update expiration time
    const expiresAt = Math.floor(Date.now() / 1000) + tokenResponse.expires_in;
    currentUser.expires_at = expiresAt;

    console.log(
      '[TokenRefresh] Directly updating user token, expires:',
      new Date(expiresAt * 1000).toISOString()
    );

    // Store the updated user
    await userManager.storeUser(currentUser);

    // Update the auth token in HTTP client
    setAuthToken(currentUser.access_token);

    return currentUser;
  } catch (error) {
    console.error('[TokenRefresh] Failed to update user with new token:', error);
    return null;
  }
};
