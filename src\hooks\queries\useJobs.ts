import { useQuery } from '@tanstack/react-query';
import { fetchJobs } from '@/services/api/index';
import { Job } from '@/utils/types';

/**
 * Custom hook to fetch and cache jobs for a component
 * @param componentId - The ID of the component to fetch jobs for
 * @param enabled - Whether the query should run automatically
 */
export const useJobs = (componentId: string | null, enabled = true) => {
  return useQuery<{ jobs: Job[] }, Error>({
    queryKey: ['componentJobs', componentId],
    queryFn: () => {
      if (!componentId) {
        throw new Error('No component ID provided to useJobs hook');
      }
      // Validate the component ID parameter
      if (componentId.trim() === '') {
        throw new Error('Empty component ID provided to use<PERSON>ob<PERSON> hook');
      }
      return fetchJobs(componentId);
    },
    enabled: !!componentId && enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};
