import React, { useState } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import ArrowIcon from '@/components/shared/ArrowIcon';

interface HeaderCellProps {
  title: string;
  icon?: string;
}

const HeaderCell: React.FC<HeaderCellProps> = ({ title, icon }) => (
  <div className={'flex items-center justify-start gap-1'}>
    {icon && (
      <span className={'max-w-4 min-w-4'}>
        <img src={icon} alt="" width="16" height="16" aria-hidden="true" />
      </span>
    )}
    <span>{title}</span>
  </div>
);

interface GenericTableProps<TData> {
  data: TData[];
  columns: ColumnDef<TData>[];
  getRowCanExpand?: (row: TData) => boolean;
  renderSubComponent?: (row: TData) => React.ReactNode;
  onRowClick?: (row: TData) => void;
  tableIsFixed?: boolean;
  highlightCurrentRow?: boolean;
  className?: string;
}

function GenericTable<TData>({
  data,
  columns,
  getRowCanExpand,
  renderSubComponent,
  onRowClick,
  className,
  tableIsFixed = true,
  highlightCurrentRow = false,
}: GenericTableProps<TData>) {
  const [currentRow, setCurrentRow] = useState<string>('');
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getRowCanExpand: getRowCanExpand ? row => getRowCanExpand(row.original) : undefined,
  });

  const rowsAreExpandable = onRowClick || renderSubComponent;
  const fixationString = tableIsFixed ? ' table-fixed' : '';
  const isCurrentRow = (row: string) => {
    return highlightCurrentRow && row == currentRow;
  };

  const expandCollapseIconForRow = (row: any) => {
    if (!rowsAreExpandable) return '\u00A0';
    const rowCanExpand = getRowCanExpand && getRowCanExpand(row.original);
    const expandIcon = (
      <ArrowIcon
        direction={row.getIsExpanded() && renderSubComponent ? 'down' : 'right'}
        className="flex-shrink-0"
        size={12}
      />
    );
    return <td className={'pl-2 w-12'}>{rowCanExpand ? expandIcon : '\u00A0'}</td>;
  };

  const visibleColumnsOfRow = (row: any) => {
    return row.getVisibleCells().length + (rowsAreExpandable ? 1 : 0);
  };

  return (
    <div className={'transition ease-in-out delay-150 ' + (className || '')}>
      <table className={'h-full w-full border-separate border-spacing-0' + fixationString}>
        <thead>
          {table.getHeaderGroups().map(headerGroup => (
            <tr key={headerGroup.id} className={'h-12'}>
              {rowsAreExpandable && <th className="w-12" />}
              {headerGroup.headers.map(header => (
                <th
                  key={header.id}
                  className={
                    'overflow-hidden whitespace-nowrap text-ellipsis h-12 font-bold text-left'
                  }
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(header.column.columnDef.header, header.getContext())}
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.map(row => (
            <React.Fragment key={row.id}>
              <tr
                className={
                  isCurrentRow(row.id)
                    ? 'h-12 bg-selected-background'
                    : 'h-12 odd:bg-secondary-background'
                }
                onClick={() => {
                  if (onRowClick || renderSubComponent) {
                    if (getRowCanExpand && getRowCanExpand(row.original)) {
                      row.toggleExpanded();
                    }
                    setCurrentRow(row.id);
                    onRowClick?.(row.original);
                  }
                }}
              >
                {rowsAreExpandable && expandCollapseIconForRow(row)}
                {row.getVisibleCells().map(cell => (
                  <td
                    key={cell.id}
                    className={'overflow-hidden whitespace-nowrap text-ellipsis h-12'}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
              {row.getIsExpanded() && renderSubComponent && (
                <tr>
                  <td colSpan={visibleColumnsOfRow(row)} className={'h-auto'}>
                    {renderSubComponent(row.original)}
                  </td>
                </tr>
              )}
            </React.Fragment>
          ))}
        </tbody>
      </table>
    </div>
  );
}

export { GenericTable, HeaderCell };
export type { GenericTableProps, HeaderCellProps };
