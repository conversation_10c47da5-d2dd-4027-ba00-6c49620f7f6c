/* Local Titillium Web font */
html,
body {
  background: #fff !important;
}
@font-face {
  font-family: 'Titillium Web';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('./assets/fonts/titillium-web/titillium-web-400-normal.woff2') format('woff2');
}

@font-face {
  font-family: 'Titillium Web';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('./assets/fonts/titillium-web/titillium-web-600-normal.woff2') format('woff2');
}

@font-face {
  font-family: 'Titillium Web';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('./assets/fonts/titillium-web/titillium-web-700-normal.woff2') format('woff2');
}

@tailwind base;
@tailwind components;

/* global.css or index.css */
:root {
  --white: #fff;
  --light-gray: #f2f2f2;
  --dark-gray: #d9d9d9;

  --green: #92d14f;
  --yellow: #f0d22b;
  --red: #ff5d5b;
  --blue: var(--theme-primary-light-blue);

  --theme-primary-blue: #0c4a82;
  --theme-primary-light-blue: #009eee;
  --theme-primary-dark-blue: #142f4e;
  --theme-secondary-yellow: #f0d22b;

  --theme-primary-background: var(--white);
  --theme-secondary-background: #d8e5f0;
  --theme-emphasized-background: #bacddc;
  --theme-selected-background: #bdcddb;

  --semi-transparent: #00000088;

  /* Font Sizes */
  --font-tiny: 10px;
  --font-small: 12px;
  --font-medium: 16px;
  --font-large: 20px;
  --font-xlarge: 24px;
  --font-xxlarge: 36px;
  --font-xxxlarge: 48px;

  /* Spacing */
  --spacing-small: 6px;
  --spacing-medium: 12px;
  --spacing-large: 24px;
  --spacing-x-large: 36px;
  --spacing-xx-large: 48px;
  --spacing-xxx-large: 60px;

  --line-thin: 1px solid var(--theme-emphasized-background);
  --line-thick: 2px solid var(--theme-emphasized-background);

  --line-thin-dark: 1px solid var(--dark-gray);
  --line-thick-dark: 2px solid var(--dark-gray);

  --font-family: 'Titillium Web', sans-serif;
  --padding-standard: var(--spacing-medium);
  --font-size-standard: var(--font-medium);

  .common-tooltip {
    opacity: 1;
    padding: 0;
  }
}

/* Add default text color */
@layer base {
  html {
    @apply text-primary;
  }
}

@layer components {
  .thin-border {
    border: var(--line-thin);
  }

  .thick-border {
    border: var(--line-thick);
  }

  .thin-border-dark {
    border: var(--line-thin-dark);
  }

  .thick-border-dark {
    border: var(--line-thick-dark);
  }

  .head-line-1 {
    font-weight: bold;
    font-size: var(--font-large);
    color: #0b4a82;
  }

  .head-line-2 {
    font-weight: bold;
    text-transform: uppercase;
    color: var(--theme-primary-blue);
  }

  .generic-header {
    font-weight: bold;
    color: var(--primary-color);
  }

  .table-header {
    @extend .generic-header;
    text-transform: uppercase;
  }

  .details-container {
    //margin-left: var(--spacing-large);
    margin-right: var(--spacing-small);
  }

  .details-grid {
    display: grid;
    gap: var(--spacing-medium);
    border: var(--line-thin);
    padding: var(--spacing-large);
  }

  .details-grid-2 {
    @extend .details-grid;
    grid-template-columns: auto auto;
  }

  .details-grid-3 {
    @extend .details-grid;
    grid-template-columns: auto auto auto;
  }

  .details-grid-4 {
    @extend .details-grid;
    grid-template-columns: auto auto auto auto;
  }

  .box {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    padding: var(--spacing-medium);
    height: 9em;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .box-left {
    @extend .box;
    justify-content: flex-end;
    align-items: start;
    text-align: left;
  }

  .box-right {
    @extend .box;
    justify-content: flex-end;
    align-items: end;
    text-align: right;
  }

  .compare-logo {
    position: absolute;
    top: 25%;
    left: 50%;
    transform: translate(-50%, -5%);
    color: var(--primary-color);
    width: 5rem;
    height: 5rem;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .thin-bottom-line {
    border-bottom: var(--line-thin);
  }

  .thick-bottom-line {
    border-bottom: var(--line-thick);
  }

  .thin-bottom-line-dark {
    border-bottom: var(--line-thin-dark);
  }

  .thick-bottom-line-dark {
    border-bottom: var(--line-thick-dark);
  }

  .max-overlay-width {
    max-width: min(90%, 1000px);
  }

  .max-overlay-height {
    max-height: 90vh;
  }

  .no-scroll {
    scrollbar-width: none;
  }

  .scroll-box {
    overflow: auto;
    padding-right: 25px;
  }

  .scroll-box::-webkit-scrollbar {
    background: var(--theme-primary-background);
    border: 1px solid var(--theme-primary-blue);
    border-radius: 25px;
    width: 8px;
  }
  .scroll-box::-webkit-scrollbar-thumb {
    background: var(--theme-primary-blue);
    border-radius: 25px;
  }

  .spinner-animation {
    animation: spin 1s linear infinite;
    color: #007bff; /* Replace with a specific color value */
    @keyframes spin {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
  }

  .header-gradient {
    background: radial-gradient(
      at 0% 20%,
      var(--theme-primary-blue) 30%,
      var(--theme-primary-dark-blue) 70%
    );
  }

  /* Flex utility classes to reduce repetition */
  .flex-center {
    @apply flex items-center justify-center;
  }

  .flex-center-col {
    @apply flex flex-col items-center justify-center;
  }

  .flex-between {
    @apply flex items-center justify-between;
  }

  .flex-start {
    @apply flex items-center justify-start;
  }

  .flex-end {
    @apply flex items-center justify-end;
  }

  /* Spinner utility classes for consistency */
  .spinner-primary {
    @apply animate-spin rounded-full border-2 border-gray-200 border-t-primary;
  }

  .spinner-sm {
    @apply h-4 w-4;
  }

  .spinner-md {
    @apply h-6 w-6;
  }

  .spinner-lg {
    @apply h-8 w-8;
  }
}

/* Reset margin and apply global font */
body {
  background: var(--theme-primary-dark-blue);
  margin: 0;
  font-family: var(--font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  scrollbar-width: none;
  -ms-overflow-style: none; /* IE and Edge */
  overflow: none;
}

body::-webkit-scrollbar {
  display: none;
}

/* Tree-specific scroll container with visible scrollbar */
.tree-scroll-container {
  overflow: auto !important;
  padding-right: 8px;
  /* Show scrollbar only when content overflows */
}

.tree-scroll-container::-webkit-scrollbar {
  background: var(--theme-primary-background);
  border: 1px solid var(--theme-primary-blue);
  border-radius: 4px;
  width: 12px;
}

.tree-scroll-container::-webkit-scrollbar-track {
  background: var(--theme-primary-background);
  border-radius: 4px;
}

.tree-scroll-container::-webkit-scrollbar-thumb {
  background: var(--theme-primary-blue);
  border-radius: 4px;
  border: 1px solid var(--theme-primary-background);
  min-height: 20px;
}

.tree-scroll-container::-webkit-scrollbar-thumb:hover {
  background: var(--theme-primary-dark-blue);
}

/* Firefox scrollbar styling */
.tree-scroll-container {
  scrollbar-width: auto; /* Show scrollbar only when needed */
  scrollbar-color: var(--theme-primary-blue) var(--theme-primary-background);
}

/* React Arborist specific scrollbar overrides */
.tree-scroll-container [role='tree'] {
  overflow: auto !important;
}

.tree-scroll-container [role='tree']::-webkit-scrollbar {
  background: var(--theme-primary-background);
  border: 1px solid var(--theme-primary-blue);
  border-radius: 4px;
  width: 12px;
}

.tree-scroll-container [role='tree']::-webkit-scrollbar-thumb {
  background: var(--theme-primary-blue);
  border-radius: 4px;
  border: 1px solid var(--theme-primary-background);
  min-height: 20px;
}

/* Blue collar selection indicator for tree nodes */
.tree-node-selected-indicator {
  position: absolute;
  left: 0;
  top: 0;
  width: 6px;
  height: 100%;
  background-color: var(--theme-primary-blue);
  border-radius: 0 2px 2px 0;
  z-index: 10;
}
