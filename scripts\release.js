#!/usr/bin/env node

/**
 * Release script for Octoplant Web Application
 * Handles semantic versioning and release preparation
 */

import { execSync } from 'child_process';
import { readFileSync, writeFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function error(message) {
  log(`❌ ${message}`, colors.red);
}

function success(message) {
  log(`✅ ${message}`, colors.green);
}

function info(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function warning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function execCommand(command, options = {}) {
  try {
    const result = execSync(command, {
      encoding: 'utf8',
      cwd: rootDir,
      stdio: 'pipe',
      ...options,
    });
    return result.trim();
  } catch (err) {
    throw new Error(`Command failed: ${command}\n${err.message}`);
  }
}

function getCurrentVersion() {
  const packageJson = JSON.parse(readFileSync(join(rootDir, 'package.json'), 'utf8'));
  return packageJson.version;
}

function updateChangelog(version, releaseType) {
  const changelogPath = join(rootDir, 'CHANGELOG.md');
  const changelog = readFileSync(changelogPath, 'utf8');

  const today = new Date().toISOString().split('T')[0];
  const versionHeader = `## [${version}] - ${today}`;

  // Replace [Unreleased] with the new version
  const updatedChangelog = changelog.replace(
    '## [Unreleased]',
    `## [Unreleased]\n\n${versionHeader}`
  );

  writeFileSync(changelogPath, updatedChangelog);
  success(`Updated CHANGELOG.md with version ${version}`);
}

function validateWorkingDirectory() {
  try {
    const status = execCommand('git status --porcelain');
    if (status) {
      error('Working directory is not clean. Please commit or stash your changes.');
      process.exit(1);
    }
    success('Working directory is clean');
  } catch (err) {
    error('Failed to check git status');
    process.exit(1);
  }
}

function runPreReleaseChecks() {
  info('Running pre-release checks...');

  try {
    // Run linting
    info('Running ESLint...');
    execCommand('npm run lint:check');
    success('Linting passed');

    // Run formatting check
    info('Checking code formatting...');
    execCommand('npm run format:check');
    success('Code formatting is correct');

    // Run build
    info('Building application...');
    execCommand('npm run build');
    success('Build completed successfully');
  } catch (err) {
    error(`Pre-release checks failed: ${err.message}`);
    process.exit(1);
  }
}

function createRelease(releaseType) {
  const currentVersion = getCurrentVersion();
  info(`Current version: ${currentVersion}`);

  // Validate inputs
  if (!['patch', 'minor', 'major'].includes(releaseType)) {
    error('Release type must be one of: patch, minor, major');
    process.exit(1);
  }

  // Validate working directory
  validateWorkingDirectory();

  // Run pre-release checks
  runPreReleaseChecks();

  // Bump version
  info(`Bumping ${releaseType} version...`);
  const newVersion = execCommand(`npm version ${releaseType} --no-git-tag-version`).replace(
    'v',
    ''
  );
  success(`Version bumped to ${newVersion}`);

  // Update changelog
  updateChangelog(newVersion, releaseType);

  // Commit changes
  info('Committing version changes...');
  execCommand('git add package.json package-lock.json CHANGELOG.md');
  execCommand(`git commit -m "chore: release v${newVersion}"`);

  // Create git tag
  info('Creating git tag...');
  execCommand(`git tag -a v${newVersion} -m "Release v${newVersion}"`);

  success(`Release v${newVersion} created successfully!`);

  // Show next steps
  log('\n' + colors.bright + 'Next steps:' + colors.reset);
  log(`1. Review the changes: git show v${newVersion}`);
  log(`2. Push to remote: git push origin main --tags`);
  log(`3. GitLab CI will automatically build and deploy the release`);

  return newVersion;
}

// Main execution
const args = process.argv.slice(2);
const releaseType = args[0];

if (!releaseType) {
  log(colors.bright + 'Octoplant Web Application Release Tool' + colors.reset);
  log('\nUsage: node scripts/release.js <release-type>');
  log('\nRelease types:');
  log('  patch  - Bug fixes and maintenance (0.1.0 → 0.1.1)');
  log('  minor  - New features and enhancements (0.1.0 → 0.2.0)');
  log('  major  - Breaking changes (0.1.0 → 1.0.0)');
  log('\nExample: node scripts/release.js minor');
  process.exit(1);
}

try {
  createRelease(releaseType);
} catch (err) {
  error(`Release failed: ${err.message}`);
  process.exit(1);
}
