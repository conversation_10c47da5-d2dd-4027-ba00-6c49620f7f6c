var en=Object.defineProperty;var tn=(t,e,r)=>e in t?en(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r;var vt=(t,e,r)=>tn(t,typeof e!="symbol"?e+"":e,r);import{j as l,A as rn,C as cr,T as ve,u as lr,f as Xe,a as mt,b as Qe,c as nn,d as an,s as on,e as sn,g as un,i as cn,F as ln,V as dn,R as yt}from"./index-CRLxp3KJ.js";import{r as d,g as fn,R as le}from"./vendor-CJBOjUNh.js";var dr={exports:{}},fr={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ue=d;function hn(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var gn=typeof Object.is=="function"?Object.is:hn,pn=ue.useState,vn=ue.useEffect,mn=ue.useLayoutEffect,yn=ue.useDebugValue;function bn(t,e){var r=e(),n=pn({inst:{value:r,getSnapshot:e}}),i=n[0].inst,a=n[1];return mn(function(){i.value=r,i.getSnapshot=e,Ae(i)&&a({inst:i})},[t,r,e]),vn(function(){return Ae(i)&&a({inst:i}),t(function(){Ae(i)&&a({inst:i})})},[t]),yn(r),r}function Ae(t){var e=t.getSnapshot;t=t.value;try{var r=e();return!gn(t,r)}catch{return!0}}function Sn(t,e){return e()}var Dn=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?Sn:bn;fr.useSyncExternalStore=ue.useSyncExternalStore!==void 0?ue.useSyncExternalStore:Dn;dr.exports=fr;var wn=dr.exports;function hr(t){var e=null,r=function(){return e==null&&(e=t()),e};return r}function In(t,e){return t.filter(function(r){return r!==e})}function On(t,e){var r=new Set,n=function(o){return r.add(o)};t.forEach(n),e.forEach(n);var i=[];return r.forEach(function(a){return i.push(a)}),i}function Tn(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function xn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function Cn(t,e,r){return e&&xn(t.prototype,e),t}function bt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var En=function(){function t(e){Tn(this,t),bt(this,"entered",[]),bt(this,"isNodeInDocument",void 0),this.isNodeInDocument=e}return Cn(t,[{key:"enter",value:function(r){var n=this,i=this.entered.length,a=function(u){return n.isNodeInDocument(u)&&(!u.contains||u.contains(r))};return this.entered=On(this.entered.filter(a),[r]),i===0&&this.entered.length>0}},{key:"leave",value:function(r){var n=this.entered.length;return this.entered=In(this.entered.filter(this.isNodeInDocument),r),n>0&&this.entered.length===0}},{key:"reset",value:function(){this.entered=[]}}]),t}(),$n=hr(function(){return/firefox/i.test(navigator.userAgent)}),gr=hr(function(){return!!window.safari});function Nn(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _n(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function Pn(t,e,r){return e&&_n(t.prototype,e),t}function de(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var St=function(){function t(e,r){Nn(this,t),de(this,"xs",void 0),de(this,"ys",void 0),de(this,"c1s",void 0),de(this,"c2s",void 0),de(this,"c3s",void 0);for(var n=e.length,i=[],a=0;a<n;a++)i.push(a);i.sort(function(R,$){return e[R]<e[$]?-1:1});for(var o=[],u=[],s,f,h=0;h<n-1;h++)s=e[h+1]-e[h],f=r[h+1]-r[h],o.push(s),u.push(f/s);for(var v=[u[0]],b=0;b<o.length-1;b++){var m=u[b],w=u[b+1];if(m*w<=0)v.push(0);else{s=o[b];var c=o[b+1],g=s+c;v.push(3*g/((g+c)/m+(g+s)/w))}}v.push(u[u.length-1]);for(var p=[],y=[],I,O=0;O<v.length-1;O++){I=u[O];var D=v[O],T=1/o[O],S=D+v[O+1]-I-I;p.push((I-D-S)*T),y.push(S*T*T)}this.xs=e,this.ys=r,this.c1s=v,this.c2s=p,this.c3s=y}return Pn(t,[{key:"interpolate",value:function(r){var n=this.xs,i=this.ys,a=this.c1s,o=this.c2s,u=this.c3s,s=n.length-1;if(r===n[s])return i[s];for(var f=0,h=u.length-1,v;f<=h;){v=Math.floor(.5*(f+h));var b=n[v];if(b<r)f=v+1;else if(b>r)h=v-1;else return i[v]}s=Math.max(0,h);var m=r-n[s],w=m*m;return i[s]+a[s]*m+o[s]*w+u[s]*m*w}}]),t}(),kn=1;function pr(t){var e=t.nodeType===kn?t:t.parentElement;if(!e)return null;var r=e.getBoundingClientRect(),n=r.top,i=r.left;return{x:i,y:n}}function be(t){return{x:t.clientX,y:t.clientY}}function Rn(t){var e;return t.nodeName==="IMG"&&($n()||!((e=document.documentElement)!==null&&e!==void 0&&e.contains(t)))}function An(t,e,r,n){var i=t?e.width:r,a=t?e.height:n;return gr()&&t&&(a/=window.devicePixelRatio,i/=window.devicePixelRatio),{dragPreviewWidth:i,dragPreviewHeight:a}}function Mn(t,e,r,n,i){var a=Rn(e),o=a?t:e,u=pr(o),s={x:r.x-u.x,y:r.y-u.y},f=t.offsetWidth,h=t.offsetHeight,v=n.anchorX,b=n.anchorY,m=An(a,e,f,h),w=m.dragPreviewWidth,c=m.dragPreviewHeight,g=function(){var S=new St([0,.5,1],[s.y,s.y/h*c,s.y+c-h]),R=S.interpolate(b);return gr()&&a&&(R+=(window.devicePixelRatio-1)*c),R},p=function(){var S=new St([0,.5,1],[s.x,s.x/f*w,s.x+w-f]);return S.interpolate(v)},y=i.offsetX,I=i.offsetY,O=y===0||y,D=I===0||I;return{x:O?y:p(),y:D?I:g()}}var vr="__NATIVE_FILE__",mr="__NATIVE_URL__",yr="__NATIVE_TEXT__",br="__NATIVE_HTML__";const Dt=Object.freeze(Object.defineProperty({__proto__:null,FILE:vr,HTML:br,TEXT:yr,URL:mr},Symbol.toStringTag,{value:"Module"}));function Me(t,e,r){var n=e.reduce(function(i,a){return i||t.getData(a)},"");return n??r}var ne;function Se(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Je=(ne={},Se(ne,vr,{exposeProperties:{files:function(e){return Array.prototype.slice.call(e.files)},items:function(e){return e.items},dataTransfer:function(e){return e}},matchesTypes:["Files"]}),Se(ne,br,{exposeProperties:{html:function(e,r){return Me(e,r,"")},dataTransfer:function(e){return e}},matchesTypes:["Html","text/html"]}),Se(ne,mr,{exposeProperties:{urls:function(e,r){return Me(e,r,"").split(`
`)},dataTransfer:function(e){return e}},matchesTypes:["Url","text/uri-list"]}),Se(ne,yr,{exposeProperties:{text:function(e,r){return Me(e,r,"")},dataTransfer:function(e){return e}},matchesTypes:["Text","text/plain"]}),ne);function Ln(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function jn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function Hn(t,e,r){return e&&jn(t.prototype,e),t}function wt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Fn=function(){function t(e){Ln(this,t),wt(this,"item",void 0),wt(this,"config",void 0),this.config=e,this.item={},this.initializeExposedProperties()}return Hn(t,[{key:"initializeExposedProperties",value:function(){var r=this;Object.keys(this.config.exposeProperties).forEach(function(n){Object.defineProperty(r.item,n,{configurable:!0,enumerable:!0,get:function(){return console.warn(`Browser doesn't allow reading "`.concat(n,'" until the drop event.')),null}})})}},{key:"loadDataTransfer",value:function(r){var n=this;if(r){var i={};Object.keys(this.config.exposeProperties).forEach(function(a){i[a]={value:n.config.exposeProperties[a](r,n.config.matchesTypes),configurable:!0,enumerable:!0}}),Object.defineProperties(this.item,i)}}},{key:"canDrag",value:function(){return!0}},{key:"beginDrag",value:function(){return this.item}},{key:"isDragging",value:function(r,n){return n===r.getSourceId()}},{key:"endDrag",value:function(){}}]),t}();function zn(t,e){var r=new Fn(Je[t]);return r.loadDataTransfer(e),r}function Le(t){if(!t)return null;var e=Array.prototype.slice.call(t.types||[]);return Object.keys(Je).filter(function(r){var n=Je[r].matchesTypes;return n.some(function(i){return e.indexOf(i)>-1})})[0]||null}function Un(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Wn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function Bn(t,e,r){return e&&Wn(t.prototype,e),t}function je(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var qn=function(){function t(e,r){Un(this,t),je(this,"ownerDocument",null),je(this,"globalContext",void 0),je(this,"optionsArgs",void 0),this.globalContext=e,this.optionsArgs=r}return Bn(t,[{key:"window",get:function(){if(this.globalContext)return this.globalContext;if(typeof window<"u")return window}},{key:"document",get:function(){var r;return(r=this.globalContext)!==null&&r!==void 0&&r.document?this.globalContext.document:this.window?this.window.document:void 0}},{key:"rootElement",get:function(){var r;return((r=this.optionsArgs)===null||r===void 0?void 0:r.rootElement)||this.window}}]),t}();function It(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Ot(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?It(Object(r),!0).forEach(function(n){N(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):It(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Vn(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Gn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function Kn(t,e,r){return e&&Gn(t.prototype,e),t}function N(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Yn=function(){function t(e,r,n){var i=this;Vn(this,t),N(this,"options",void 0),N(this,"actions",void 0),N(this,"monitor",void 0),N(this,"registry",void 0),N(this,"enterLeaveCounter",void 0),N(this,"sourcePreviewNodes",new Map),N(this,"sourcePreviewNodeOptions",new Map),N(this,"sourceNodes",new Map),N(this,"sourceNodeOptions",new Map),N(this,"dragStartSourceIds",null),N(this,"dropTargetIds",[]),N(this,"dragEnterTargetIds",[]),N(this,"currentNativeSource",null),N(this,"currentNativeHandle",null),N(this,"currentDragSourceNode",null),N(this,"altKeyPressed",!1),N(this,"mouseMoveTimeoutTimer",null),N(this,"asyncEndDragFrameId",null),N(this,"dragOverTargetIds",null),N(this,"lastClientOffset",null),N(this,"hoverRafId",null),N(this,"getSourceClientOffset",function(a){var o=i.sourceNodes.get(a);return o&&pr(o)||null}),N(this,"endDragNativeItem",function(){i.isDraggingNativeItem()&&(i.actions.endDrag(),i.currentNativeHandle&&i.registry.removeSource(i.currentNativeHandle),i.currentNativeHandle=null,i.currentNativeSource=null)}),N(this,"isNodeInDocument",function(a){return!!(a&&i.document&&i.document.body&&i.document.body.contains(a))}),N(this,"endDragIfSourceWasRemovedFromDOM",function(){var a=i.currentDragSourceNode;a==null||i.isNodeInDocument(a)||i.clearCurrentDragSourceNode()&&i.monitor.isDragging()&&i.actions.endDrag()}),N(this,"handleTopDragStartCapture",function(){i.clearCurrentDragSourceNode(),i.dragStartSourceIds=[]}),N(this,"handleTopDragStart",function(a){if(!a.defaultPrevented){var o=i.dragStartSourceIds;i.dragStartSourceIds=null;var u=be(a);i.monitor.isDragging()&&i.actions.endDrag(),i.actions.beginDrag(o||[],{publishSource:!1,getSourceClientOffset:i.getSourceClientOffset,clientOffset:u});var s=a.dataTransfer,f=Le(s);if(i.monitor.isDragging()){if(s&&typeof s.setDragImage=="function"){var h=i.monitor.getSourceId(),v=i.sourceNodes.get(h),b=i.sourcePreviewNodes.get(h)||v;if(b){var m=i.getCurrentSourcePreviewNodeOptions(),w=m.anchorX,c=m.anchorY,g=m.offsetX,p=m.offsetY,y={anchorX:w,anchorY:c},I={offsetX:g,offsetY:p},O=Mn(v,b,u,y,I);s.setDragImage(b,O.x,O.y)}}try{s==null||s.setData("application/json",{})}catch{}i.setCurrentDragSourceNode(a.target);var D=i.getCurrentSourcePreviewNodeOptions(),T=D.captureDraggingState;T?i.actions.publishDragSource():setTimeout(function(){return i.actions.publishDragSource()},0)}else if(f)i.beginDragNativeItem(f);else{if(s&&!s.types&&(a.target&&!a.target.hasAttribute||!a.target.hasAttribute("draggable")))return;a.preventDefault()}}}),N(this,"handleTopDragEndCapture",function(){i.clearCurrentDragSourceNode()&&i.monitor.isDragging()&&i.actions.endDrag()}),N(this,"handleTopDragEnterCapture",function(a){i.dragEnterTargetIds=[];var o=i.enterLeaveCounter.enter(a.target);if(!(!o||i.monitor.isDragging())){var u=a.dataTransfer,s=Le(u);s&&i.beginDragNativeItem(s,u)}}),N(this,"handleTopDragEnter",function(a){var o=i.dragEnterTargetIds;if(i.dragEnterTargetIds=[],!!i.monitor.isDragging()){i.altKeyPressed=a.altKey,o.length>0&&i.actions.hover(o,{clientOffset:be(a)});var u=o.some(function(s){return i.monitor.canDropOnTarget(s)});u&&(a.preventDefault(),a.dataTransfer&&(a.dataTransfer.dropEffect=i.getCurrentDropEffect()))}}),N(this,"handleTopDragOverCapture",function(){i.dragOverTargetIds=[]}),N(this,"handleTopDragOver",function(a){var o=i.dragOverTargetIds;if(i.dragOverTargetIds=[],!i.monitor.isDragging()){a.preventDefault(),a.dataTransfer&&(a.dataTransfer.dropEffect="none");return}i.altKeyPressed=a.altKey,i.lastClientOffset=be(a),i.hoverRafId===null&&typeof requestAnimationFrame<"u"&&(i.hoverRafId=requestAnimationFrame(function(){i.monitor.isDragging()&&i.actions.hover(o||[],{clientOffset:i.lastClientOffset}),i.hoverRafId=null}));var u=(o||[]).some(function(s){return i.monitor.canDropOnTarget(s)});u?(a.preventDefault(),a.dataTransfer&&(a.dataTransfer.dropEffect=i.getCurrentDropEffect())):i.isDraggingNativeItem()?a.preventDefault():(a.preventDefault(),a.dataTransfer&&(a.dataTransfer.dropEffect="none"))}),N(this,"handleTopDragLeaveCapture",function(a){i.isDraggingNativeItem()&&a.preventDefault();var o=i.enterLeaveCounter.leave(a.target);o&&i.isDraggingNativeItem()&&setTimeout(function(){return i.endDragNativeItem()},0)}),N(this,"handleTopDropCapture",function(a){if(i.dropTargetIds=[],i.isDraggingNativeItem()){var o;a.preventDefault(),(o=i.currentNativeSource)===null||o===void 0||o.loadDataTransfer(a.dataTransfer)}else Le(a.dataTransfer)&&a.preventDefault();i.enterLeaveCounter.reset()}),N(this,"handleTopDrop",function(a){var o=i.dropTargetIds;i.dropTargetIds=[],i.actions.hover(o,{clientOffset:be(a)}),i.actions.drop({dropEffect:i.getCurrentDropEffect()}),i.isDraggingNativeItem()?i.endDragNativeItem():i.monitor.isDragging()&&i.actions.endDrag()}),N(this,"handleSelectStart",function(a){var o=a.target;typeof o.dragDrop=="function"&&(o.tagName==="INPUT"||o.tagName==="SELECT"||o.tagName==="TEXTAREA"||o.isContentEditable||(a.preventDefault(),o.dragDrop()))}),this.options=new qn(r,n),this.actions=e.getActions(),this.monitor=e.getMonitor(),this.registry=e.getRegistry(),this.enterLeaveCounter=new En(this.isNodeInDocument)}return Kn(t,[{key:"profile",value:function(){var r,n;return{sourcePreviewNodes:this.sourcePreviewNodes.size,sourcePreviewNodeOptions:this.sourcePreviewNodeOptions.size,sourceNodeOptions:this.sourceNodeOptions.size,sourceNodes:this.sourceNodes.size,dragStartSourceIds:((r=this.dragStartSourceIds)===null||r===void 0?void 0:r.length)||0,dropTargetIds:this.dropTargetIds.length,dragEnterTargetIds:this.dragEnterTargetIds.length,dragOverTargetIds:((n=this.dragOverTargetIds)===null||n===void 0?void 0:n.length)||0}}},{key:"window",get:function(){return this.options.window}},{key:"document",get:function(){return this.options.document}},{key:"rootElement",get:function(){return this.options.rootElement}},{key:"setup",value:function(){var r=this.rootElement;if(r!==void 0){if(r.__isReactDndBackendSetUp)throw new Error("Cannot have two HTML5 backends at the same time.");r.__isReactDndBackendSetUp=!0,this.addEventListeners(r)}}},{key:"teardown",value:function(){var r=this.rootElement;if(r!==void 0&&(r.__isReactDndBackendSetUp=!1,this.removeEventListeners(this.rootElement),this.clearCurrentDragSourceNode(),this.asyncEndDragFrameId)){var n;(n=this.window)===null||n===void 0||n.cancelAnimationFrame(this.asyncEndDragFrameId)}}},{key:"connectDragPreview",value:function(r,n,i){var a=this;return this.sourcePreviewNodeOptions.set(r,i),this.sourcePreviewNodes.set(r,n),function(){a.sourcePreviewNodes.delete(r),a.sourcePreviewNodeOptions.delete(r)}}},{key:"connectDragSource",value:function(r,n,i){var a=this;this.sourceNodes.set(r,n),this.sourceNodeOptions.set(r,i);var o=function(f){return a.handleDragStart(f,r)},u=function(f){return a.handleSelectStart(f)};return n.setAttribute("draggable","true"),n.addEventListener("dragstart",o),n.addEventListener("selectstart",u),function(){a.sourceNodes.delete(r),a.sourceNodeOptions.delete(r),n.removeEventListener("dragstart",o),n.removeEventListener("selectstart",u),n.setAttribute("draggable","false")}}},{key:"connectDropTarget",value:function(r,n){var i=this,a=function(f){return i.handleDragEnter(f,r)},o=function(f){return i.handleDragOver(f,r)},u=function(f){return i.handleDrop(f,r)};return n.addEventListener("dragenter",a),n.addEventListener("dragover",o),n.addEventListener("drop",u),function(){n.removeEventListener("dragenter",a),n.removeEventListener("dragover",o),n.removeEventListener("drop",u)}}},{key:"addEventListeners",value:function(r){r.addEventListener&&(r.addEventListener("dragstart",this.handleTopDragStart),r.addEventListener("dragstart",this.handleTopDragStartCapture,!0),r.addEventListener("dragend",this.handleTopDragEndCapture,!0),r.addEventListener("dragenter",this.handleTopDragEnter),r.addEventListener("dragenter",this.handleTopDragEnterCapture,!0),r.addEventListener("dragleave",this.handleTopDragLeaveCapture,!0),r.addEventListener("dragover",this.handleTopDragOver),r.addEventListener("dragover",this.handleTopDragOverCapture,!0),r.addEventListener("drop",this.handleTopDrop),r.addEventListener("drop",this.handleTopDropCapture,!0))}},{key:"removeEventListeners",value:function(r){r.removeEventListener&&(r.removeEventListener("dragstart",this.handleTopDragStart),r.removeEventListener("dragstart",this.handleTopDragStartCapture,!0),r.removeEventListener("dragend",this.handleTopDragEndCapture,!0),r.removeEventListener("dragenter",this.handleTopDragEnter),r.removeEventListener("dragenter",this.handleTopDragEnterCapture,!0),r.removeEventListener("dragleave",this.handleTopDragLeaveCapture,!0),r.removeEventListener("dragover",this.handleTopDragOver),r.removeEventListener("dragover",this.handleTopDragOverCapture,!0),r.removeEventListener("drop",this.handleTopDrop),r.removeEventListener("drop",this.handleTopDropCapture,!0))}},{key:"getCurrentSourceNodeOptions",value:function(){var r=this.monitor.getSourceId(),n=this.sourceNodeOptions.get(r);return Ot({dropEffect:this.altKeyPressed?"copy":"move"},n||{})}},{key:"getCurrentDropEffect",value:function(){return this.isDraggingNativeItem()?"copy":this.getCurrentSourceNodeOptions().dropEffect}},{key:"getCurrentSourcePreviewNodeOptions",value:function(){var r=this.monitor.getSourceId(),n=this.sourcePreviewNodeOptions.get(r);return Ot({anchorX:.5,anchorY:.5,captureDraggingState:!1},n||{})}},{key:"isDraggingNativeItem",value:function(){var r=this.monitor.getItemType();return Object.keys(Dt).some(function(n){return Dt[n]===r})}},{key:"beginDragNativeItem",value:function(r,n){this.clearCurrentDragSourceNode(),this.currentNativeSource=zn(r,n),this.currentNativeHandle=this.registry.addSource(r,this.currentNativeSource),this.actions.beginDrag([this.currentNativeHandle])}},{key:"setCurrentDragSourceNode",value:function(r){var n=this;this.clearCurrentDragSourceNode(),this.currentDragSourceNode=r;var i=1e3;this.mouseMoveTimeoutTimer=setTimeout(function(){var a;return(a=n.rootElement)===null||a===void 0?void 0:a.addEventListener("mousemove",n.endDragIfSourceWasRemovedFromDOM,!0)},i)}},{key:"clearCurrentDragSourceNode",value:function(){if(this.currentDragSourceNode){if(this.currentDragSourceNode=null,this.rootElement){var r;(r=this.window)===null||r===void 0||r.clearTimeout(this.mouseMoveTimeoutTimer||void 0),this.rootElement.removeEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)}return this.mouseMoveTimeoutTimer=null,!0}return!1}},{key:"handleDragStart",value:function(r,n){r.defaultPrevented||(this.dragStartSourceIds||(this.dragStartSourceIds=[]),this.dragStartSourceIds.unshift(n))}},{key:"handleDragEnter",value:function(r,n){this.dragEnterTargetIds.unshift(n)}},{key:"handleDragOver",value:function(r,n){this.dragOverTargetIds===null&&(this.dragOverTargetIds=[]),this.dragOverTargetIds.unshift(n)}},{key:"handleDrop",value:function(r,n){this.dropTargetIds.unshift(n)}}]),t}(),De;function Xn(){return De||(De=new Image,De.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="),De}var Qn=function(e,r,n){return new Yn(e,r,n)},Sr=d.createContext({dragDropManager:void 0}),q;(function(t){t.SOURCE="SOURCE",t.TARGET="TARGET"})(q||(q={}));function C(t,e){for(var r=arguments.length,n=new Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i];if(!t){var a;if(e===void 0)a=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var o=0;a=new Error(e.replace(/%s/g,function(){return n[o++]})),a.name="Invariant Violation"}throw a.framesToPop=1,a}}var st="dnd-core/INIT_COORDS",$e="dnd-core/BEGIN_DRAG",ut="dnd-core/PUBLISH_DRAG_SOURCE",Ne="dnd-core/HOVER",_e="dnd-core/DROP",Pe="dnd-core/END_DRAG";function Tt(t,e){return{type:st,payload:{sourceClientOffset:e||null,clientOffset:t||null}}}function we(t){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?we=function(r){return typeof r}:we=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},we(t)}function Jn(t,e,r){return e.split(".").reduce(function(n,i){return n&&n[i]?n[i]:r||null},t)}function Zn(t,e){return t.filter(function(r){return r!==e})}function Dr(t){return we(t)==="object"}function ei(t,e){var r=new Map,n=function(o){r.set(o,r.has(o)?r.get(o)+1:1)};t.forEach(n),e.forEach(n);var i=[];return r.forEach(function(a,o){a===1&&i.push(o)}),i}function ti(t,e){return t.filter(function(r){return e.indexOf(r)>-1})}var ri={type:st,payload:{clientOffset:null,sourceClientOffset:null}};function ni(t){return function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{publishSource:!0},i=n.publishSource,a=i===void 0?!0:i,o=n.clientOffset,u=n.getSourceClientOffset,s=t.getMonitor(),f=t.getRegistry();t.dispatch(Tt(o)),ii(r,s,f);var h=si(r,s);if(h===null){t.dispatch(ri);return}var v=null;if(o){if(!u)throw new Error("getSourceClientOffset must be defined");ai(u),v=u(h)}t.dispatch(Tt(o,v));var b=f.getSource(h),m=b.beginDrag(s,h);if(m!=null){oi(m),f.pinSource(h);var w=f.getSourceType(h);return{type:$e,payload:{itemType:w,item:m,sourceId:h,clientOffset:o||null,sourceClientOffset:v||null,isSourcePublic:!!a}}}}}function ii(t,e,r){C(!e.isDragging(),"Cannot call beginDrag while dragging."),t.forEach(function(n){C(r.getSource(n),"Expected sourceIds to be registered.")})}function ai(t){C(typeof t=="function","When clientOffset is provided, getSourceClientOffset must be a function.")}function oi(t){C(Dr(t),"Item must be an object.")}function si(t,e){for(var r=null,n=t.length-1;n>=0;n--)if(e.canDragSource(t[n])){r=t[n];break}return r}function ui(t){return function(){var r=t.getMonitor();if(r.isDragging())return{type:ut}}}function Ze(t,e){return e===null?t===null:Array.isArray(t)?t.some(function(r){return r===e}):t===e}function ci(t){return function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.clientOffset;li(r);var a=r.slice(0),o=t.getMonitor(),u=t.getRegistry();di(a,o,u);var s=o.getItemType();return fi(a,u,s),hi(a,o,u),{type:Ne,payload:{targetIds:a,clientOffset:i||null}}}}function li(t){C(Array.isArray(t),"Expected targetIds to be an array.")}function di(t,e,r){C(e.isDragging(),"Cannot call hover while not dragging."),C(!e.didDrop(),"Cannot call hover after drop.");for(var n=0;n<t.length;n++){var i=t[n];C(t.lastIndexOf(i)===n,"Expected targetIds to be unique in the passed array.");var a=r.getTarget(i);C(a,"Expected targetIds to be registered.")}}function fi(t,e,r){for(var n=t.length-1;n>=0;n--){var i=t[n],a=e.getTargetType(i);Ze(a,r)||t.splice(n,1)}}function hi(t,e,r){t.forEach(function(n){var i=r.getTarget(n);i.hover(e,n)})}function xt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Ct(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?xt(Object(r),!0).forEach(function(n){gi(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):xt(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function gi(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pi(t){return function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=t.getMonitor(),i=t.getRegistry();vi(n);var a=bi(n);a.forEach(function(o,u){var s=mi(o,u,i,n),f={type:_e,payload:{dropResult:Ct(Ct({},r),s)}};t.dispatch(f)})}}function vi(t){C(t.isDragging(),"Cannot call drop while not dragging."),C(!t.didDrop(),"Cannot call drop twice during one drag operation.")}function mi(t,e,r,n){var i=r.getTarget(t),a=i?i.drop(n,t):void 0;return yi(a),typeof a>"u"&&(a=e===0?{}:n.getDropResult()),a}function yi(t){C(typeof t>"u"||Dr(t),"Drop result must either be an object or undefined.")}function bi(t){var e=t.getTargetIds().filter(t.canDropOnTarget,t);return e.reverse(),e}function Si(t){return function(){var r=t.getMonitor(),n=t.getRegistry();Di(r);var i=r.getSourceId();if(i!=null){var a=n.getSource(i,!0);a.endDrag(r,i),n.unpinSource()}return{type:Pe}}}function Di(t){C(t.isDragging(),"Cannot call endDrag while not dragging.")}function wi(t){return{beginDrag:ni(t),publishDragSource:ui(t),hover:ci(t),drop:pi(t),endDrag:Si(t)}}function Ii(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Oi(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function Ti(t,e,r){return e&&Oi(t.prototype,e),t}function fe(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var xi=function(){function t(e,r){var n=this;Ii(this,t),fe(this,"store",void 0),fe(this,"monitor",void 0),fe(this,"backend",void 0),fe(this,"isSetUp",!1),fe(this,"handleRefCountChange",function(){var i=n.store.getState().refCount>0;n.backend&&(i&&!n.isSetUp?(n.backend.setup(),n.isSetUp=!0):!i&&n.isSetUp&&(n.backend.teardown(),n.isSetUp=!1))}),this.store=e,this.monitor=r,e.subscribe(this.handleRefCountChange)}return Ti(t,[{key:"receiveBackend",value:function(r){this.backend=r}},{key:"getMonitor",value:function(){return this.monitor}},{key:"getBackend",value:function(){return this.backend}},{key:"getRegistry",value:function(){return this.monitor.registry}},{key:"getActions",value:function(){var r=this,n=this.store.dispatch;function i(o){return function(){for(var u=arguments.length,s=new Array(u),f=0;f<u;f++)s[f]=arguments[f];var h=o.apply(r,s);typeof h<"u"&&n(h)}}var a=wi(this);return Object.keys(a).reduce(function(o,u){var s=a[u];return o[u]=i(s),o},{})}},{key:"dispatch",value:function(r){this.store.dispatch(r)}}]),t}();function z(t){return"Minified Redux error #"+t+"; visit https://redux.js.org/Errors?code="+t+" for the full message or use the non-minified dev environment for full errors. "}var Et=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}(),He=function(){return Math.random().toString(36).substring(7).split("").join(".")},Ce={INIT:"@@redux/INIT"+He(),REPLACE:"@@redux/REPLACE"+He(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+He()}};function Ci(t){if(typeof t!="object"||t===null)return!1;for(var e=t;Object.getPrototypeOf(e)!==null;)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e}function ct(t,e,r){var n;if(typeof e=="function"&&typeof r=="function"||typeof r=="function"&&typeof arguments[3]=="function")throw new Error(z(0));if(typeof e=="function"&&typeof r>"u"&&(r=e,e=void 0),typeof r<"u"){if(typeof r!="function")throw new Error(z(1));return r(ct)(t,e)}if(typeof t!="function")throw new Error(z(2));var i=t,a=e,o=[],u=o,s=!1;function f(){u===o&&(u=o.slice())}function h(){if(s)throw new Error(z(3));return a}function v(c){if(typeof c!="function")throw new Error(z(4));if(s)throw new Error(z(5));var g=!0;return f(),u.push(c),function(){if(g){if(s)throw new Error(z(6));g=!1,f();var y=u.indexOf(c);u.splice(y,1),o=null}}}function b(c){if(!Ci(c))throw new Error(z(7));if(typeof c.type>"u")throw new Error(z(8));if(s)throw new Error(z(9));try{s=!0,a=i(a,c)}finally{s=!1}for(var g=o=u,p=0;p<g.length;p++){var y=g[p];y()}return c}function m(c){if(typeof c!="function")throw new Error(z(10));i=c,b({type:Ce.REPLACE})}function w(){var c,g=v;return c={subscribe:function(y){if(typeof y!="object"||y===null)throw new Error(z(11));function I(){y.next&&y.next(h())}I();var O=g(I);return{unsubscribe:O}}},c[Et]=function(){return this},c}return b({type:Ce.INIT}),n={dispatch:b,subscribe:v,getState:h,replaceReducer:m},n[Et]=w,n}function Ei(t){Object.keys(t).forEach(function(e){var r=t[e],n=r(void 0,{type:Ce.INIT});if(typeof n>"u")throw new Error(z(12));if(typeof r(void 0,{type:Ce.PROBE_UNKNOWN_ACTION()})>"u")throw new Error(z(13))})}function $t(t){for(var e=Object.keys(t),r={},n=0;n<e.length;n++){var i=e[n];typeof t[i]=="function"&&(r[i]=t[i])}var a=Object.keys(r),o;try{Ei(r)}catch(u){o=u}return function(s,f){if(s===void 0&&(s={}),o)throw o;for(var h=!1,v={},b=0;b<a.length;b++){var m=a[b],w=r[m],c=s[m],g=w(c,f);if(typeof g>"u")throw f&&f.type,new Error(z(14));v[m]=g,h=h||g!==c}return h=h||a.length!==Object.keys(s).length,h?v:s}}var $i=function(e,r){return e===r};function Ni(t,e){return!t&&!e?!0:!t||!e?!1:t.x===e.x&&t.y===e.y}function _i(t,e){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:$i;if(t.length!==e.length)return!1;for(var n=0;n<t.length;++n)if(!r(t[n],e[n]))return!1;return!0}function Nt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function _t(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Nt(Object(r),!0).forEach(function(n){Pi(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Nt(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Pi(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Pt={initialSourceClientOffset:null,initialClientOffset:null,clientOffset:null};function ki(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Pt,e=arguments.length>1?arguments[1]:void 0,r=e.payload;switch(e.type){case st:case $e:return{initialSourceClientOffset:r.sourceClientOffset,initialClientOffset:r.clientOffset,clientOffset:r.clientOffset};case Ne:return Ni(t.clientOffset,r.clientOffset)?t:_t(_t({},t),{},{clientOffset:r.clientOffset});case Pe:case _e:return Pt;default:return t}}var lt="dnd-core/ADD_SOURCE",dt="dnd-core/ADD_TARGET",ft="dnd-core/REMOVE_SOURCE",ke="dnd-core/REMOVE_TARGET";function Ri(t){return{type:lt,payload:{sourceId:t}}}function Ai(t){return{type:dt,payload:{targetId:t}}}function Mi(t){return{type:ft,payload:{sourceId:t}}}function Li(t){return{type:ke,payload:{targetId:t}}}function kt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function B(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?kt(Object(r),!0).forEach(function(n){ji(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):kt(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function ji(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Hi={itemType:null,item:null,sourceId:null,targetIds:[],dropResult:null,didDrop:!1,isSourcePublic:null};function Fi(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Hi,e=arguments.length>1?arguments[1]:void 0,r=e.payload;switch(e.type){case $e:return B(B({},t),{},{itemType:r.itemType,item:r.item,sourceId:r.sourceId,isSourcePublic:r.isSourcePublic,dropResult:null,didDrop:!1});case ut:return B(B({},t),{},{isSourcePublic:!0});case Ne:return B(B({},t),{},{targetIds:r.targetIds});case ke:return t.targetIds.indexOf(r.targetId)===-1?t:B(B({},t),{},{targetIds:Zn(t.targetIds,r.targetId)});case _e:return B(B({},t),{},{dropResult:r.dropResult,didDrop:!0,targetIds:[]});case Pe:return B(B({},t),{},{itemType:null,item:null,sourceId:null,dropResult:null,didDrop:!1,isSourcePublic:null,targetIds:[]});default:return t}}function zi(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;switch(e.type){case lt:case dt:return t+1;case ft:case ke:return t-1;default:return t}}var Ee=[],ht=[];Ee.__IS_NONE__=!0;ht.__IS_ALL__=!0;function Ui(t,e){if(t===Ee)return!1;if(t===ht||typeof e>"u")return!0;var r=ti(e,t);return r.length>0}function Wi(){var t=arguments.length>1?arguments[1]:void 0;switch(t.type){case Ne:break;case lt:case dt:case ke:case ft:return Ee;case $e:case ut:case Pe:case _e:default:return ht}var e=t.payload,r=e.targetIds,n=r===void 0?[]:r,i=e.prevTargetIds,a=i===void 0?[]:i,o=ei(n,a),u=o.length>0||!_i(n,a);if(!u)return Ee;var s=a[a.length-1],f=n[n.length-1];return s!==f&&(s&&o.push(s),f&&o.push(f)),o}function Bi(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;return t+1}function Rt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function At(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Rt(Object(r),!0).forEach(function(n){qi(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Rt(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function qi(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Vi(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;return{dirtyHandlerIds:Wi(t.dirtyHandlerIds,{type:e.type,payload:At(At({},e.payload),{},{prevTargetIds:Jn(t,"dragOperation.targetIds",[])})}),dragOffset:ki(t.dragOffset,e),refCount:zi(t.refCount,e),dragOperation:Fi(t.dragOperation,e),stateId:Bi(t.stateId)}}function Gi(t,e){return{x:t.x+e.x,y:t.y+e.y}}function wr(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Ki(t){var e=t.clientOffset,r=t.initialClientOffset,n=t.initialSourceClientOffset;return!e||!r||!n?null:wr(Gi(e,n),r)}function Yi(t){var e=t.clientOffset,r=t.initialClientOffset;return!e||!r?null:wr(e,r)}function Xi(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Qi(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function Ji(t,e,r){return e&&Qi(t.prototype,e),t}function Mt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Zi=function(){function t(e,r){Xi(this,t),Mt(this,"store",void 0),Mt(this,"registry",void 0),this.store=e,this.registry=r}return Ji(t,[{key:"subscribeToStateChange",value:function(r){var n=this,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{handlerIds:void 0},a=i.handlerIds;C(typeof r=="function","listener must be a function."),C(typeof a>"u"||Array.isArray(a),"handlerIds, when specified, must be an array of strings.");var o=this.store.getState().stateId,u=function(){var f=n.store.getState(),h=f.stateId;try{var v=h===o||h===o+1&&!Ui(f.dirtyHandlerIds,a);v||r()}finally{o=h}};return this.store.subscribe(u)}},{key:"subscribeToOffsetChange",value:function(r){var n=this;C(typeof r=="function","listener must be a function.");var i=this.store.getState().dragOffset,a=function(){var u=n.store.getState().dragOffset;u!==i&&(i=u,r())};return this.store.subscribe(a)}},{key:"canDragSource",value:function(r){if(!r)return!1;var n=this.registry.getSource(r);return C(n,"Expected to find a valid source. sourceId=".concat(r)),this.isDragging()?!1:n.canDrag(this,r)}},{key:"canDropOnTarget",value:function(r){if(!r)return!1;var n=this.registry.getTarget(r);if(C(n,"Expected to find a valid target. targetId=".concat(r)),!this.isDragging()||this.didDrop())return!1;var i=this.registry.getTargetType(r),a=this.getItemType();return Ze(i,a)&&n.canDrop(this,r)}},{key:"isDragging",value:function(){return!!this.getItemType()}},{key:"isDraggingSource",value:function(r){if(!r)return!1;var n=this.registry.getSource(r,!0);if(C(n,"Expected to find a valid source. sourceId=".concat(r)),!this.isDragging()||!this.isSourcePublic())return!1;var i=this.registry.getSourceType(r),a=this.getItemType();return i!==a?!1:n.isDragging(this,r)}},{key:"isOverTarget",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{shallow:!1};if(!r)return!1;var i=n.shallow;if(!this.isDragging())return!1;var a=this.registry.getTargetType(r),o=this.getItemType();if(o&&!Ze(a,o))return!1;var u=this.getTargetIds();if(!u.length)return!1;var s=u.indexOf(r);return i?s===u.length-1:s>-1}},{key:"getItemType",value:function(){return this.store.getState().dragOperation.itemType}},{key:"getItem",value:function(){return this.store.getState().dragOperation.item}},{key:"getSourceId",value:function(){return this.store.getState().dragOperation.sourceId}},{key:"getTargetIds",value:function(){return this.store.getState().dragOperation.targetIds}},{key:"getDropResult",value:function(){return this.store.getState().dragOperation.dropResult}},{key:"didDrop",value:function(){return this.store.getState().dragOperation.didDrop}},{key:"isSourcePublic",value:function(){return!!this.store.getState().dragOperation.isSourcePublic}},{key:"getInitialClientOffset",value:function(){return this.store.getState().dragOffset.initialClientOffset}},{key:"getInitialSourceClientOffset",value:function(){return this.store.getState().dragOffset.initialSourceClientOffset}},{key:"getClientOffset",value:function(){return this.store.getState().dragOffset.clientOffset}},{key:"getSourceClientOffset",value:function(){return Ki(this.store.getState().dragOffset)}},{key:"getDifferenceFromInitialOffset",value:function(){return Yi(this.store.getState().dragOffset)}}]),t}(),ea=0;function ta(){return ea++}function Ie(t){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Ie=function(r){return typeof r}:Ie=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},Ie(t)}function ra(t){C(typeof t.canDrag=="function","Expected canDrag to be a function."),C(typeof t.beginDrag=="function","Expected beginDrag to be a function."),C(typeof t.endDrag=="function","Expected endDrag to be a function.")}function na(t){C(typeof t.canDrop=="function","Expected canDrop to be a function."),C(typeof t.hover=="function","Expected hover to be a function."),C(typeof t.drop=="function","Expected beginDrag to be a function.")}function et(t,e){if(e&&Array.isArray(t)){t.forEach(function(r){return et(r,!1)});return}C(typeof t=="string"||Ie(t)==="symbol",e?"Type can only be a string, a symbol, or an array of either.":"Type can only be a string or a symbol.")}const Lt=typeof global<"u"?global:self,Ir=Lt.MutationObserver||Lt.WebKitMutationObserver;function Or(t){return function(){const r=setTimeout(i,0),n=setInterval(i,50);function i(){clearTimeout(r),clearInterval(n),t()}}}function ia(t){let e=1;const r=new Ir(t),n=document.createTextNode("");return r.observe(n,{characterData:!0}),function(){e=-e,n.data=e}}const aa=typeof Ir=="function"?ia:Or;class oa{enqueueTask(e){const{queue:r,requestFlush:n}=this;r.length||(n(),this.flushing=!0),r[r.length]=e}constructor(){this.queue=[],this.pendingErrors=[],this.flushing=!1,this.index=0,this.capacity=1024,this.flush=()=>{const{queue:e}=this;for(;this.index<e.length;){const r=this.index;if(this.index++,e[r].call(),this.index>this.capacity){for(let n=0,i=e.length-this.index;n<i;n++)e[n]=e[n+this.index];e.length-=this.index,this.index=0}}e.length=0,this.index=0,this.flushing=!1},this.registerPendingError=e=>{this.pendingErrors.push(e),this.requestErrorThrow()},this.requestFlush=aa(this.flush),this.requestErrorThrow=Or(()=>{if(this.pendingErrors.length)throw this.pendingErrors.shift()})}}class sa{call(){try{this.task&&this.task()}catch(e){this.onError(e)}finally{this.task=null,this.release(this)}}constructor(e,r){this.onError=e,this.release=r,this.task=null}}class ua{create(e){const r=this.freeTasks,n=r.length?r.pop():new sa(this.onError,i=>r[r.length]=i);return n.task=e,n}constructor(e){this.onError=e,this.freeTasks=[]}}const Tr=new oa,ca=new ua(Tr.registerPendingError);function la(t){Tr.enqueueTask(ca.create(t))}function da(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function fa(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function ha(t,e,r){return e&&fa(t.prototype,e),t}function ie(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ga(t,e){return ya(t)||ma(t,e)||va(t,e)||pa()}function pa(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function va(t,e){if(t){if(typeof t=="string")return jt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return jt(t,e)}}function jt(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ma(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n=[],i=!0,a=!1,o,u;try{for(r=r.call(t);!(i=(o=r.next()).done)&&(n.push(o.value),!(e&&n.length===e));i=!0);}catch(s){a=!0,u=s}finally{try{!i&&r.return!=null&&r.return()}finally{if(a)throw u}}return n}}function ya(t){if(Array.isArray(t))return t}function ba(t){var e=ta().toString();switch(t){case q.SOURCE:return"S".concat(e);case q.TARGET:return"T".concat(e);default:throw new Error("Unknown Handler Role: ".concat(t))}}function Ht(t){switch(t[0]){case"S":return q.SOURCE;case"T":return q.TARGET;default:C(!1,"Cannot parse handler ID: ".concat(t))}}function Ft(t,e){var r=t.entries(),n=!1;do{var i=r.next(),a=i.done,o=ga(i.value,2),u=o[1];if(u===e)return!0;n=!!a}while(!n);return!1}var Sa=function(){function t(e){da(this,t),ie(this,"types",new Map),ie(this,"dragSources",new Map),ie(this,"dropTargets",new Map),ie(this,"pinnedSourceId",null),ie(this,"pinnedSource",null),ie(this,"store",void 0),this.store=e}return ha(t,[{key:"addSource",value:function(r,n){et(r),ra(n);var i=this.addHandler(q.SOURCE,r,n);return this.store.dispatch(Ri(i)),i}},{key:"addTarget",value:function(r,n){et(r,!0),na(n);var i=this.addHandler(q.TARGET,r,n);return this.store.dispatch(Ai(i)),i}},{key:"containsHandler",value:function(r){return Ft(this.dragSources,r)||Ft(this.dropTargets,r)}},{key:"getSource",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;C(this.isSourceId(r),"Expected a valid source ID.");var i=n&&r===this.pinnedSourceId,a=i?this.pinnedSource:this.dragSources.get(r);return a}},{key:"getTarget",value:function(r){return C(this.isTargetId(r),"Expected a valid target ID."),this.dropTargets.get(r)}},{key:"getSourceType",value:function(r){return C(this.isSourceId(r),"Expected a valid source ID."),this.types.get(r)}},{key:"getTargetType",value:function(r){return C(this.isTargetId(r),"Expected a valid target ID."),this.types.get(r)}},{key:"isSourceId",value:function(r){var n=Ht(r);return n===q.SOURCE}},{key:"isTargetId",value:function(r){var n=Ht(r);return n===q.TARGET}},{key:"removeSource",value:function(r){var n=this;C(this.getSource(r),"Expected an existing source."),this.store.dispatch(Mi(r)),la(function(){n.dragSources.delete(r),n.types.delete(r)})}},{key:"removeTarget",value:function(r){C(this.getTarget(r),"Expected an existing target."),this.store.dispatch(Li(r)),this.dropTargets.delete(r),this.types.delete(r)}},{key:"pinSource",value:function(r){var n=this.getSource(r);C(n,"Expected an existing source."),this.pinnedSourceId=r,this.pinnedSource=n}},{key:"unpinSource",value:function(){C(this.pinnedSource,"No source is pinned at the time."),this.pinnedSourceId=null,this.pinnedSource=null}},{key:"addHandler",value:function(r,n,i){var a=ba(r);return this.types.set(a,n),r===q.SOURCE?this.dragSources.set(a,i):r===q.TARGET&&this.dropTargets.set(a,i),a}}]),t}();function Da(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:void 0,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,i=wa(n),a=new Zi(i,new Sa(i)),o=new xi(i,a),u=t(o,e,r);return o.receiveBackend(u),o}function wa(t){var e=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION__;return ct(Vi,t&&e&&e({name:"dnd-core",instanceId:"dnd-core"}))}var Ia=["children"];function Oa(t,e){return Ea(t)||Ca(t,e)||xa(t,e)||Ta()}function Ta(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function xa(t,e){if(t){if(typeof t=="string")return zt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return zt(t,e)}}function zt(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Ca(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n=[],i=!0,a=!1,o,u;try{for(r=r.call(t);!(i=(o=r.next()).done)&&(n.push(o.value),!(e&&n.length===e));i=!0);}catch(s){a=!0,u=s}finally{try{!i&&r.return!=null&&r.return()}finally{if(a)throw u}}return n}}function Ea(t){if(Array.isArray(t))return t}function $a(t,e){if(t==null)return{};var r=Na(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function Na(t,e){if(t==null)return{};var r={},n=Object.keys(t),i,a;for(a=0;a<n.length;a++)i=n[a],!(e.indexOf(i)>=0)&&(r[i]=t[i]);return r}var Ut=0,Oe=Symbol.for("__REACT_DND_CONTEXT_INSTANCE__"),_a=d.memo(function(e){var r=e.children,n=$a(e,Ia),i=Pa(n),a=Oa(i,2),o=a[0],u=a[1];return d.useEffect(function(){if(u){var s=xr();return++Ut,function(){--Ut===0&&(s[Oe]=null)}}},[]),l.jsx(Sr.Provider,Object.assign({value:o},{children:r}),void 0)});function Pa(t){if("manager"in t){var e={dragDropManager:t.manager};return[e,!1]}var r=ka(t.backend,t.context,t.options,t.debugMode),n=!t.context;return[r,n]}function ka(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:xr(),r=arguments.length>2?arguments[2]:void 0,n=arguments.length>3?arguments[3]:void 0,i=e;return i[Oe]||(i[Oe]={dragDropManager:Da(t,e,r,n)}),i[Oe]}function xr(){return typeof global<"u"?global:window}function Ra(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Aa(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function Ma(t,e,r){return e&&Aa(t.prototype,e),t}function Wt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Fe=!1,ze=!1,La=function(){function t(e){Ra(this,t),Wt(this,"internalMonitor",void 0),Wt(this,"sourceId",null),this.internalMonitor=e.getMonitor()}return Ma(t,[{key:"receiveHandlerId",value:function(r){this.sourceId=r}},{key:"getHandlerId",value:function(){return this.sourceId}},{key:"canDrag",value:function(){C(!Fe,"You may not call monitor.canDrag() inside your canDrag() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return Fe=!0,this.internalMonitor.canDragSource(this.sourceId)}finally{Fe=!1}}},{key:"isDragging",value:function(){if(!this.sourceId)return!1;C(!ze,"You may not call monitor.isDragging() inside your isDragging() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return ze=!0,this.internalMonitor.isDraggingSource(this.sourceId)}finally{ze=!1}}},{key:"subscribeToStateChange",value:function(r,n){return this.internalMonitor.subscribeToStateChange(r,n)}},{key:"isDraggingSource",value:function(r){return this.internalMonitor.isDraggingSource(r)}},{key:"isOverTarget",value:function(r,n){return this.internalMonitor.isOverTarget(r,n)}},{key:"getTargetIds",value:function(){return this.internalMonitor.getTargetIds()}},{key:"isSourcePublic",value:function(){return this.internalMonitor.isSourcePublic()}},{key:"getSourceId",value:function(){return this.internalMonitor.getSourceId()}},{key:"subscribeToOffsetChange",value:function(r){return this.internalMonitor.subscribeToOffsetChange(r)}},{key:"canDragSource",value:function(r){return this.internalMonitor.canDragSource(r)}},{key:"canDropOnTarget",value:function(r){return this.internalMonitor.canDropOnTarget(r)}},{key:"getItemType",value:function(){return this.internalMonitor.getItemType()}},{key:"getItem",value:function(){return this.internalMonitor.getItem()}},{key:"getDropResult",value:function(){return this.internalMonitor.getDropResult()}},{key:"didDrop",value:function(){return this.internalMonitor.didDrop()}},{key:"getInitialClientOffset",value:function(){return this.internalMonitor.getInitialClientOffset()}},{key:"getInitialSourceClientOffset",value:function(){return this.internalMonitor.getInitialSourceClientOffset()}},{key:"getSourceClientOffset",value:function(){return this.internalMonitor.getSourceClientOffset()}},{key:"getClientOffset",value:function(){return this.internalMonitor.getClientOffset()}},{key:"getDifferenceFromInitialOffset",value:function(){return this.internalMonitor.getDifferenceFromInitialOffset()}}]),t}();function ja(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Ha(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function Fa(t,e,r){return e&&Ha(t.prototype,e),t}function Bt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Ue=!1,za=function(){function t(e){ja(this,t),Bt(this,"internalMonitor",void 0),Bt(this,"targetId",null),this.internalMonitor=e.getMonitor()}return Fa(t,[{key:"receiveHandlerId",value:function(r){this.targetId=r}},{key:"getHandlerId",value:function(){return this.targetId}},{key:"subscribeToStateChange",value:function(r,n){return this.internalMonitor.subscribeToStateChange(r,n)}},{key:"canDrop",value:function(){if(!this.targetId)return!1;C(!Ue,"You may not call monitor.canDrop() inside your canDrop() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor");try{return Ue=!0,this.internalMonitor.canDropOnTarget(this.targetId)}finally{Ue=!1}}},{key:"isOver",value:function(r){return this.targetId?this.internalMonitor.isOverTarget(this.targetId,r):!1}},{key:"getItemType",value:function(){return this.internalMonitor.getItemType()}},{key:"getItem",value:function(){return this.internalMonitor.getItem()}},{key:"getDropResult",value:function(){return this.internalMonitor.getDropResult()}},{key:"didDrop",value:function(){return this.internalMonitor.didDrop()}},{key:"getInitialClientOffset",value:function(){return this.internalMonitor.getInitialClientOffset()}},{key:"getInitialSourceClientOffset",value:function(){return this.internalMonitor.getInitialSourceClientOffset()}},{key:"getSourceClientOffset",value:function(){return this.internalMonitor.getSourceClientOffset()}},{key:"getClientOffset",value:function(){return this.internalMonitor.getClientOffset()}},{key:"getDifferenceFromInitialOffset",value:function(){return this.internalMonitor.getDifferenceFromInitialOffset()}}]),t}();function Ua(t){if(typeof t.type!="string"){var e=t.type.displayName||t.type.name||"the component";throw new Error("Only native element nodes can now be passed to React DnD connectors."+"You can either wrap ".concat(e," into a <div>, or turn it into a ")+"drag source or a drop target itself.")}}function Wa(t){return function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(!d.isValidElement(e)){var n=e;return t(n,r),n}var i=e;Ua(i);var a=r?function(o){return t(o,r)}:t;return Ba(i,a)}}function Cr(t){var e={};return Object.keys(t).forEach(function(r){var n=t[r];if(r.endsWith("Ref"))e[r]=t[r];else{var i=Wa(n);e[r]=function(){return i}}}),e}function qt(t,e){typeof t=="function"?t(e):t.current=e}function Ba(t,e){var r=t.ref;return C(typeof r!="string","Cannot connect React DnD to an element with an existing string ref. Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs"),r?d.cloneElement(t,{ref:function(i){qt(r,i),qt(e,i)}}):d.cloneElement(t,{ref:e})}function Te(t){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Te=function(r){return typeof r}:Te=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},Te(t)}function tt(t){return t!==null&&Te(t)==="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function rt(t,e,r,n){var i=void 0;if(i!==void 0)return!!i;if(t===e)return!0;if(typeof t!="object"||!t||typeof e!="object"||!e)return!1;var a=Object.keys(t),o=Object.keys(e);if(a.length!==o.length)return!1;for(var u=Object.prototype.hasOwnProperty.bind(e),s=0;s<a.length;s++){var f=a[s];if(!u(f))return!1;var h=t[f],v=e[f];if(i=void 0,i===!1||i===void 0&&h!==v)return!1}return!0}function qa(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Va(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function Ga(t,e,r){return e&&Va(t.prototype,e),t}function H(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Ka=function(){function t(e){var r=this;qa(this,t),H(this,"hooks",Cr({dragSource:function(i,a){r.clearDragSource(),r.dragSourceOptions=a||null,tt(i)?r.dragSourceRef=i:r.dragSourceNode=i,r.reconnectDragSource()},dragPreview:function(i,a){r.clearDragPreview(),r.dragPreviewOptions=a||null,tt(i)?r.dragPreviewRef=i:r.dragPreviewNode=i,r.reconnectDragPreview()}})),H(this,"handlerId",null),H(this,"dragSourceRef",null),H(this,"dragSourceNode",void 0),H(this,"dragSourceOptionsInternal",null),H(this,"dragSourceUnsubscribe",void 0),H(this,"dragPreviewRef",null),H(this,"dragPreviewNode",void 0),H(this,"dragPreviewOptionsInternal",null),H(this,"dragPreviewUnsubscribe",void 0),H(this,"lastConnectedHandlerId",null),H(this,"lastConnectedDragSource",null),H(this,"lastConnectedDragSourceOptions",null),H(this,"lastConnectedDragPreview",null),H(this,"lastConnectedDragPreviewOptions",null),H(this,"backend",void 0),this.backend=e}return Ga(t,[{key:"receiveHandlerId",value:function(r){this.handlerId!==r&&(this.handlerId=r,this.reconnect())}},{key:"connectTarget",get:function(){return this.dragSource}},{key:"dragSourceOptions",get:function(){return this.dragSourceOptionsInternal},set:function(r){this.dragSourceOptionsInternal=r}},{key:"dragPreviewOptions",get:function(){return this.dragPreviewOptionsInternal},set:function(r){this.dragPreviewOptionsInternal=r}},{key:"reconnect",value:function(){this.reconnectDragSource(),this.reconnectDragPreview()}},{key:"reconnectDragSource",value:function(){var r=this.dragSource,n=this.didHandlerIdChange()||this.didConnectedDragSourceChange()||this.didDragSourceOptionsChange();if(n&&this.disconnectDragSource(),!!this.handlerId){if(!r){this.lastConnectedDragSource=r;return}n&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragSource=r,this.lastConnectedDragSourceOptions=this.dragSourceOptions,this.dragSourceUnsubscribe=this.backend.connectDragSource(this.handlerId,r,this.dragSourceOptions))}}},{key:"reconnectDragPreview",value:function(){var r=this.dragPreview,n=this.didHandlerIdChange()||this.didConnectedDragPreviewChange()||this.didDragPreviewOptionsChange();if(n&&this.disconnectDragPreview(),!!this.handlerId){if(!r){this.lastConnectedDragPreview=r;return}n&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragPreview=r,this.lastConnectedDragPreviewOptions=this.dragPreviewOptions,this.dragPreviewUnsubscribe=this.backend.connectDragPreview(this.handlerId,r,this.dragPreviewOptions))}}},{key:"didHandlerIdChange",value:function(){return this.lastConnectedHandlerId!==this.handlerId}},{key:"didConnectedDragSourceChange",value:function(){return this.lastConnectedDragSource!==this.dragSource}},{key:"didConnectedDragPreviewChange",value:function(){return this.lastConnectedDragPreview!==this.dragPreview}},{key:"didDragSourceOptionsChange",value:function(){return!rt(this.lastConnectedDragSourceOptions,this.dragSourceOptions)}},{key:"didDragPreviewOptionsChange",value:function(){return!rt(this.lastConnectedDragPreviewOptions,this.dragPreviewOptions)}},{key:"disconnectDragSource",value:function(){this.dragSourceUnsubscribe&&(this.dragSourceUnsubscribe(),this.dragSourceUnsubscribe=void 0)}},{key:"disconnectDragPreview",value:function(){this.dragPreviewUnsubscribe&&(this.dragPreviewUnsubscribe(),this.dragPreviewUnsubscribe=void 0,this.dragPreviewNode=null,this.dragPreviewRef=null)}},{key:"dragSource",get:function(){return this.dragSourceNode||this.dragSourceRef&&this.dragSourceRef.current}},{key:"dragPreview",get:function(){return this.dragPreviewNode||this.dragPreviewRef&&this.dragPreviewRef.current}},{key:"clearDragSource",value:function(){this.dragSourceNode=null,this.dragSourceRef=null}},{key:"clearDragPreview",value:function(){this.dragPreviewNode=null,this.dragPreviewRef=null}}]),t}();function Ya(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Xa(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function Qa(t,e,r){return e&&Xa(t.prototype,e),t}function K(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Ja=function(){function t(e){var r=this;Ya(this,t),K(this,"hooks",Cr({dropTarget:function(i,a){r.clearDropTarget(),r.dropTargetOptions=a,tt(i)?r.dropTargetRef=i:r.dropTargetNode=i,r.reconnect()}})),K(this,"handlerId",null),K(this,"dropTargetRef",null),K(this,"dropTargetNode",void 0),K(this,"dropTargetOptionsInternal",null),K(this,"unsubscribeDropTarget",void 0),K(this,"lastConnectedHandlerId",null),K(this,"lastConnectedDropTarget",null),K(this,"lastConnectedDropTargetOptions",null),K(this,"backend",void 0),this.backend=e}return Qa(t,[{key:"connectTarget",get:function(){return this.dropTarget}},{key:"reconnect",value:function(){var r=this.didHandlerIdChange()||this.didDropTargetChange()||this.didOptionsChange();r&&this.disconnectDropTarget();var n=this.dropTarget;if(this.handlerId){if(!n){this.lastConnectedDropTarget=n;return}r&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDropTarget=n,this.lastConnectedDropTargetOptions=this.dropTargetOptions,this.unsubscribeDropTarget=this.backend.connectDropTarget(this.handlerId,n,this.dropTargetOptions))}}},{key:"receiveHandlerId",value:function(r){r!==this.handlerId&&(this.handlerId=r,this.reconnect())}},{key:"dropTargetOptions",get:function(){return this.dropTargetOptionsInternal},set:function(r){this.dropTargetOptionsInternal=r}},{key:"didHandlerIdChange",value:function(){return this.lastConnectedHandlerId!==this.handlerId}},{key:"didDropTargetChange",value:function(){return this.lastConnectedDropTarget!==this.dropTarget}},{key:"didOptionsChange",value:function(){return!rt(this.lastConnectedDropTargetOptions,this.dropTargetOptions)}},{key:"disconnectDropTarget",value:function(){this.unsubscribeDropTarget&&(this.unsubscribeDropTarget(),this.unsubscribeDropTarget=void 0)}},{key:"dropTarget",get:function(){return this.dropTargetNode||this.dropTargetRef&&this.dropTargetRef.current}},{key:"clearDropTarget",value:function(){this.dropTargetRef=null,this.dropTargetNode=null}}]),t}();function Za(t,e,r){var n=r.getRegistry(),i=n.addTarget(t,e);return[i,function(){return n.removeTarget(i)}]}function eo(t,e,r){var n=r.getRegistry(),i=n.addSource(t,e);return[i,function(){return n.removeSource(i)}]}var te=typeof window<"u"?d.useLayoutEffect:d.useEffect;function xe(t){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?xe=function(r){return typeof r}:xe=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},xe(t)}function to(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function ro(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function no(t,e,r){return e&&ro(t.prototype,e),t}function We(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var io=function(){function t(e,r,n){to(this,t),We(this,"spec",void 0),We(this,"monitor",void 0),We(this,"connector",void 0),this.spec=e,this.monitor=r,this.connector=n}return no(t,[{key:"beginDrag",value:function(){var r,n=this.spec,i=this.monitor,a=null;return xe(n.item)==="object"?a=n.item:typeof n.item=="function"?a=n.item(i):a={},(r=a)!==null&&r!==void 0?r:null}},{key:"canDrag",value:function(){var r=this.spec,n=this.monitor;return typeof r.canDrag=="boolean"?r.canDrag:typeof r.canDrag=="function"?r.canDrag(n):!0}},{key:"isDragging",value:function(r,n){var i=this.spec,a=this.monitor,o=i.isDragging;return o?o(a):n===r.getSourceId()}},{key:"endDrag",value:function(){var r=this.spec,n=this.monitor,i=this.connector,a=r.end;a&&a(n.getItem(),n),i.reconnect()}}]),t}();function ao(t,e,r){var n=d.useMemo(function(){return new io(t,e,r)},[e,r]);return d.useEffect(function(){n.spec=t},[t]),n}function re(){var t=d.useContext(Sr),e=t.dragDropManager;return C(e!=null,"Expected drag drop context"),e}function oo(t){return d.useMemo(function(){var e=t.type;return C(e!=null,"spec.type must be defined"),e},[t])}function so(t,e){return fo(t)||lo(t,e)||co(t,e)||uo()}function uo(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function co(t,e){if(t){if(typeof t=="string")return Vt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Vt(t,e)}}function Vt(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function lo(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n=[],i=!0,a=!1,o,u;try{for(r=r.call(t);!(i=(o=r.next()).done)&&(n.push(o.value),!(e&&n.length===e));i=!0);}catch(s){a=!0,u=s}finally{try{!i&&r.return!=null&&r.return()}finally{if(a)throw u}}return n}}function fo(t){if(Array.isArray(t))return t}function ho(t,e,r){var n=re(),i=ao(t,e,r),a=oo(t);te(function(){if(a!=null){var u=eo(a,i,n),s=so(u,2),f=s[0],h=s[1];return e.receiveHandlerId(f),r.receiveHandlerId(f),h}},[n,e,r,i,a])}function go(t){return yo(t)||mo(t)||vo(t)||po()}function po(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function vo(t,e){if(t){if(typeof t=="string")return nt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nt(t,e)}}function mo(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function yo(t){if(Array.isArray(t))return nt(t)}function nt(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Er(t,e){var r=go(e||[]);return e==null&&typeof t!="function"&&r.push(t),d.useMemo(function(){return typeof t=="function"?t():t},r)}function bo(){var t=re();return d.useMemo(function(){return new La(t)},[t])}function So(t,e){var r=re(),n=d.useMemo(function(){return new Ka(r.getBackend())},[r]);return te(function(){return n.dragSourceOptions=t||null,n.reconnect(),function(){return n.disconnectDragSource()}},[n,t]),te(function(){return n.dragPreviewOptions=e||null,n.reconnect(),function(){return n.disconnectDragPreview()}},[n,e]),n}var Do=function t(e,r){if(e===r)return!0;if(e&&r&&typeof e=="object"&&typeof r=="object"){if(e.constructor!==r.constructor)return!1;var n,i,a;if(Array.isArray(e)){if(n=e.length,n!=r.length)return!1;for(i=n;i--!==0;)if(!t(e[i],r[i]))return!1;return!0}if(e.constructor===RegExp)return e.source===r.source&&e.flags===r.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===r.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===r.toString();if(a=Object.keys(e),n=a.length,n!==Object.keys(r).length)return!1;for(i=n;i--!==0;)if(!Object.prototype.hasOwnProperty.call(r,a[i]))return!1;for(i=n;i--!==0;){var o=a[i];if(!t(e[o],r[o]))return!1}return!0}return e!==e&&r!==r};const wo=fn(Do);function Io(t,e){return Co(t)||xo(t,e)||To(t,e)||Oo()}function Oo(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function To(t,e){if(t){if(typeof t=="string")return Gt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Gt(t,e)}}function Gt(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function xo(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n=[],i=!0,a=!1,o,u;try{for(r=r.call(t);!(i=(o=r.next()).done)&&(n.push(o.value),!(e&&n.length===e));i=!0);}catch(s){a=!0,u=s}finally{try{!i&&r.return!=null&&r.return()}finally{if(a)throw u}}return n}}function Co(t){if(Array.isArray(t))return t}function $r(t,e,r){var n=d.useState(function(){return e(t)}),i=Io(n,2),a=i[0],o=i[1],u=d.useCallback(function(){var s=e(t);wo(a,s)||(o(s),r&&r())},[a,t,r]);return te(u),[a,u]}function Eo(t,e){return Po(t)||_o(t,e)||No(t,e)||$o()}function $o(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function No(t,e){if(t){if(typeof t=="string")return Kt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Kt(t,e)}}function Kt(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function _o(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n=[],i=!0,a=!1,o,u;try{for(r=r.call(t);!(i=(o=r.next()).done)&&(n.push(o.value),!(e&&n.length===e));i=!0);}catch(s){a=!0,u=s}finally{try{!i&&r.return!=null&&r.return()}finally{if(a)throw u}}return n}}function Po(t){if(Array.isArray(t))return t}function ko(t,e,r){var n=$r(t,e,r),i=Eo(n,2),a=i[0],o=i[1];return te(function(){var s=t.getHandlerId();if(s!=null)return t.subscribeToStateChange(o,{handlerIds:[s]})},[t,o]),a}function Nr(t,e,r){return ko(e,t||function(){return{}},function(){return r.reconnect()})}function Ro(t){return d.useMemo(function(){return t.hooks.dragSource()},[t])}function Ao(t){return d.useMemo(function(){return t.hooks.dragPreview()},[t])}function Mo(t,e){var r=Er(t,e);C(!r.begin,"useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)");var n=bo(),i=So(r.options,r.previewOptions);return ho(r,n,i),[Nr(r.collect,n,i),Ro(i),Ao(i)]}function Lo(t){var e=t.accept;return d.useMemo(function(){return C(t.accept!=null,"accept must be defined"),Array.isArray(e)?e:[e]},[e])}function jo(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Ho(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function Fo(t,e,r){return e&&Ho(t.prototype,e),t}function Yt(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var zo=function(){function t(e,r){jo(this,t),Yt(this,"spec",void 0),Yt(this,"monitor",void 0),this.spec=e,this.monitor=r}return Fo(t,[{key:"canDrop",value:function(){var r=this.spec,n=this.monitor;return r.canDrop?r.canDrop(n.getItem(),n):!0}},{key:"hover",value:function(){var r=this.spec,n=this.monitor;r.hover&&r.hover(n.getItem(),n)}},{key:"drop",value:function(){var r=this.spec,n=this.monitor;if(r.drop)return r.drop(n.getItem(),n)}}]),t}();function Uo(t,e){var r=d.useMemo(function(){return new zo(t,e)},[e]);return d.useEffect(function(){r.spec=t},[t]),r}function Wo(t,e){return Go(t)||Vo(t,e)||qo(t,e)||Bo()}function Bo(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function qo(t,e){if(t){if(typeof t=="string")return Xt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Xt(t,e)}}function Xt(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Vo(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n=[],i=!0,a=!1,o,u;try{for(r=r.call(t);!(i=(o=r.next()).done)&&(n.push(o.value),!(e&&n.length===e));i=!0);}catch(s){a=!0,u=s}finally{try{!i&&r.return!=null&&r.return()}finally{if(a)throw u}}return n}}function Go(t){if(Array.isArray(t))return t}function Ko(t,e,r){var n=re(),i=Uo(t,e),a=Lo(t);te(function(){var u=Za(a,i,n),s=Wo(u,2),f=s[0],h=s[1];return e.receiveHandlerId(f),r.receiveHandlerId(f),h},[n,e,i,r,a.map(function(o){return o.toString()}).join("|")])}function Yo(){var t=re();return d.useMemo(function(){return new za(t)},[t])}function Xo(t){var e=re(),r=d.useMemo(function(){return new Ja(e.getBackend())},[e]);return te(function(){return r.dropTargetOptions=t||null,r.reconnect(),function(){return r.disconnectDropTarget()}},[t]),r}function Qo(t){return d.useMemo(function(){return t.hooks.dropTarget()},[t])}function _r(t,e){var r=Er(t,e),n=Yo(),i=Xo(r.options);return Ko(r,n,i),[Nr(r.collect,n,i),Qo(i)]}function Jo(t,e){return rs(t)||ts(t,e)||es(t,e)||Zo()}function Zo(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function es(t,e){if(t){if(typeof t=="string")return Qt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Qt(t,e)}}function Qt(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ts(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n=[],i=!0,a=!1,o,u;try{for(r=r.call(t);!(i=(o=r.next()).done)&&(n.push(o.value),!(e&&n.length===e));i=!0);}catch(s){a=!0,u=s}finally{try{!i&&r.return!=null&&r.return()}finally{if(a)throw u}}return n}}function rs(t){if(Array.isArray(t))return t}function ns(t){var e=re(),r=e.getMonitor(),n=$r(r,t),i=Jo(n,2),a=i[0],o=i[1];return d.useEffect(function(){return r.subscribeToOffsetChange(o)}),d.useEffect(function(){return r.subscribeToStateChange(o)}),a}function it(){return it=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},it.apply(null,arguments)}function Jt(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function at(t,e){return at=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,n){return r.__proto__=n,r},at(t,e)}function is(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,at(t,e)}var Zt=Number.isNaN||function(e){return typeof e=="number"&&e!==e};function as(t,e){return!!(t===e||Zt(t)&&Zt(e))}function os(t,e){if(t.length!==e.length)return!1;for(var r=0;r<t.length;r++)if(!as(t[r],e[r]))return!1;return!0}function Be(t,e){e===void 0&&(e=os);var r,n=[],i,a=!1;function o(){for(var u=[],s=0;s<arguments.length;s++)u[s]=arguments[s];return a&&r===this&&e(u,n)||(i=t.apply(this,u),a=!0,r=this,n=u),i}return o}var ss=typeof performance=="object"&&typeof performance.now=="function",er=ss?function(){return performance.now()}:function(){return Date.now()};function tr(t){cancelAnimationFrame(t.id)}function us(t,e){var r=er();function n(){er()-r>=e?t.call(null):i.id=requestAnimationFrame(n)}var i={id:requestAnimationFrame(n)};return i}var qe=-1;function rr(t){if(t===void 0&&(t=!1),qe===-1||t){var e=document.createElement("div"),r=e.style;r.width="50px",r.height="50px",r.overflow="scroll",document.body.appendChild(e),qe=e.offsetWidth-e.clientWidth,document.body.removeChild(e)}return qe}var ae=null;function nr(t){if(t===void 0&&(t=!1),ae===null||t){var e=document.createElement("div"),r=e.style;r.width="50px",r.height="50px",r.overflow="scroll",r.direction="rtl";var n=document.createElement("div"),i=n.style;return i.width="100px",i.height="100px",e.appendChild(n),document.body.appendChild(e),e.scrollLeft>0?ae="positive-descending":(e.scrollLeft=1,e.scrollLeft===0?ae="negative":ae="positive-ascending"),document.body.removeChild(e),ae}return ae}var cs=150,ls=function(e,r){return e};function ds(t){var e,r=t.getItemOffset,n=t.getEstimatedTotalSize,i=t.getItemSize,a=t.getOffsetForIndexAndAlignment,o=t.getStartIndexForOffset,u=t.getStopIndexForStartIndex,s=t.initInstanceProps,f=t.shouldResetStyleCacheOnItemSizeChange,h=t.validateProps;return e=function(v){is(b,v);function b(w){var c;return c=v.call(this,w)||this,c._instanceProps=s(c.props,Jt(c)),c._outerRef=void 0,c._resetIsScrollingTimeoutId=null,c.state={instance:Jt(c),isScrolling:!1,scrollDirection:"forward",scrollOffset:typeof c.props.initialScrollOffset=="number"?c.props.initialScrollOffset:0,scrollUpdateWasRequested:!1},c._callOnItemsRendered=void 0,c._callOnItemsRendered=Be(function(g,p,y,I){return c.props.onItemsRendered({overscanStartIndex:g,overscanStopIndex:p,visibleStartIndex:y,visibleStopIndex:I})}),c._callOnScroll=void 0,c._callOnScroll=Be(function(g,p,y){return c.props.onScroll({scrollDirection:g,scrollOffset:p,scrollUpdateWasRequested:y})}),c._getItemStyle=void 0,c._getItemStyle=function(g){var p=c.props,y=p.direction,I=p.itemSize,O=p.layout,D=c._getItemStyleCache(f&&I,f&&O,f&&y),T;if(D.hasOwnProperty(g))T=D[g];else{var S=r(c.props,g,c._instanceProps),R=i(c.props,g,c._instanceProps),$=y==="horizontal"||O==="horizontal",W=y==="rtl",Y=$?S:0;D[g]=T={position:"absolute",left:W?void 0:Y,right:W?Y:void 0,top:$?0:S,height:$?"100%":R,width:$?R:"100%"}}return T},c._getItemStyleCache=void 0,c._getItemStyleCache=Be(function(g,p,y){return{}}),c._onScrollHorizontal=function(g){var p=g.currentTarget,y=p.clientWidth,I=p.scrollLeft,O=p.scrollWidth;c.setState(function(D){if(D.scrollOffset===I)return null;var T=c.props.direction,S=I;if(T==="rtl")switch(nr()){case"negative":S=-I;break;case"positive-descending":S=O-y-I;break}return S=Math.max(0,Math.min(S,O-y)),{isScrolling:!0,scrollDirection:D.scrollOffset<S?"forward":"backward",scrollOffset:S,scrollUpdateWasRequested:!1}},c._resetIsScrollingDebounced)},c._onScrollVertical=function(g){var p=g.currentTarget,y=p.clientHeight,I=p.scrollHeight,O=p.scrollTop;c.setState(function(D){if(D.scrollOffset===O)return null;var T=Math.max(0,Math.min(O,I-y));return{isScrolling:!0,scrollDirection:D.scrollOffset<T?"forward":"backward",scrollOffset:T,scrollUpdateWasRequested:!1}},c._resetIsScrollingDebounced)},c._outerRefSetter=function(g){var p=c.props.outerRef;c._outerRef=g,typeof p=="function"?p(g):p!=null&&typeof p=="object"&&p.hasOwnProperty("current")&&(p.current=g)},c._resetIsScrollingDebounced=function(){c._resetIsScrollingTimeoutId!==null&&tr(c._resetIsScrollingTimeoutId),c._resetIsScrollingTimeoutId=us(c._resetIsScrolling,cs)},c._resetIsScrolling=function(){c._resetIsScrollingTimeoutId=null,c.setState({isScrolling:!1},function(){c._getItemStyleCache(-1,null)})},c}b.getDerivedStateFromProps=function(c,g){return fs(c,g),h(c),null};var m=b.prototype;return m.scrollTo=function(c){c=Math.max(0,c),this.setState(function(g){return g.scrollOffset===c?null:{scrollDirection:g.scrollOffset<c?"forward":"backward",scrollOffset:c,scrollUpdateWasRequested:!0}},this._resetIsScrollingDebounced)},m.scrollToItem=function(c,g){g===void 0&&(g="auto");var p=this.props,y=p.itemCount,I=p.layout,O=this.state.scrollOffset;c=Math.max(0,Math.min(c,y-1));var D=0;if(this._outerRef){var T=this._outerRef;I==="vertical"?D=T.scrollWidth>T.clientWidth?rr():0:D=T.scrollHeight>T.clientHeight?rr():0}this.scrollTo(a(this.props,c,g,O,this._instanceProps,D))},m.componentDidMount=function(){var c=this.props,g=c.direction,p=c.initialScrollOffset,y=c.layout;if(typeof p=="number"&&this._outerRef!=null){var I=this._outerRef;g==="horizontal"||y==="horizontal"?I.scrollLeft=p:I.scrollTop=p}this._callPropsCallbacks()},m.componentDidUpdate=function(){var c=this.props,g=c.direction,p=c.layout,y=this.state,I=y.scrollOffset,O=y.scrollUpdateWasRequested;if(O&&this._outerRef!=null){var D=this._outerRef;if(g==="horizontal"||p==="horizontal")if(g==="rtl")switch(nr()){case"negative":D.scrollLeft=-I;break;case"positive-ascending":D.scrollLeft=I;break;default:var T=D.clientWidth,S=D.scrollWidth;D.scrollLeft=S-T-I;break}else D.scrollLeft=I;else D.scrollTop=I}this._callPropsCallbacks()},m.componentWillUnmount=function(){this._resetIsScrollingTimeoutId!==null&&tr(this._resetIsScrollingTimeoutId)},m.render=function(){var c=this.props,g=c.children,p=c.className,y=c.direction,I=c.height,O=c.innerRef,D=c.innerElementType,T=c.innerTagName,S=c.itemCount,R=c.itemData,$=c.itemKey,W=$===void 0?ls:$,Y=c.layout,Z=c.outerElementType,me=c.outerTagName,ye=c.style,Re=c.useIsScrolling,x=c.width,E=this.state.isScrolling,P=y==="horizontal"||Y==="horizontal",_=P?this._onScrollHorizontal:this._onScrollVertical,M=this._getRangeToRender(),j=M[0],F=M[1],X=[];if(S>0)for(var J=j;J<=F;J++)X.push(d.createElement(g,{data:R,key:W(J,R),index:J,isScrolling:Re?E:void 0,style:this._getItemStyle(J)}));var pt=n(this.props,this._instanceProps);return d.createElement(Z||me||"div",{className:p,onScroll:_,ref:this._outerRefSetter,style:it({position:"relative",height:I,width:x,overflow:"auto",WebkitOverflowScrolling:"touch",willChange:"transform",direction:y},ye)},d.createElement(D||T||"div",{children:X,ref:O,style:{height:P?"100%":pt,pointerEvents:E?"none":void 0,width:P?pt:"100%"}}))},m._callPropsCallbacks=function(){if(typeof this.props.onItemsRendered=="function"){var c=this.props.itemCount;if(c>0){var g=this._getRangeToRender(),p=g[0],y=g[1],I=g[2],O=g[3];this._callOnItemsRendered(p,y,I,O)}}if(typeof this.props.onScroll=="function"){var D=this.state,T=D.scrollDirection,S=D.scrollOffset,R=D.scrollUpdateWasRequested;this._callOnScroll(T,S,R)}},m._getRangeToRender=function(){var c=this.props,g=c.itemCount,p=c.overscanCount,y=this.state,I=y.isScrolling,O=y.scrollDirection,D=y.scrollOffset;if(g===0)return[0,0,0,0];var T=o(this.props,D,this._instanceProps),S=u(this.props,T,D,this._instanceProps),R=!I||O==="backward"?Math.max(1,p):1,$=!I||O==="forward"?Math.max(1,p):1;return[Math.max(0,T-R),Math.max(0,Math.min(g-1,S+$)),T,S]},b}(d.PureComponent),e.defaultProps={direction:"ltr",itemData:void 0,layout:"vertical",overscanCount:2,useIsScrolling:!1},e}var fs=function(e,r){e.children,e.direction,e.height,e.layout,e.innerTagName,e.outerTagName,e.width,r.instance},hs=ds({getItemOffset:function(e,r){var n=e.itemSize;return r*n},getItemSize:function(e,r){var n=e.itemSize;return n},getEstimatedTotalSize:function(e){var r=e.itemCount,n=e.itemSize;return n*r},getOffsetForIndexAndAlignment:function(e,r,n,i,a,o){var u=e.direction,s=e.height,f=e.itemCount,h=e.itemSize,v=e.layout,b=e.width,m=u==="horizontal"||v==="horizontal",w=m?b:s,c=Math.max(0,f*h-w),g=Math.min(c,r*h),p=Math.max(0,r*h-w+h+o);switch(n==="smart"&&(i>=p-w&&i<=g+w?n="auto":n="center"),n){case"start":return g;case"end":return p;case"center":{var y=Math.round(p+(g-p)/2);return y<Math.ceil(w/2)?0:y>c+Math.floor(w/2)?c:y}case"auto":default:return i>=p&&i<=g?i:i<p?p:g}},getStartIndexForOffset:function(e,r){var n=e.itemCount,i=e.itemSize;return Math.max(0,Math.min(n-1,Math.floor(r/i)))},getStopIndexForStartIndex:function(e,r,n){var i=e.direction,a=e.height,o=e.itemCount,u=e.itemSize,s=e.layout,f=e.width,h=i==="horizontal"||s==="horizontal",v=r*u,b=h?f:a,m=Math.ceil((b+n-v)/u);return Math.max(0,Math.min(o-1,r+m-1))},initInstanceProps:function(e){},shouldResetStyleCacheOnItemSizeChange:!0,validateProps:function(e){e.itemSize}});function A(t,e,r,n){Object.defineProperty(t,e,{get:r,set:n,enumerable:!0,configurable:!0})}const Pr=d.createContext(null);function U(){const t=d.useContext(Pr);if(t===null)throw new Error("No Tree Api Provided");return t}const kr=d.createContext(null);function gs(){const t=d.useContext(kr);if(t===null)throw new Error("Provide a NodesContext");return t}const Rr=d.createContext(null);function ps(){const t=d.useContext(Rr);if(t===null)throw new Error("Provide a DnDContext");return t}const Ar=d.createContext(0);function Mr(){d.useContext(Ar)}var vs={};A(vs,"TreeApi",()=>se);var k={};A(k,"bound",()=>Lr);A(k,"isItem",()=>jr);A(k,"isClosed",()=>Hr);A(k,"isDecendent",()=>ms);A(k,"indexOf",()=>Fr);A(k,"noop",()=>ys);A(k,"dfs",()=>zr);A(k,"walk",()=>Ur);A(k,"focusNextElement",()=>Wr);A(k,"focusPrevElement",()=>Br);A(k,"access",()=>Ds);A(k,"identifyNull",()=>ws);A(k,"identify",()=>ee);A(k,"mergeRefs",()=>Is);A(k,"safeRun",()=>Vr);A(k,"waitFor",()=>Os);A(k,"getInsertIndex",()=>Ts);A(k,"getInsertParentId",()=>xs);function Lr(t,e,r){return Math.max(Math.min(t,r),e)}function jr(t){return t&&t.isLeaf}function Hr(t){return t&&t.isInternal&&!t.isOpen}const ms=(t,e)=>{let r=t;for(;r;){if(r.id===e.id)return!0;r=r.parent}return!1},Fr=t=>{if(!t.parent)throw Error("Node does not have a parent");return t.parent.children.findIndex(e=>e.id===t.id)};function ys(){}function zr(t,e){if(!t)return null;if(t.id===e)return t;if(t.children)for(let r of t.children){const n=zr(r,e);if(n)return n}return null}function Ur(t,e){if(e(t),t.children)for(let r of t.children)Ur(r,e)}function Wr(t){const e=qr(t);let r;for(let n=0;n<e.length;++n)if(e[n]===t){r=bs(e,n);break}r==null||r.focus()}function Br(t){const e=qr(t);let r;for(let n=0;n<e.length;++n)if(e[n]===t){r=Ss(e,n);break}r==null||r.focus()}function bs(t,e){return e+1<t.length?t[e+1]:t[0]}function Ss(t,e){return e-1>=0?t[e-1]:t[t.length-1]}function qr(t){return Array.from(document.querySelectorAll('button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"]):not([disabled]), details:not([disabled]), summary:not(:disabled)')).filter(e=>e===t||!t.contains(e))}function Ds(t,e){return typeof e=="boolean"?e:typeof e=="string"?t[e]:e(t)}function ws(t){return t===null?null:ee(t)}function ee(t){return typeof t=="string"?t:t.id}function Is(...t){return e=>{t.forEach(r=>{typeof r=="function"?r(e):r!=null&&(r.current=e)})}}function Vr(t,...e){if(t)return t(...e)}function Os(t){return new Promise((e,r)=>{let n=0;function i(){n+=1,n===100&&r(),t()?e():setTimeout(i,10)}i()})}function Ts(t){var r;const e=t.focusedNode;return e?e.isOpen?0:e.parent?e.childIndex+1:0:((r=t.root.children)==null?void 0:r.length)??0}function xs(t){const e=t.focusedNode;return e?e.isOpen?e.id:e.parent&&!e.parent.isRoot?e.parent.id:null:null}const Cs={display:"flex",alignItems:"center",zIndex:1},Es={flex:1,height:"2px",background:"#4B91E2",borderRadius:"1px"},$s={width:"4px",height:"4px",boxShadow:"0 0 0 3px #4B91E2",borderRadius:"50%"},Ns=le.memo(function({top:e,left:r,indent:n}){const i={position:"absolute",pointerEvents:"none",top:e-2+"px",left:r+"px",right:n+"px"};return l.jsxs("div",{style:{...Cs,...i},children:[l.jsx("div",{style:{...$s}}),l.jsx("div",{style:{...Es}})]})});function _s({node:t,attrs:e,innerRef:r,children:n}){return l.jsx("div",{...e,ref:r,onFocus:i=>i.stopPropagation(),onClick:t.handleClick,children:n})}function Ps(t){return l.jsxs("div",{ref:t.dragHandle,style:t.style,children:[l.jsx("span",{onClick:e=>{e.stopPropagation(),t.node.toggle()},children:t.node.isLeaf?"🌳":t.node.isOpen?"🗁":"🗀"})," ",t.node.isEditing?l.jsx(Rs,{...t}):l.jsx(ks,{...t})]})}function ks(t){return l.jsx(l.Fragment,{children:l.jsx("span",{children:t.node.data.name})})}function Rs({node:t}){const e=d.useRef();return d.useEffect(()=>{var r,n;(r=e.current)==null||r.focus(),(n=e.current)==null||n.select()},[]),l.jsx("input",{ref:e,defaultValue:t.data.name,onBlur:()=>t.reset(),onKeyDown:r=>{var n;r.key==="Escape"&&t.reset(),r.key==="Enter"&&t.submit(((n=e.current)==null?void 0:n.value)||"")}})}function Ve(t){return{type:"EDIT",id:t}}function As(t={id:null},e){return e.type==="EDIT"?{...t,id:e.id}:t}function oe(t){return{type:"FOCUS",id:t}}function Ms(){return{type:"TREE_BLUR"}}function Ls(t={id:null,treeFocused:!1},e){return e.type==="FOCUS"?{...t,id:e.id,treeFocused:!0}:e.type==="TREE_BLUR"?{...t,treeFocused:!1}:t}var js={};A(js,"NodeApi",()=>he);class he{constructor(e){vt(this,"handleClick",e=>{e.metaKey&&!this.tree.props.disableMultiSelection?this.isSelected?this.deselect():this.selectMulti():e.shiftKey&&!this.tree.props.disableMultiSelection?this.selectContiguous():(this.select(),this.activate())});this.tree=e.tree,this.id=e.id,this.data=e.data,this.level=e.level,this.children=e.children,this.parent=e.parent,this.isDraggable=e.isDraggable,this.rowIndex=e.rowIndex}get isRoot(){return this.id===ge}get isLeaf(){return!Array.isArray(this.children)}get isInternal(){return!this.isLeaf}get isOpen(){return this.isLeaf?!1:this.tree.isOpen(this.id)}get isClosed(){return this.isLeaf?!1:!this.tree.isOpen(this.id)}get isEditable(){return this.tree.isEditable(this.data)}get isEditing(){return this.tree.editingId===this.id}get isSelected(){return this.tree.isSelected(this.id)}get isOnlySelection(){return this.isSelected&&this.tree.hasOneSelection}get isSelectedStart(){var e;return this.isSelected&&!((e=this.prev)!=null&&e.isSelected)}get isSelectedEnd(){var e;return this.isSelected&&!((e=this.next)!=null&&e.isSelected)}get isFocused(){return this.tree.isFocused(this.id)}get isDragging(){return this.tree.isDragging(this.id)}get willReceiveDrop(){return this.tree.willReceiveDrop(this.id)}get state(){return{isClosed:this.isClosed,isDragging:this.isDragging,isEditing:this.isEditing,isFocused:this.isFocused,isInternal:this.isInternal,isLeaf:this.isLeaf,isOpen:this.isOpen,isSelected:this.isSelected,isSelectedEnd:this.isSelectedEnd,isSelectedStart:this.isSelectedStart,willReceiveDrop:this.willReceiveDrop}}get childIndex(){return this.parent&&this.parent.children?this.parent.children.findIndex(e=>e.id===this.id):-1}get next(){return this.rowIndex===null?null:this.tree.at(this.rowIndex+1)}get prev(){return this.rowIndex===null?null:this.tree.at(this.rowIndex-1)}get nextSibling(){var r;const e=this.childIndex;return((r=this.parent)==null?void 0:r.children[e+1])??null}select(){this.tree.select(this)}deselect(){this.tree.deselect(this)}selectMulti(){this.tree.selectMulti(this)}selectContiguous(){this.tree.selectContiguous(this)}activate(){this.tree.activate(this)}focus(){this.tree.focus(this)}toggle(){this.tree.toggle(this)}open(){this.tree.open(this)}openParents(){this.tree.openParents(this)}close(){this.tree.close(this)}submit(e){this.tree.submit(this,e)}reset(){this.tree.reset()}clone(){return new he({...this})}edit(){return this.tree.edit(this)}}const ge="__REACT_ARBORIST_INTERNAL_ROOT__";function ir(t){function e(i,a,o){const u=t.accessId(i),s=new he({tree:t,data:i,level:a,parent:o,id:u,children:null,isDraggable:t.isDraggable(i),rowIndex:null}),f=t.accessChildren(i);return f&&(s.children=f.map(h=>e(h,a+1,s))),s}const r=new he({tree:t,id:ge,data:{id:ge},level:-1,parent:null,children:null,isDraggable:!0,rowIndex:null}),n=t.props.data??[];return r.children=n.map(i=>e(i,0,r)),r}const ot={open(t,e){return{type:"VISIBILITY_OPEN",id:t,filtered:e}},close(t,e){return{type:"VISIBILITY_CLOSE",id:t,filtered:e}},toggle(t,e){return{type:"VISIBILITY_TOGGLE",id:t,filtered:e}},clear(t){return{type:"VISIBILITY_CLEAR",filtered:t}}};function ar(t={},e){if(e.type==="VISIBILITY_OPEN")return{...t,[e.id]:!0};if(e.type==="VISIBILITY_CLOSE")return{...t,[e.id]:!1};if(e.type==="VISIBILITY_TOGGLE"){const r=t[e.id];return{...t,[e.id]:!r}}else return e.type==="VISIBILITY_CLEAR"?{}:t}function Hs(t={filtered:{},unfiltered:{}},e){return e.type.startsWith("VISIBILITY")?e.filtered?{...t,filtered:ar(t.filtered,e)}:{...t,unfiltered:ar(t.unfiltered,e)}:t}const pe=t=>({nodes:{open:{filtered:{},unfiltered:(t==null?void 0:t.initialOpenState)??{}},focus:{id:null,treeFocused:!1},edit:{id:null},drag:{id:null,idWillReceiveDrop:null},selection:{ids:new Set,anchor:null,mostRecent:null}},dnd:{cursor:{type:"none"},dragId:null,dragIds:[],parentId:null,index:-1}}),V={clear:()=>({type:"SELECTION_CLEAR"}),only:t=>({type:"SELECTION_ONLY",id:ee(t)}),add:t=>({type:"SELECTION_ADD",ids:(Array.isArray(t)?t:[t]).map(ee)}),remove:t=>({type:"SELECTION_REMOVE",ids:(Array.isArray(t)?t:[t]).map(ee)}),set:t=>({type:"SELECTION_SET",...t}),mostRecent:t=>({type:"SELECTION_MOST_RECENT",id:t===null?null:ee(t)}),anchor:t=>({type:"SELECTION_ANCHOR",id:t===null?null:ee(t)})};function Fs(t=pe().nodes.selection,e){const r=t.ids;switch(e.type){case"SELECTION_CLEAR":return{...t,ids:new Set};case"SELECTION_ONLY":return{...t,ids:new Set([e.id])};case"SELECTION_ADD":return e.ids.length===0?t:(e.ids.forEach(n=>r.add(n)),{...t,ids:new Set(r)});case"SELECTION_REMOVE":return e.ids.length===0?t:(e.ids.forEach(n=>r.delete(n)),{...t,ids:new Set(r)});case"SELECTION_SET":return{...t,ids:e.ids,mostRecent:e.mostRecent,anchor:e.anchor};case"SELECTION_MOST_RECENT":return{...t,mostRecent:e.id};case"SELECTION_ANCHOR":return{...t,anchor:e.id};default:return t}}const ce={cursor(t){return{type:"DND_CURSOR",cursor:t}},dragStart(t,e){return{type:"DND_DRAG_START",id:t,dragIds:e}},dragEnd(){return{type:"DND_DRAG_END"}},hovering(t,e){return{type:"DND_HOVERING",parentId:t,index:e}}};function zs(t=pe().dnd,e){switch(e.type){case"DND_CURSOR":return{...t,cursor:e.cursor};case"DND_DRAG_START":return{...t,dragId:e.id,dragIds:e.dragIds};case"DND_DRAG_END":return pe().dnd;case"DND_HOVERING":return{...t,parentId:e.parentId,index:e.index};default:return t}}const Us={position:"fixed",pointerEvents:"none",zIndex:100,left:0,top:0,width:"100%",height:"100%"},Ws=t=>{if(!t)return{display:"none"};const{x:e,y:r}=t;return{transform:`translate(${e}px, ${r}px)`}},Bs=t=>{if(!t)return{display:"none"};const{x:e,y:r}=t;return{transform:`translate(${e+10}px, ${r+10}px)`}};function Gr({offset:t,mouse:e,id:r,dragIds:n,isDragging:i}){return l.jsxs(qs,{isDragging:i,children:[l.jsx(Vs,{offset:t,children:l.jsx(Ks,{id:r,dragIds:n})}),l.jsx(Gs,{mouse:e,count:n.length})]})}const qs=d.memo(function(e){return e.isDragging?l.jsx("div",{style:Us,children:e.children}):null});function Vs(t){return l.jsx("div",{className:"row preview",style:Ws(t.offset),children:t.children})}function Gs(t){const{count:e,mouse:r}=t;return e>1?l.jsx("div",{className:"selected-count",style:Bs(r),children:e}):null}const Ks=d.memo(function(e){const r=U(),n=r.get(e.id);return n?l.jsx(r.renderNode,{preview:!0,node:n,style:{paddingLeft:n.level*r.indent,opacity:.2,background:"transparent"},tree:r}):null});function Ys(){const t=U(),r=ps().cursor;if(!r||r.type!=="line")return null;const n=t.indent,i=t.rowHeight*r.index+(t.props.padding??t.props.paddingTop??0),a=n*r.level,o=t.renderCursor;return l.jsx(o,{top:i,left:a,indent:n})}const Xs=d.forwardRef(function(e,r){const{children:n,...i}=e,a=U();return l.jsxs("div",{ref:r,...i,onClick:o=>{o.currentTarget===o.target&&a.deselectAll()},children:[l.jsx(Qs,{}),n]})}),Qs=()=>{const t=U();return l.jsx("div",{style:{height:t.visibleNodes.length*t.rowHeight,width:"100%",position:"absolute",left:"0",right:"0"},children:l.jsx(Ys,{})})},Js=d.forwardRef(function({style:e,...r},n){const i=U(),a=i.props.padding??i.props.paddingTop??0,o=i.props.padding??i.props.paddingBottom??0;return l.jsx("div",{ref:n,style:{...e,height:`${parseFloat(e.height)+a+o}px`},...r})});function Zs(t){const e=U(),r=e.selectedIds,[n,i,a]=Mo(()=>({canDrag:()=>t.isDraggable,type:"NODE",item:()=>{const o=e.isSelected(t.id)?Array.from(r):[t.id];return e.dispatch(ce.dragStart(t.id,o)),{id:t.id}},end:()=>{e.hideCursor();let{parentId:o,index:u,dragIds:s}=e.state.dnd;e.canDrop()&&(Vr(e.props.onMove,{dragIds:s,parentId:o===ge?null:o,index:u,dragNodes:e.dragNodes,parentNode:e.get(o)}),e.open(o)),e.dispatch(ce.dragEnd())}}),[r,t]);return d.useEffect(()=>{a(Xn())},[a]),i}function eu(t,e){const r=t.getBoundingClientRect(),n=e.x-Math.round(r.x),i=e.y-Math.round(r.y),a=r.height,o=i<a/2,u=!o,s=a/4,f=i>s&&i<a-s;return{x:n,inTopHalf:o,inBottomHalf:u,inMiddle:f,atTop:!f&&o,atBottom:!f&&u}}function tu(t,e,r,n){return t?t.isInternal?n.atTop?[e,t]:n.inMiddle?[t,t]:[t,r]:n.inTopHalf?[e,t]:[t,r]:[e,null]}function ru(t,e,r,n){const i=Math.round(Math.max(0,t.x-n)/n);let a,o;return e?r?(o=e.level,a=r.level):(o=e.level,a=0):(o=0,a=0),Lr(i,a,o)}function Ge(t,e){return{parentId:t||null,index:e}}function Ke(t,e){return{type:"line",index:t,level:e}}function nu(t){return{type:"highlight",id:t}}function iu(t,e){var a;let r=t;for(;r.parent&&r.level>e;)r=r.parent;const n=((a=r.parent)==null?void 0:a.id)||null,i=Fr(r)+1;return{parentId:n,index:i}}function Kr(t){var u;const e=eu(t.element,t.offset),{node:r,nextNode:n,prevNode:i}=t,[a,o]=tu(r,i,n,e);if(r&&r.isInternal&&e.inMiddle)return{drop:Ge(r.id,0),cursor:nu(r.id)};if(!a)return{drop:Ge((u=o==null?void 0:o.parent)==null?void 0:u.id,0),cursor:Ke(0,0)};if(jr(a)||Hr(a)){const s=ru(e,a,o,t.indent);return{drop:iu(a,s),cursor:Ke(a.rowIndex+1,s)}}return{drop:Ge(a==null?void 0:a.id,0),cursor:Ke(a.rowIndex+1,a.level+1)}}function au(t,e){const r=U(),[n,i]=_r(()=>({accept:"NODE",canDrop:()=>r.canDrop(),hover:(a,o)=>{const u=o.getClientOffset();if(!t.current||!u)return;const{cursor:s,drop:f}=Kr({element:t.current,offset:u,indent:r.indent,node:e,prevNode:e.prev,nextNode:e.next});f&&r.dispatch(ce.hovering(f.parentId,f.index)),o.canDrop()?s&&r.showCursor(s):r.hideCursor()},drop:(a,o)=>{if(!o.canDrop())return null}}),[e,t.current,r.props]);return i}function ou(t){const e=U(),r=e.at(t);if(!r)throw new Error(`Could not find node for index: ${t}`);return d.useMemo(()=>{const n=r.clone();return e.visibleNodes[t]=n,n},[...Object.values(r.state),r])}const su=le.memo(function({index:e,style:r}){Mr(),gs();const n=U(),i=ou(e),a=d.useRef(null),o=Zs(i),u=au(a,i),s=d.useCallback(c=>{a.current=c,u(c)},[u]),f=n.indent*i.level,h=d.useMemo(()=>({paddingLeft:f}),[f]),v=d.useMemo(()=>({...r,top:parseFloat(r.top)+(n.props.padding??n.props.paddingTop??0)}),[r,n.props.padding,n.props.paddingTop]),b={role:"treeitem","aria-level":i.level,"aria-selected":i.isSelected,style:v,tabIndex:-1,className:n.props.rowClassName};d.useEffect(()=>{var c;!i.isEditing&&i.isFocused&&((c=a.current)==null||c.focus({preventScroll:!0}))},[i.isEditing,i.isFocused,a.current]);const m=n.renderNode,w=n.renderRow;return l.jsx(w,{node:i,innerRef:s,attrs:b,children:l.jsx(m,{node:i,tree:n,style:h,dragHandle:o})})});let Ye="",or=null;function Yr(){Mr();const t=U();return l.jsx("div",{style:{height:t.height,width:t.width,minHeight:0,minWidth:0},onContextMenu:t.props.onContextMenu,onClick:t.props.onClick,tabIndex:0,onFocus:e=>{e.currentTarget.contains(e.relatedTarget)||t.onFocus()},onBlur:e=>{e.currentTarget.contains(e.relatedTarget)||t.onBlur()},onKeyDown:e=>{var n;if(t.isEditing)return;if(e.key==="Backspace"){if(!t.props.onDelete)return;const i=Array.from(t.selectedIds);if(i.length>1){let a=t.mostRecentNode;for(;a&&a.isSelected;)a=a.nextSibling;a||(a=t.lastNode),t.focus(a,{scroll:!1}),t.delete(Array.from(i))}else{const a=t.focusedNode;if(a){const o=a.nextSibling,u=a.parent;t.focus(o||u,{scroll:!1}),t.delete(a)}}return}if(e.key==="Tab"&&!e.shiftKey){e.preventDefault(),Wr(e.currentTarget);return}if(e.key==="Tab"&&e.shiftKey){e.preventDefault(),Br(e.currentTarget);return}if(e.key==="ArrowDown"){e.preventDefault();const i=t.nextNode;if(e.metaKey){t.select(t.focusedNode),t.activate(t.focusedNode);return}else if(!e.shiftKey||t.props.disableMultiSelection){t.focus(i);return}else{if(!i)return;const a=t.focusedNode;a?a.isSelected?t.selectContiguous(i):t.selectMulti(i):t.focus(t.firstNode);return}}if(e.key==="ArrowUp"){e.preventDefault();const i=t.prevNode;if(!e.shiftKey||t.props.disableMultiSelection){t.focus(i);return}else{if(!i)return;const a=t.focusedNode;a?a.isSelected?t.selectContiguous(i):t.selectMulti(i):t.focus(t.lastNode);return}}if(e.key==="ArrowRight"){const i=t.focusedNode;if(!i)return;i.isInternal&&i.isOpen?t.focus(t.nextNode):i.isInternal&&t.open(i.id);return}if(e.key==="ArrowLeft"){const i=t.focusedNode;if(!i||i.isRoot)return;i.isInternal&&i.isOpen?t.close(i.id):(n=i.parent)!=null&&n.isRoot||t.focus(i.parent);return}if(e.key==="a"&&e.metaKey&&!t.props.disableMultiSelection){e.preventDefault(),t.selectAll();return}if(e.key==="a"&&!e.metaKey&&t.props.onCreate){t.createLeaf();return}if(e.key==="A"&&!e.metaKey){if(!t.props.onCreate)return;t.createInternal();return}if(e.key==="Home"){e.preventDefault(),t.focus(t.firstNode);return}if(e.key==="End"){e.preventDefault(),t.focus(t.lastNode);return}if(e.key==="Enter"){const i=t.focusedNode;if(!i||!i.isEditable||!t.props.onRename)return;setTimeout(()=>{i&&t.edit(i)});return}if(e.key===" "){e.preventDefault();const i=t.focusedNode;if(!i)return;i.isLeaf?(i.select(),i.activate()):i.toggle();return}if(e.key==="*"){const i=t.focusedNode;if(!i)return;t.openSiblings(i);return}if(e.key==="PageUp"){e.preventDefault(),t.pageUp();return}e.key==="PageDown"&&(e.preventDefault(),t.pageDown()),clearTimeout(or),Ye+=e.key,or=setTimeout(()=>{Ye=""},600);const r=t.visibleNodes.find(i=>{const a=i.data.name;return typeof a=="string"?a.toLowerCase().startsWith(Ye):!1});r&&t.focus(r.id)},children:l.jsx(hs,{className:t.props.className,outerRef:t.listEl,itemCount:t.visibleNodes.length,height:t.height,width:t.width,itemSize:t.rowHeight,overscanCount:t.overscanCount,itemKey:e=>{var r;return((r=t.visibleNodes[e])==null?void 0:r.id)||e},outerElementType:Xs,innerElementType:Js,onScroll:t.props.onScroll,onItemsRendered:t.onItemsRendered.bind(t),ref:t.list,children:su})})}function sr(t){return t.isFiltered?cu(t.root,t.isMatch.bind(t)):uu(t.root)}function uu(t){const e=[];function r(n){var i;n.level>=0&&e.push(n),n.isOpen&&((i=n.children)==null||i.forEach(r))}return r(t),e.forEach(Xr),e}function cu(t,e){const r={},n=[];function i(o){if(!o.isRoot&&e(o)){r[o.id]=!0;let s=o.parent;for(;s;)r[s.id]=!0,s=s.parent}if(o.children)for(let s of o.children)i(s)}function a(o){var u;o.level>=0&&r[o.id]&&n.push(o),o.isOpen&&((u=o.children)==null||u.forEach(a))}return i(t),a(t),n.forEach(Xr),n}function Xr(t,e){t.rowIndex=e}const ur=t=>t.reduce((e,r,n)=>(e[r.id]=n,e),{}),{safeRun:L,identify:Q,identifyNull:G}=k;class se{constructor(e,r,n,i){this.store=e,this.props=r,this.list=n,this.listEl=i,this.visibleStartIndex=0,this.visibleStopIndex=0,this.root=ir(this),this.visibleNodes=sr(this),this.idToIndex=ur(this.visibleNodes)}update(e){this.props=e,this.root=ir(this),this.visibleNodes=sr(this),this.idToIndex=ur(this.visibleNodes)}dispatch(e){return this.store.dispatch(e)}get state(){return this.store.getState()}get openState(){return this.state.nodes.open.unfiltered}get width(){return this.props.width??300}get height(){return this.props.height??500}get indent(){return this.props.indent??24}get rowHeight(){return this.props.rowHeight??24}get overscanCount(){return this.props.overscanCount??1}get searchTerm(){return(this.props.searchTerm||"").trim()}get matchFn(){const e=this.props.searchMatch??((r,n)=>JSON.stringify(Object.values(r.data)).toLocaleLowerCase().includes(n.toLocaleLowerCase()));return r=>e(r,this.searchTerm)}accessChildren(e){const r=this.props.childrenAccessor||"children";return k.access(e,r)??null}accessId(e){const r=this.props.idAccessor||"id",n=k.access(e,r);if(!n)throw new Error("Data must contain an 'id' property or props.idAccessor must return a string");return n}get firstNode(){return this.visibleNodes[0]??null}get lastNode(){return this.visibleNodes[this.visibleNodes.length-1]??null}get focusedNode(){return this.get(this.state.nodes.focus.id)??null}get mostRecentNode(){return this.get(this.state.nodes.selection.mostRecent)??null}get nextNode(){const e=this.indexOf(this.focusedNode);return e===null?null:this.at(e+1)}get prevNode(){const e=this.indexOf(this.focusedNode);return e===null?null:this.at(e-1)}get(e){return e&&e in this.idToIndex&&this.visibleNodes[this.idToIndex[e]]||null}at(e){return this.visibleNodes[e]||null}nodesBetween(e,r){if(e===null||r===null)return[];const n=this.indexOf(e)??0,i=this.indexOf(r);if(i===null)return[];const a=Math.min(n,i),o=Math.max(n,i);return this.visibleNodes.slice(a,o+1)}indexOf(e){const r=k.identifyNull(e);return r?this.idToIndex[r]:null}get editingId(){return this.state.nodes.edit.id}createInternal(){return this.create({type:"internal"})}createLeaf(){return this.create({type:"leaf"})}async create(e={}){const r=e.parentId===void 0?k.getInsertParentId(this):e.parentId,n=e.index??k.getInsertIndex(this),i=e.type??"leaf",a=await L(this.props.onCreate,{type:i,parentId:r,index:n,parentNode:this.get(r)});a&&(this.focus(a),setTimeout(()=>{this.edit(a).then(()=>{this.select(a),this.activate(a)})}))}async delete(e){if(!e)return;const n=(Array.isArray(e)?e:[e]).map(Q),i=n.map(a=>this.get(a)).filter(a=>!!a);await L(this.props.onDelete,{nodes:i,ids:n})}edit(e){const r=Q(e);return this.resolveEdit({cancelled:!0}),this.scrollTo(r),this.dispatch(Ve(r)),new Promise(n=>{se.editPromise=n})}async submit(e,r){if(!e)return;const n=Q(e);await L(this.props.onRename,{id:n,name:r,node:this.get(n)}),this.dispatch(Ve(null)),this.resolveEdit({cancelled:!1,value:r}),setTimeout(()=>this.onFocus())}reset(){this.dispatch(Ve(null)),this.resolveEdit({cancelled:!0}),setTimeout(()=>this.onFocus())}activate(e){const r=this.get(G(e));r&&L(this.props.onActivate,r)}resolveEdit(e){const r=se.editPromise;r&&r(e),se.editPromise=null}get selectedIds(){return this.state.nodes.selection.ids}get selectedNodes(){let e=[];for(let r of Array.from(this.selectedIds)){const n=this.get(r);n&&e.push(n)}return e}focus(e,r={}){e&&(this.props.selectionFollowsFocus?this.select(e):(this.dispatch(oe(Q(e))),r.scroll!==!1&&this.scrollTo(e),this.focusedNode&&L(this.props.onFocus,this.focusedNode)))}pageUp(){var a;const e=this.visibleStartIndex,n=this.visibleStopIndex-e;let i=((a=this.focusedNode)==null?void 0:a.rowIndex)??0;i>e?i=e:i=Math.max(e-n,0),this.focus(this.at(i))}pageDown(){var a;const e=this.visibleStartIndex,r=this.visibleStopIndex,n=r-e;let i=((a=this.focusedNode)==null?void 0:a.rowIndex)??0;i<r?i=r:i=Math.min(i+n,this.visibleNodes.length-1),this.focus(this.at(i))}select(e,r={}){if(!e)return;const n=r.focus!==!1,i=Q(e);n&&this.dispatch(oe(i)),this.dispatch(V.only(i)),this.dispatch(V.anchor(i)),this.dispatch(V.mostRecent(i)),this.scrollTo(i,r.align),this.focusedNode&&n&&L(this.props.onFocus,this.focusedNode),L(this.props.onSelect,this.selectedNodes)}deselect(e){if(!e)return;const r=Q(e);this.dispatch(V.remove(r))}selectMulti(e){const r=this.get(G(e));r&&(this.dispatch(oe(r.id)),this.dispatch(V.add(r.id)),this.dispatch(V.anchor(r.id)),this.dispatch(V.mostRecent(r.id)),this.scrollTo(r),this.focusedNode&&L(this.props.onFocus,this.focusedNode),L(this.props.onSelect,this.selectedNodes))}selectContiguous(e){if(!e)return;const r=Q(e),{anchor:n,mostRecent:i}=this.state.nodes.selection;this.dispatch(oe(r)),this.dispatch(V.remove(this.nodesBetween(n,i))),this.dispatch(V.add(this.nodesBetween(n,G(r)))),this.dispatch(V.mostRecent(r)),this.scrollTo(r),this.focusedNode&&L(this.props.onFocus,this.focusedNode),L(this.props.onSelect,this.selectedNodes)}deselectAll(){this.setSelection({ids:[],anchor:null,mostRecent:null}),L(this.props.onSelect,this.selectedNodes)}selectAll(){var e;this.setSelection({ids:Object.keys(this.idToIndex),anchor:this.firstNode,mostRecent:this.lastNode}),this.dispatch(oe((e=this.lastNode)==null?void 0:e.id)),this.focusedNode&&L(this.props.onFocus,this.focusedNode),L(this.props.onSelect,this.selectedNodes)}setSelection(e){var a;const r=new Set((a=e.ids)==null?void 0:a.map(Q)),n=G(e.anchor),i=G(e.mostRecent);this.dispatch(V.set({ids:r,anchor:n,mostRecent:i})),L(this.props.onSelect,this.selectedNodes)}get cursorParentId(){const{cursor:e}=this.state.dnd;switch(e.type){case"highlight":return e.id;default:return null}}get cursorOverFolder(){return this.state.dnd.cursor.type==="highlight"}get dragNodes(){return this.state.dnd.dragIds.map(e=>this.get(e)).filter(e=>!!e)}canDrop(){if(this.isFiltered)return!1;const e=this.get(this.state.dnd.parentId)??this.root,r=this.dragNodes,n=this.props.disableDrop;for(const i of r)if(!i||!e||i.isInternal&&k.isDecendent(e,i))return!1;return typeof n=="function"?!n({parentNode:e,dragNodes:this.dragNodes,index:this.state.dnd.index}):typeof n=="string"?!e.data[n]:typeof n=="boolean"?!n:!0}hideCursor(){this.dispatch(ce.cursor({type:"none"}))}showCursor(e){this.dispatch(ce.cursor(e))}open(e){const r=G(e);r&&(this.isOpen(r)||(this.dispatch(ot.open(r,this.isFiltered)),L(this.props.onToggle,r)))}close(e){const r=G(e);r&&this.isOpen(r)&&(this.dispatch(ot.close(r,this.isFiltered)),L(this.props.onToggle,r))}toggle(e){const r=G(e);if(r)return this.isOpen(r)?this.close(r):this.open(r)}openParents(e){const r=G(e);if(!r)return;const n=k.dfs(this.root,r);let i=n==null?void 0:n.parent;for(;i;)this.open(i.id),i=i.parent}openSiblings(e){const r=e.parent;if(!r)this.toggle(e.id);else if(r.children){const n=e.isOpen;for(let i of r.children)i.isInternal&&(n?this.close(i.id):this.open(i.id));this.scrollTo(this.focusedNode)}}openAll(){k.walk(this.root,e=>{e.isInternal&&e.open()})}closeAll(){k.walk(this.root,e=>{e.isInternal&&e.close()})}scrollTo(e,r="smart"){if(!e)return;const n=Q(e);return this.openParents(n),k.waitFor(()=>n in this.idToIndex).then(()=>{var a;const i=this.idToIndex[n];i!==void 0&&((a=this.list.current)==null||a.scrollToItem(i,r))}).catch(()=>{})}get isEditing(){return this.state.nodes.edit.id!==null}get isFiltered(){var e;return!!((e=this.props.searchTerm)!=null&&e.trim())}get hasFocus(){return this.state.nodes.focus.treeFocused}get hasNoSelection(){return this.state.nodes.selection.ids.size===0}get hasOneSelection(){return this.state.nodes.selection.ids.size===1}get hasMultipleSelections(){return this.state.nodes.selection.ids.size>1}isSelected(e){return e?this.state.nodes.selection.ids.has(e):!1}isOpen(e){if(!e)return!1;if(e===ge)return!0;const r=this.props.openByDefault??!0;return this.isFiltered?this.state.nodes.open.filtered[e]??!0:this.state.nodes.open.unfiltered[e]??r}isEditable(e){const r=this.props.disableEdit||(()=>!1);return!k.access(e,r)}isDraggable(e){const r=this.props.disableDrag||(()=>!1);return!k.access(e,r)}isDragging(e){const r=G(e);return r?this.state.nodes.drag.id===r:!1}isFocused(e){return this.hasFocus&&this.state.nodes.focus.id===e}isMatch(e){return this.matchFn(e)}willReceiveDrop(e){const r=G(e);return r?r===this.state.nodes.drag.idWillReceiveDrop:!1}onFocus(){const e=this.focusedNode||this.firstNode;e&&this.dispatch(oe(e.id))}onBlur(){this.dispatch(Ms())}onItemsRendered(e){this.visibleStartIndex=e.visibleStartIndex,this.visibleStopIndex=e.visibleStopIndex}get renderContainer(){return this.props.renderContainer||Yr}get renderRow(){return this.props.renderRow||_s}get renderNode(){return this.props.children||Ps}get renderDragPreview(){return this.props.renderDragPreview||Gr}get renderCursor(){return this.props.renderCursor||Ns}}function lu(t={id:null,idWillReceiveDrop:null},e){switch(e.type){case"DND_DRAG_START":return{...t,id:e.id};case"DND_DRAG_END":return{...t,id:null};case"DND_CURSOR":const r=e.cursor;return r.type==="highlight"&&r.id!==t.idWillReceiveDrop?{...t,idWillReceiveDrop:r.id}:r.type!=="highlight"&&t.idWillReceiveDrop!==null?{...t,idWillReceiveDrop:null}:t;default:return t}}const du=$t({nodes:$t({focus:Ls,edit:As,open:Hs,selection:Fs,drag:lu}),dnd:zs}),fu=pe();function hu({treeProps:t,imperativeHandle:e,children:r}){const n=d.useRef(null),i=d.useRef(null),a=d.useRef(ct(du,pe(t))),o=wn.useSyncExternalStore(a.current.subscribe,a.current.getState,()=>fu),u=d.useMemo(()=>new se(a.current,t,n,i),[]),s=d.useRef(0);return d.useMemo(()=>{s.current+=1,u.update(t)},[...Object.values(t),o.nodes.open]),d.useImperativeHandle(e,()=>u),d.useEffect(()=>{u.props.selection?u.select(u.props.selection,{focus:!1}):u.deselectAll()},[u.props.selection]),d.useEffect(()=>{u.props.searchTerm||a.current.dispatch(ot.clear(!0))},[u.props.searchTerm]),l.jsx(Pr.Provider,{value:u,children:l.jsx(Ar.Provider,{value:s.current,children:l.jsx(kr.Provider,{value:o.nodes,children:l.jsx(Rr.Provider,{value:o.dnd,children:l.jsx(_a,{backend:Qn,options:{rootElement:u.props.dndRootElement||void 0},...t.dndManager&&{manager:t.dndManager},children:r})})})})})}function gu(){const t=U(),[,e]=_r(()=>({accept:"NODE",canDrop:(r,n)=>n.isOver({shallow:!0})?t.canDrop():!1,hover:(r,n)=>{if(!n.isOver({shallow:!0}))return;const i=n.getClientOffset();if(!t.listEl.current||!i)return;const{cursor:a,drop:o}=Kr({element:t.listEl.current,offset:i,indent:t.indent,node:null,prevNode:t.visibleNodes[t.visibleNodes.length-1],nextNode:null});o&&t.dispatch(ce.hovering(o.parentId,o.index)),n.canDrop()?a&&t.showCursor(a):t.hideCursor()}}),[t]);e(t.listEl)}function pu(t){return gu(),t.children}function vu(){const e=U().props.renderContainer||Yr;return l.jsx(l.Fragment,{children:l.jsx(e,{})})}function mu(){const t=U(),{offset:e,mouse:r,item:n,isDragging:i}=ns(o=>({offset:o.getSourceClientOffset(),mouse:o.getClientOffset(),item:o.getItem(),isDragging:o.isDragging()})),a=t.props.renderDragPreview||Gr;return l.jsx(a,{offset:e,mouse:r,id:(n==null?void 0:n.id)||null,dragIds:(n==null?void 0:n.dragIds)||[],isDragging:i})}var yu={};A(yu,"useSimpleTree",()=>Zr);var bu={};A(bu,"SimpleTree",()=>Qr);class Qr{constructor(e){this.root=Su(e)}get data(){var e;return((e=this.root.children)==null?void 0:e.map(r=>r.data))??[]}create(e){const r=e.parentId?this.find(e.parentId):this.root;if(!r)return null;r.addChild(e.data,e.index)}move(e){const r=this.find(e.id),n=e.parentId?this.find(e.parentId):this.root;!r||!n||(n.addChild(r.data,e.index),r.drop())}update(e){const r=this.find(e.id);r&&r.update(e.changes)}drop(e){const r=this.find(e.id);r&&r.drop()}find(e,r=this.root){if(!r)return null;if(r.id===e)return r;if(r.children){for(let n of r.children){const i=this.find(e,n);if(i)return i}return null}return null}}function Su(t){const e=new Jr({id:"ROOT"},null);return e.children=t.map(r=>gt(r,e)),e}function gt(t,e){const r=new Jr(t,e);return t.children&&(r.children=t.children.map(n=>gt(n,r))),r}class Jr{constructor(e,r){this.data=e,this.parent=r,this.id=e.id}hasParent(){return!!this.parent}get childIndex(){return this.hasParent()?this.parent.children.indexOf(this):-1}addChild(e,r){const n=gt(e,this);this.children=this.children??[],this.children.splice(r,0,n),this.data.children=this.data.children??[],this.data.children.splice(r,0,e)}removeChild(e){var r,n;(r=this.children)==null||r.splice(e,1),(n=this.data.children)==null||n.splice(e,1)}update(e){if(this.hasParent()){const r=this.childIndex;this.parent.addChild({...this.data,...e},r),this.drop()}}drop(){this.hasParent()&&this.parent.removeChild(this.childIndex)}}let Du=0;function Zr(t){const[e,r]=d.useState(t),n=d.useMemo(()=>new Qr(e),[e]);return[e,{onMove:f=>{for(const h of f.dragIds)n.move({id:h,parentId:f.parentId,index:f.index});r(n.data)},onRename:({name:f,id:h})=>{n.update({id:h,changes:{name:f}}),r(n.data)},onCreate:({parentId:f,index:h,type:v})=>{const b={id:`simple-tree-id-${Du++}`,name:""};return v==="internal"&&(b.children=[]),n.create({parentId:f,index:h,data:b}),r(n.data),b},onDelete:f=>{f.ids.forEach(h=>n.drop({id:h})),r(n.data)}}]}function wu(t){if(t.initialData&&t.data)throw new Error("React Arborist Tree => Provide either a data or initialData prop, but not both.");if(t.initialData&&(t.onCreate||t.onDelete||t.onMove||t.onRename))throw new Error(`React Arborist Tree => You passed the initialData prop along with a data handler.
Use the data prop if you want to provide your own handlers.`);if(t.initialData){const[e,r]=Zr(t.initialData);return{...t,...r,data:e}}else return t}function Iu(t,e){const r=wu(t);return l.jsxs(hu,{treeProps:r,imperativeHandle:e,children:[l.jsx(pu,{children:l.jsx(vu,{})}),l.jsx(mu,{})]})}const Ou=d.forwardRef(Iu),Tu="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'%20standalone='no'?%3e%3csvg%20id='Group_106'%20data-name='Group%20106'%20width='351'%20height='351'%20viewBox='0%200%20350.99999%20351'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:svg='http://www.w3.org/2000/svg'%3e%3cdefs%20id='defs1'%3e%3cclipPath%20id='clip-path'%3e%3crect%20id='Rectangle_82'%20data-name='Rectangle%2082'%20width='15.996'%20height='12.441'%20fill='%230b4a82'%20x='0'%20y='0'%20/%3e%3c/clipPath%3e%3c/defs%3e%3cg%20id='Group_105'%20data-name='Group%20105'%20clip-path='url(%23clip-path)'%20transform='matrix(21.943818,0,0,24.999999,0,20)'%3e%3cpath%20id='Path_28'%20data-name='Path%2028'%20d='M%2012.441,3.554%20V%204.443%20H%2013.33%20V%203.554%20A%201.779,1.779%200%200%200%2011.553,1.777%20H%208.289%20A%201.779,1.779%200%200%201%207.031,1.258%20L%206.3,0.519%20A%201.777,1.777%200%200%200%205.038,0%20H%201.777%20A%201.779,1.779%200%200%200%200,1.777%20v%208.886%20a%201.779,1.779%200%200%200%201.777,1.777%20h%2011.108%20a%201.333,1.333%200%200%200%201.239,-0.839%20L%2015.9,7.159%20A%201.332,1.332%200%200%200%2014.661,5.332%20H%203.554%20A%201.333,1.333%200%200%200%202.315,6.171%20L%200.889,9.742%20V%201.777%20A%200.888,0.888%200%200%201%201.777,0.889%20H%205.04%20A%200.886,0.886%200%200%201%205.668,1.15%20L%206.3,0.522%205.672,1.15%206.408,1.886%20a%202.665,2.665%200%200%200%201.886,0.78%20h%203.263%20a%200.888,0.888%200%200%201%200.889,0.889%20m%20-1.777,8%20H%201.777%20A%200.439,0.439%200%200%201%201.41,11.361%200.448,0.448%200%200%201%201.366,10.947%20L%203.144,6.5%20A%200.444,0.444%200%200%201%203.558,6.22%20h%2011.105%20a%200.438,0.438%200%200%201%200.367,0.194%200.448,0.448%200%200%201%200.044,0.414%20L%2013.3,11.272%20a%200.443,0.443%200%200%201%20-0.411,0.281%20z'%20fill='%230b4a82'%20/%3e%3c/g%3e%3c/svg%3e",xu="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'%20standalone='no'?%3e%3csvg%20id='Component_270_8'%20data-name='Component%20270%20–%208'%20width='351'%20height='351'%20viewBox='0%200%20351%20351'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:svg='http://www.w3.org/2000/svg'%3e%3cdefs%20id='defs1'%3e%3cclipPath%20id='clip-path'%3e%3crect%20id='Rectangle_81'%20data-name='Rectangle%2081'%20width='16'%20height='14'%20fill='%230b4a82'%20x='0'%20y='0'%20/%3e%3c/clipPath%3e%3c/defs%3e%3cg%20id='Group_102'%20data-name='Group%20102'%20clip-path='url(%23clip-path)'%20transform='matrix(21.9375,0,0,22.214286,0,20)'%3e%3cpath%20id='Path_27'%20data-name='Path%2027'%20d='M%202,1%20A%201,1%200%200%200%201,2%20v%2010%20a%201,1%200%200%200%201,1%20h%2012%20a%201,1%200%200%200%201,-1%20V%204%20A%201,1%200%200%200%2014,3%20H%209.122%20A%202,2%200%200%201%207.706,2.416%20L%206.584,1.294%20A%201,1%200%200%200%205.878,1%20Z%20M%200,2%20A%202,2%200%200%201%202,0%20H%205.878%20A%202,2%200%200%201%207.294,0.584%20L%208.416,1.706%20A%201,1%200%200%200%209.122,2%20H%2014%20a%202,2%200%200%201%202,2%20v%208%20a%202,2%200%200%201%20-2,2%20H%202%20A%202,2%200%200%201%200,12%20Z'%20fill='%230b4a82'%20/%3e%3c/g%3e%3c/svg%3e",Cu=({expanded:t,node:e,toggleExpand:r,isLoading:n=!1})=>{var a;const i=o=>{o.stopPropagation(),r&&r()};return l.jsxs("div",{className:"flex items-center h-10 text-base p-small",style:{cursor:r?"pointer":"default"},children:[l.jsxs("div",{className:"flex items-center",children:[e.type!=="Component"&&l.jsxs(l.Fragment,{children:[n?l.jsx("div",{className:"flex-shrink-0 spinner-primary h-3 w-3 mr-2"}):l.jsx(rn,{direction:t?"down":"right",className:"flex-shrink-0 mr-2",onClick:i,size:12}),l.jsx("img",{src:t?Tu:xu,alt:t?"Open Folder":"Closed Folder",className:"flex-shrink-0 h-3.5 w-4 mr-2"})]}),e.type==="Component"&&l.jsxs(l.Fragment,{children:[n?l.jsx("div",{className:"flex-shrink-0 spinner-primary h-3 w-3 mr-2"}):l.jsx("div",{className:"flex-shrink-0 h-3 w-3 mr-2"}),((a=e.componentType)==null?void 0:a.icon)&&l.jsx("div",{className:"flex-shrink-0 mr-2",children:l.jsx(cr,{size:4,data:e.componentType.icon})})]})]}),l.jsx("span",{className:"flex-shrink-0 overflow-hidden whitespace-nowrap text-ellipsis",children:e.name})]})},Eu=le.memo(Cu),$u=(t={})=>{const{searchPanelRef:e,minHeight:r=300,maxHeightRatio:n=.8,paddingBottom:i=24,headerHeight:a=60}=t,[o,u]=d.useState(r),s=d.useCallback(()=>{var c;const f=window.innerHeight,h=((c=e==null?void 0:e.current)==null?void 0:c.offsetHeight)||120,b=f-a-h-i-20,m=f*n,w=Math.max(r,Math.min(b,m));u(g=>Math.abs(g-w)>10?w:g)},[e,r,n,i,a]);return d.useEffect(()=>{s();const f=()=>{s()};return window.addEventListener("resize",f),()=>window.removeEventListener("resize",f)},[s]),d.useEffect(()=>{if(!(e!=null&&e.current))return;const f=new ResizeObserver(()=>{s()});return f.observe(e.current),()=>{f.disconnect()}},[s,e]),o},Nu=t=>{const[e,r]=d.useState(394),n=d.useCallback(()=>{var b;const i=((b=t==null?void 0:t.current)==null?void 0:b.offsetWidth)||420,f=12+8+2+6,h=i-f,v=Math.max(300,Math.min(h,i-20));r(v)},[t]);return d.useEffect(()=>{n();const i=()=>{n()};return window.addEventListener("resize",i),()=>window.removeEventListener("resize",i)},[n]),d.useEffect(()=>{if(!(t!=null&&t.current))return;const i=new ResizeObserver(()=>{n()});return i.observe(t.current),()=>{i.disconnect()}},[n,t]),e};function _u(t,e){const[r,n]=d.useState(t);return d.useEffect(()=>{const i=setTimeout(()=>{n(t)},e);return()=>{clearTimeout(i)}},[t,e]),r}function Pu(t){const e=d.useContext(ve);if(!e)throw new Error("useTreeNode must be used within a TreeProvider");const r=e.isNodeExpanded(t),n=d.useCallback(()=>{e.toggleNodeExpansion(t)},[t,r,e]),i=d.useCallback(()=>{r||e.expandNode(t)},[t,r,e]),a=d.useCallback(()=>{r&&e.collapseNode(t)},[t,r,e]);return{isExpanded:r,toggleExpand:n,expand:i,collapse:a}}const ku=le.memo(({node:t,style:e,dragHandle:r,updateNodeInTree:n,selectedNodeId:i,onComponentSelect:a})=>{const o=lr(),u=d.useContext(ve),{isExpanded:s,toggleExpand:f}=Pu(t.id),h=d.useRef(!1),{prefetchEnabled:v=!0,prefetchDelay:b=500}=u||{},m=d.useRef(null),[w,c]=d.useState(!1);d.useEffect(()=>{t.isOpen!==s&&t.toggle()},[t,s]);const g=d.useCallback(async()=>{try{if(t.data.type==="Directory"){const D=await o.fetchQuery({queryKey:["directoryChildren",t.data.id,1],queryFn:()=>Xe(t.data.id,1,50)});n({...t.data,children:D.nodes.map(T=>({...T,path:[...t.data.path,T.name]})),isLoading:!1,hasMoreChildren:D.hasMorePages,currentPage:1,totalCount:D.totalCount}),u&&u.expandNode(t.id)}else if(t.data.type==="Component"){const[D,T]=await Promise.all([o.fetchQuery({queryKey:["componentDetails",t.data.id],queryFn:()=>mt(t.data.id)}),o.fetchQuery({queryKey:["versionDetails",t.data.id],queryFn:()=>Qe(t.data.id)})]);if(n({...t.data,isLoading:!1}),u&&u.expandNode(t.id),h.current&&D&&T){const S=T.versions||[],R={...D,versions:S,localVersion:null,backupJobConfigured:!1};a(R,t.data.path),h.current=!1}}}catch{n({...t.data,isLoading:!1})}},[t.data,t.id,o,n,a,h,u]);d.useEffect(()=>{t.data.isLoading&&g()},[t.data.isLoading,g]);const p=d.useCallback(()=>{var T;t.data.type==="Component"?(n({...t.data,isLoading:!0}),h.current=!0):!s&&!((T=t.data.children)!=null&&T.length)?(n({...t.data,isLoading:!0}),h.current=!0):f()},[t,s,n,f]),y=d.useCallback(()=>{if(!(!v||w)){if(t.data.type==="Component"){if(o.getQueryData(["componentDetails",t.data.id]))return}else if(t.data.type==="Directory"&&o.getQueryData(["directoryChildren",t.data.id,1]))return;m.current=window.setTimeout(()=>{c(!0),t.data.type==="Component"?(o.prefetchQuery({queryKey:["componentDetails",t.data.id],queryFn:()=>mt(t.data.id)}).catch(()=>{}),o.prefetchQuery({queryKey:["versionDetails",t.data.id],queryFn:()=>Qe(t.data.id)}).catch(()=>{})):t.data.type==="Directory"&&o.prefetchQuery({queryKey:["directoryChildren",t.data.id,1],queryFn:()=>Xe(t.data.id,1,50)}).catch(()=>{})},b)}},[t.data,o,w,v,b]),I=d.useCallback(()=>{v&&(m.current&&(clearTimeout(m.current),m.current=null),c(!1))},[v]);d.useEffect(()=>()=>{m.current&&clearTimeout(m.current)},[]);const O=t.id===i&&t.data.type==="Component";return l.jsxs("div",{style:{...e,width:"100%",height:"48px",display:"flex",alignItems:"center",position:"relative"},ref:r,className:O?"bg-secondary-background":"hover:bg-secondary-background",onMouseEnter:y,onMouseLeave:I,onClick:p,role:"button","aria-expanded":s,children:[O&&l.jsx("div",{className:"tree-node-selected-indicator"}),l.jsx(Eu,{expanded:s,node:t.data,toggleExpand:p,isLoading:t.data.isLoading})]})},(t,e)=>t.node.id===e.node.id&&t.node.isOpen===e.node.isOpen&&t.node.data.isLoading===e.node.data.isLoading&&t.selectedNodeId===e.selectedNodeId&&t.style.paddingLeft===e.style.paddingLeft),Ru=({data:t,onComponentSelect:e,searchTerm:r="",calculatedHeight:n})=>{const i=d.useContext(ve),a=d.useRef(!1),[o,u]=d.useState(!1),s=d.useRef(0),f=500,h=d.useRef(null),v=d.useRef(new Set),b=d.useRef(null),m=_u(r,500),w=d.useMemo(()=>(m==null?void 0:m.toLowerCase())??"",[m]),c=d.useMemo(()=>t.map(x=>({...x,path:[x.name],children:x.children?x.children.map(E=>({...E,path:[x.name,E.name],children:void 0})):void 0})),[t]),[g,p]=d.useState(c),y=d.useRef(null),I=d.useRef(null),O=lr();d.useEffect(()=>{if(c&&c.length>0){const x=c.map(E=>({...E,isOpen:E.children&&E.children.length>0?!0:E.isOpen}));p(x)}},[c]);const D=d.useCallback(x=>{var P,_;if(!w)return x;if(!(x!=null&&x.length))return[];const E=[];for(const M of x){const j=M.name.toLowerCase().includes(w);let F=[];if(j&&!((P=M.children)!=null&&P.length)){E.push(M);continue}(_=M.children)!=null&&_.length&&(F=D(M.children)),(j||F.length)&&E.push({...M,children:F})}return E},[w]),T=d.useMemo(()=>w?D(g):g,[g,D,w]),S=d.useCallback(x=>{p(E=>{if(!E.length)return E;const P=_=>_.map(M=>{var j;return M.id===x.id?{...M,...x}:(j=M.children)!=null&&j.length?{...M,children:P(M.children)}:M});return P(E)})},[]),R=d.useCallback((x,E,P)=>{x&&P&&(y.current=P,b.current={details:x,path:E}),e(x,E)},[e]),$=d.useCallback(x=>l.jsx(ku,{...x,updateNodeInTree:S,selectedNodeId:y.current,onComponentSelect:(E,P)=>R(E,P,x.node.id)}),[S,R]);d.useEffect(()=>{if(!w&&b.current){const{details:x,path:E}=b.current;e(x,E)}},[w,e]);const W=n||600,Y=Nu(h),Z=d.useCallback(()=>{if(!I.current)return null;const x=I.current.visibleNodes||[],E=[];for(let P=0;P<x.length;P++){const _=x[P];if(_.data.type!=="Directory"||!_.isOpen)continue;if(_.data.hasMoreChildren===!0&&!_.data.isLoading&&!_.data.isLoadingMore){let j=P;for(let F=P+1;F<x.length;F++){const X=x[F];if(X.data.path&&_.data.path&&X.data.path.length>_.data.path.length&&X.data.path.slice(0,_.data.path.length).join("/")===_.data.path.join("/"))j=F;else break}E.push({node:_.data,treeNode:_,lastChildIndex:j,directoryIndex:P})}}return E.length===0?null:(E.sort((P,_)=>_.lastChildIndex-P.lastChildIndex),E[0])},[]),me=d.useCallback(async()=>{if(a.current||o)return;const x=Date.now();if(x-s.current<f)return;const P=Z();if(!P){a.current=!1;return}const{node:_}=P;v.current.add(_.id),a.current=!0,s.current=x,S({..._,isLoadingMore:!0});try{const j=(_.currentPage||1)+1,F=await O.fetchQuery({queryKey:["directoryChildren",_.id,j],queryFn:()=>Xe(_.id,j,50)}),X=[..._.children||[],...F.nodes.map(J=>({...J,path:[..._.path,J.name]}))];S({..._,children:X,hasMoreChildren:F.hasMorePages,currentPage:j,isLoadingMore:!1})}catch{S({..._,isLoadingMore:!1})}finally{a.current=!1,u(!1)}},[Z,o,O,S]),ye=d.useRef(me);ye.current=me,d.useEffect(()=>{const x=I.current;if(x&&x.listEl&&x.listEl.current&&typeof x.listEl.current.addEventListener=="function"){const E=x.listEl.current;let P=!1;const _=()=>{if(a.current||!E)return;const{scrollTop:M,scrollHeight:j,clientHeight:F}=E;j-M-F<300&&(P||(window.requestAnimationFrame(()=>{ye.current(),P=!1}),P=!0))};return E.addEventListener("scroll",_,{passive:!0}),()=>{E&&typeof E.removeEventListener=="function"&&E.removeEventListener("scroll",_)}}},[]);const Re=d.useMemo(()=>{const x=(i==null?void 0:i.loading)||!1,E=!T||T.length===0;return x&&E?l.jsx("div",{className:"relative",ref:h,style:{height:`${W}px`},children:l.jsx("div",{className:"absolute inset-0 flex-center",children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"spinner-primary spinner-lg mb-2"}),l.jsx("p",{className:"text-border-color-dark text-sm",children:"Loading tree data..."})]})})}):l.jsxs("div",{className:"relative",ref:h,style:{height:`${W}px`},children:[l.jsx(Ou,{ref:I,data:T,width:Y,height:W,indent:15,rowHeight:48,overscanCount:20,disableDrag:!0,disableDrop:!0,className:"tree-scroll-container",children:$}),o&&l.jsx("div",{className:"absolute bottom-0 left-0 right-0 flex-center py-2 bg-background",children:l.jsx("div",{className:"spinner-primary spinner-md"})})]})},[T,W,Y,$,o]);return l.jsx("div",{className:"h-full",children:Re})},Au=le.memo(Ru),Mu=({placeholder:t,onSearch:e,value:r,onDropdownClick:n})=>{const i=a=>{e&&e(a.target.value)};return l.jsxs("div",{className:"flex items-center rounded-br-sm relative w-full h-10 text-border-color-dark placeholder:text-border-color-dark bg-inherit",children:[l.jsx("span",{className:"h-full border-l-4 border-primary"}),l.jsx("input",{type:"text",className:"grow bg-inherit outline-0 text-medium p-small ml-small uppercase border-b-2 border-border-color m-0 border-0 h-full",placeholder:t,onChange:i,value:r}),l.jsx("button",{className:"h-full w-10 bg-border-color flex-center rounded-tr-sm rounded-br-sm hover:bg-hover",onClick:n,children:l.jsx("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",className:"w-5 h-5 fill-none stroke-border-color-dark",children:l.jsx("path",{d:"M7 10l5 5 5-5",className:"stroke-background",fill:"none",strokeWidth:"2"})})})]})},Lu=(t,e={})=>{const{enabled:r=!0,keepPreviousData:n=!0}=e,{isAuthenticated:i,isTokenReady:a}=nn();return an({queryKey:["componentSearch",t],queryFn:()=>{if(!t)return Promise.resolve([]);if(t.trim()==="")throw new Error("Empty search term provided to useComponentSearch hook");return on(t)},enabled:i&&a&&!!t&&r,staleTime:2*60*1e3,gcTime:5*60*1e3,retry:(o,u)=>{var s;return(s=u.message)!=null&&s.includes("Network Error")?o<2:!1},placeholderData:n?o=>o:void 0})},ju=({searchTerm:t,setSearchTerm:e})=>{const{t:r}=sn(),n=d.useContext(ve),[i,a]=d.useState(!1),[o,u]=d.useState({left:0,top:0,width:0}),s=d.useRef(null),f=un();if(!n)throw new Error("TreeContext must be used within a TreeProvider");const{isTreeVisible:h,setSelectedNode:v,setSelectedDetails:b,handleComponentSelect:m,setCurrentDetailsPage:w,updateTreeWithDirectory:c}=n;d.useEffect(()=>{if(i&&s.current){const S=s.current.getBoundingClientRect();u({left:S.left,top:S.bottom+window.scrollY,width:S.width})}},[i]),d.useEffect(()=>{const S=R=>{s.current&&!s.current.contains(R.target)&&a(!1)};return document.addEventListener("mousedown",S),()=>{document.removeEventListener("mousedown",S)}},[]);const g=()=>{t&&a(!0)};d.useEffect(()=>{a(!!t)},[t]);const{data:p=[],isLoading:y,refetch:I,error:O}=Lu(t,{enabled:!1});d.useEffect(()=>{const S=setTimeout(()=>{t&&I()},300);return()=>clearTimeout(S)},[t,I]);const D=S=>{a(!1),e(""),v&&v(S);const R=S.path||[S.id];if(f(yt.TREE),c){const $={id:S.id,name:S.name,type:S.type,path:R,description:"",componentType:null};requestAnimationFrame(()=>{c(S.id,$).then(()=>{n.setSelectedPath&&n.setSelectedPath(R)}).catch(()=>{})})}},T=()=>{if(!i)return null;if(O)return l.jsx("div",{className:"px-4 py-3 text-center text-red-500",children:l.jsxs("div",{className:"flex items-center justify-center mb-1",children:[l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"h-5 w-5 mr-1",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),l.jsx("span",{children:typeof O=="string"?O:O instanceof Error?O.message:"Error searching"})]})});const S=p.filter($=>$.type==="Component"),R=p.filter($=>$.type!=="Component");return l.jsx("div",{className:"shadow-lg rounded-md border border-border-color max-h-80 overflow-y-auto bg-background fixed z-[999]",style:{left:`${o.left}px`,top:`${o.top}px`,width:`${o.width}px`},children:O?l.jsx("div",{className:"px-4 py-3 text-center text-gray-500",children:l.jsx("div",{className:"flex items-center justify-center w-full text-red-500 p-4",children:(O==null?void 0:O.message)||"Error loading tree data"})}):p.length>0?l.jsxs("div",{className:"py-1",children:[S.length>0&&l.jsxs(l.Fragment,{children:[l.jsx("div",{className:"px-4 py-2 bg-gray-100 font-medium text-gray-700",children:"Components"}),S.map($=>l.jsx("div",{className:"px-4 py-2 hover:bg-hover cursor-pointer pl-6",onClick:()=>{a(!1),e(""),v&&v($);const W=$.path||[$.id];Qe($.id).then(Y=>{const Z={name:$.name,id:$.id,componentType:$.componentType,versions:Y.versions,jobs:[],localVersion:null,backupJobConfigured:!1,masterData:[]};b&&b(Z),n.setSelectedPath&&n.setSelectedPath(W),m&&m(Z,W),w&&w(dn.ChangeHistory)}).catch(()=>{}),f(yt.TREE)},children:l.jsxs("div",{className:"flex items-center",children:[l.jsx("div",{className:"w-6 h-6 mr-2 flex-shrink-0 flex items-center justify-center",children:$.componentType&&$.componentType.icon?l.jsx(cr,{data:$.componentType.icon,size:4}):l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"h-5 w-5",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),l.jsxs("div",{className:"flex flex-col",children:[l.jsx("span",{className:"font-medium",children:$.name}),l.jsx("span",{className:"text-tiny text-gray-500",children:$.displayPath})]})]})},$.id))]}),R.length>0&&l.jsxs(l.Fragment,{children:[l.jsx("div",{className:"px-4 py-2 bg-gray-100 font-medium text-gray-700",children:"Directories"}),R.map($=>l.jsx("div",{className:"px-4 py-2 hover:bg-hover cursor-pointer pl-6",onClick:()=>D($),children:l.jsxs("div",{className:"flex items-center",children:[l.jsx("div",{className:"w-6 h-6 mr-2 flex-shrink-0 flex items-center justify-center",children:l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"h-5 w-5",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"})})}),l.jsxs("div",{className:"flex flex-col",children:[l.jsx("span",{className:"font-medium",children:$.name}),l.jsx("span",{className:"text-tiny text-gray-500",children:$.displayPath})]})]})},$.id))]})]}):l.jsxs("div",{className:"px-4 py-3 text-center text-gray-500",children:[l.jsxs("div",{className:"flex items-center justify-center mb-1",children:[l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"h-5 w-5 mr-1 text-gray-400",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),l.jsx("span",{children:"No results found"})]}),l.jsx("div",{className:"text-tiny",children:"Try a different search term"})]})})};return l.jsxs("div",{className:`flex-grow-0 overflow-visible ${h?"":"hidden"}`,children:[l.jsxs("div",{className:"text-2xl mr-large h-[60px] mt-8 uppercase",children:[l.jsx("span",{className:"font-bold",children:r("tree.project")})," ",r("tree.tree")]}),l.jsxs("div",{className:"pt-medium pb-medium mr-large relative",ref:s,children:[cn(ln.TreeSearch)&&l.jsx(Mu,{value:t,onSearch:e,placeholder:r("tree.searchPlaceholder"),onDropdownClick:g}),T()]})]})},Hu=({searchTerm:t,setSearchTerm:e})=>{const r=d.useRef(null),n=d.useRef(null),i=d.useContext(ve);if(!i)throw new Error("TreeView must be used within a TreeProvider");const{data:a,handleComponentSelect:o,isTreeVisible:u}=i,s=$u({searchPanelRef:r,minHeight:300,maxHeightRatio:.8,paddingBottom:24,headerHeight:60});return l.jsxs("div",{ref:n,className:`flex flex-col w-full ml-4 overflow-x-hidden h-full ${u?"":"hidden"}`,children:[l.jsx("div",{ref:r,children:l.jsx(ju,{searchTerm:t,setSearchTerm:e})}),l.jsx("div",{className:"flex-1 min-h-0",children:l.jsx(Au,{path:[],data:a,searchTerm:t||"",onComponentSelect:o,calculatedHeight:s})})]})},Wu=le.memo(Hu);export{Wu as default};
