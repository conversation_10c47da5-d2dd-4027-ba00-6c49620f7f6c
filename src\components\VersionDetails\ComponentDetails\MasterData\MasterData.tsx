import React from 'react';
import { ComponentDetails } from '@/utils/types';

import { useTranslation } from 'react-i18next';
import KeyValueFieldArea, {
  FieldProps,
} from '@/components/shared/KeyValueFieldArea/KeyValueFieldArea';

interface MasterDataProps {
  details: ComponentDetails;
}

const MasterData: React.FC<MasterDataProps> = ({ details }) => {
  const { t } = useTranslation();

  // Define field data for easy mapping
  // In this specific case, we do not want to display empty fields, so we select "undefined" for empty fields
  const fields: FieldProps[] = [
    { label: t('masterdata.masterdata1'), value: details.data1 || undefined },
    { label: t('masterdata.masterdata2'), value: details.data2 || undefined },
    { label: t('masterdata.masterdata3'), value: details.data3 || undefined },
    { label: t('masterdata.masterdata4'), value: details.data4 || undefined },
    { label: t('masterdata.masterdata5'), value: details.data5 || undefined },
    { label: t('masterdata.masterdata6'), value: details.data6 || undefined },
    { label: t('masterdata.masterdata7'), value: details.data7 || undefined },
    { label: t('masterdata.masterdata8'), value: details.data8 || undefined },
    { label: t('masterdata.masterdata9'), value: details.data9 || undefined },
    { label: t('masterdata.masterdata10'), value: details.data10 || undefined },
  ];

  return <KeyValueFieldArea fields={fields} />;
};
export default MasterData;
