# React Project

This project is a React application.

## Getting Started

These instructions will help you set up and run the project on your local machine.

### Prerequisites

Make sure you have the following installed on your machine:

- [Node.js](https://nodejs.org/) (which includes npm)
- Optional: [Docker](https://www.docker.com/) (optional, for running the project in a container)
- [Google Chrome CORS extension](https://chrome.google.com/webstore/detail/cors/ehlhhnffhgaipnpdbmoocpgifgplcjof) (optional, in some cases required to load the Tree component)

### Installing

Install the project dependencies:

```bash
npm install
```

This command installs all the dependencies listed in the `package.json` file. It ensures that you have all the necessary packages to run the project.

### Code Style and Linting

This project uses ESLint and Prettier for consistent code styling across the team. The configuration includes:

- ESLint for code quality and style checking
- Prettier for automatic code formatting
- EditorConfig for consistent editor settings
- <PERSON><PERSON> for pre-commit hooks

Available scripts:

```bash
npm run lint        # Check and fix linting issues
npm run lint:check  # Check for linting issues without fixing
npm run format      # Format all files using Prettier
npm run format:check # Check if files need formatting
```

Code will be automatically formatted and linted before each commit.

#### Recommended VS Code Settings

If you're using Visual Studio Code, we recommend the following settings in your `.vscode/settings.json`:

```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
```

Required VS Code extensions:

- ESLint
- Prettier - Code formatter

### Running the Project

To start the development server, run:

```bash
npm start
```

This command starts the development server and opens the application in your default web browser. The server will reload automatically if you make changes to the code.

### CORS Issues

In some cases, you might encounter CORS issues when trying to load the Tree component. To bypass these issues temporarily, you can use the [CORS extension for Google Chrome](https://chrome.google.com/webstore/detail/cors/ehlhhnffhgaipnpdbmoocpgifgplcjof).

### OPTIONAL: Running the Project with Docker

It's also possible to run the project within a Docker container. This is optional but provides a consistent development environment.

1. Build the Docker image:

   ```bash
   docker build
   ```

2. Run the Docker container:

   ```bash
   docker run -p 3000:3000 react-project
   ```

Alternatively, you can use `docker-compose` to manage the services:

1. Start the project with `docker-compose`:

   ```bash
   docker-compose up
   ```

This command will start all the services defined in the `docker-compose.yml` file, including the React application.

he project, installation instructions, details on running the project both locally and in a Docker container, handling CORS issues, and the project structure. It serves as a helpful guide for anyone looking to set up and work with your React project.
testing config changes

## Dummy Change for Testing
This is a simple change to test the PR workflow.
