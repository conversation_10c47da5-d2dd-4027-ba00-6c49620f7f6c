import React, { useEffect, useState } from 'react';

interface ComponentIconProps {
  data: string;
  size: number;
}

export const ComponentTypeIcon: React.FC<ComponentIconProps> = ({ data, size }) => {
  const [iconSrc, setIconSrc] = useState<string>('');

  useEffect(() => {
    if (!data) {
      return;
    }

    try {
      // For SVGs with embedded images, extract the PNG data URL directly
      if (data.includes('<svg') && data.includes('xlink:href="data:image/png;base64,')) {
        const match = data.match(/xlink:href="data:image\/png;base64,([^"]+)"/);
        if (match && match[1]) {
          setIconSrc(`data:image/png;base64,${match[1]}`);
          return;
        }
      }

      // For direct SVG content
      if (data.includes('<svg')) {
        setIconSrc(`data:image/svg+xml;charset=utf-8,${encodeURIComponent(data)}`);
        return;
      }

      // Default fallback - assume it's base64 data for an image
      setIconSrc(`data:image/png;base64,${data}`);
    } catch (e) {}
  }, [data]);

  if (!iconSrc) {
    return null;
  }

  return (
    <img
      alt="Component Icon"
      src={iconSrc}
      className={`h-${size} w-${size} flex-shrink-0`}
      onError={e => {
        console.error('[ComponentTypeIcon] Failed to load icon');
        // Hide the broken image
        (e.target as HTMLImageElement).style.display = 'none';
      }}
    />
  );
};

export default ComponentTypeIcon;
