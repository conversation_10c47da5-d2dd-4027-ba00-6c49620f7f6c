import React, { use<PERSON><PERSON>back, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { NodeRendererProps, Tree } from 'react-arborist';
import { ComponentDetails, TreeNodeType } from '@/utils/types';
import TreeHeader from '../Tree/TreeHeader/TreeNodeHeader';
import { useTreeWidth } from '@/hooks/useTreeHeight';

import { useQueryClient } from '@tanstack/react-query';
import { fetchComponentDetails, fetchTreeNodes, fetchVersionDetails } from '@/services/api/index';
import { useDebounce } from '@/hooks/utils/useDebounce';
import { TreeContext } from '@/context/TreeContext';
import { useTreeNode } from '@/hooks/useTreeNode';

interface ArboristTreeNodeProps {
  data: TreeNodeType[];
  searchTerm: string;
  path: string[];
  onComponentSelect: (details: ComponentDetails | null, path: string[]) => void;
  calculatedHeight?: number;
}

interface ArboristData extends Omit<TreeNodeType, 'children'> {
  children?: ArboristData[];
  isLoading?: boolean;
  hasMoreChildren?: boolean;
  currentPage?: number;
  totalCount?: number;
  path: string[];
}

interface TreeNodeRendererProps extends NodeRendererProps<ArboristData> {
  updateNodeInTree: (node: ArboristData) => void;
  selectedNodeId: string | null;
  onComponentSelect: (details: ComponentDetails | null, path: string[]) => void;
}

/**
 * Optimized TreeNodeRenderer component
 */
const TreeNodeRenderer = React.memo(
  ({
    node,
    style,
    dragHandle,
    updateNodeInTree,
    selectedNodeId,
    onComponentSelect,
  }: TreeNodeRendererProps) => {
    const queryClient = useQueryClient();
    const treeContext = useContext(TreeContext);
    const { isExpanded, toggleExpand } = useTreeNode(node.id);
    const isUserClickRef = useRef(false);

    // Get prefetching configuration from context or use defaults
    const { prefetchEnabled = true, prefetchDelay = 500 } = treeContext || {};
    const hoverTimerRef = useRef<number | null>(null);
    const [isPrefetching, setIsPrefetching] = useState(false);

    useEffect(() => {
      if (node.isOpen !== isExpanded) {
        node.toggle();
      }
    }, [node, isExpanded]);

    const loadNodeData = useCallback(async () => {
      try {
        if (node.data.type === 'Directory') {
          const firstPageResult = await queryClient.fetchQuery({
            queryKey: ['directoryChildren', node.data.id, 1],
            queryFn: () => fetchTreeNodes(node.data.id, 1, 50),
          });

          updateNodeInTree({
            ...node.data,
            children: firstPageResult.nodes.map(child => ({
              ...child,
              path: [...node.data.path, child.name],
            })),
            isLoading: false,
            hasMoreChildren: firstPageResult.hasMorePages,
            currentPage: 1,
            totalCount: firstPageResult.totalCount,
          });

          if (treeContext) {
            treeContext.expandNode(node.id);
          }
        } else if (node.data.type === 'Component') {
          const [details, versions] = await Promise.all([
            queryClient.fetchQuery({
              queryKey: ['componentDetails', node.data.id],
              queryFn: () => fetchComponentDetails(node.data.id),
            }),
            queryClient.fetchQuery({
              queryKey: ['versionDetails', node.data.id],
              queryFn: () => fetchVersionDetails(node.data.id),
            }),
          ]);

          updateNodeInTree({ ...node.data, isLoading: false });

          if (treeContext) {
            treeContext.expandNode(node.id);
          }

          if (isUserClickRef.current && details && versions) {
            const versionsArray = versions.versions || [];
            const newVersionDetails = {
              ...details,
              versions: versionsArray,
              localVersion: null,
              backupJobConfigured: false,
            };
            onComponentSelect(newVersionDetails, node.data.path);
            isUserClickRef.current = false;
          }
        }
      } catch (error) {
        updateNodeInTree({ ...node.data, isLoading: false });
      }
    }, [
      node.data,
      node.id,
      queryClient,
      updateNodeInTree,
      onComponentSelect,
      isUserClickRef,
      treeContext,
    ]);

    useEffect(() => {
      if (node.data.isLoading) {
        loadNodeData();
      }
    }, [node.data.isLoading, loadNodeData]);

    const handleToggle = useCallback(() => {
      const isComponent = node.data.type === 'Component';

      if (isComponent) {
        updateNodeInTree({ ...node.data, isLoading: true });
        isUserClickRef.current = true;
      } else {
        // It's a Directory
        if (!isExpanded && !node.data.children?.length) {
          // If it's collapsed AND has no children, it needs to load data.
          // Loading data will also trigger expansion via treeContext.expandNode in loadNodeData.
          updateNodeInTree({ ...node.data, isLoading: true });
          isUserClickRef.current = true;
        } else {
          // If it's a directory that is already expanded (isExpanded is true),
          // or it's collapsed (!isExpanded) but already has children,
          // then we just need to toggle its expansion state in our context.
          // The useEffect will take care of calling node.toggle() for react-arborist.
          toggleExpand();
        }
      }
    }, [node, isExpanded, updateNodeInTree, toggleExpand]);

    const handleMouseEnter = useCallback(() => {
      if (!prefetchEnabled || isPrefetching) return;

      if (node.data.type === 'Component') {
        const hasComponentData = queryClient.getQueryData(['componentDetails', node.data.id]);
        if (hasComponentData) return; // Don't prefetch if data already exists
      } else if (node.data.type === 'Directory') {
        // Check if the first page of directory children already exists in cache
        const hasDirectoryData = queryClient.getQueryData(['directoryChildren', node.data.id, 1]);
        if (hasDirectoryData) return; // Don't prefetch if data already exists
      }

      hoverTimerRef.current = window.setTimeout(() => {
        setIsPrefetching(true);

        if (node.data.type === 'Component') {
          queryClient
            .prefetchQuery({
              queryKey: ['componentDetails', node.data.id],
              queryFn: () => fetchComponentDetails(node.data.id),
            })
            .catch(() => {}); // Handle or log prefetch error if necessary

          queryClient
            .prefetchQuery({
              queryKey: ['versionDetails', node.data.id],
              queryFn: () => fetchVersionDetails(node.data.id),
            })
            .catch(() => {}); // Handle or log prefetch error if necessary
        } else if (node.data.type === 'Directory') {
          queryClient
            .prefetchQuery({
              queryKey: ['directoryChildren', node.data.id, 1], // Prefetch first page
              queryFn: () => fetchTreeNodes(node.data.id, 1, 50),
            })
            .catch(() => {}); // Handle or log prefetch error if necessary
        }
        // No need to clear hoverTimerRef.current here, it's done on timeout or mouseleave
      }, prefetchDelay);
    }, [node.data, queryClient, isPrefetching, prefetchEnabled, prefetchDelay]);

    const handleMouseLeave = useCallback(() => {
      if (!prefetchEnabled) return;

      if (hoverTimerRef.current) {
        clearTimeout(hoverTimerRef.current);
        hoverTimerRef.current = null;
      }
      // Reset prefetching state only if it was actively prefetching or about to.
      // This prevents resetting if mouse leaves after prefetch completed.
      // However, for simplicity and to ensure clean state on mouse re-enter, resetting here is fine.
      setIsPrefetching(false);
    }, [prefetchEnabled]);

    // Cleanup timer on unmount
    useEffect(() => {
      return () => {
        if (hoverTimerRef.current) {
          clearTimeout(hoverTimerRef.current);
        }
      };
    }, []);

    const isSelected = node.id === selectedNodeId && node.data.type === 'Component';

    return (
      <div
        style={{
          ...style,
          width: '100%',
          height: '48px',
          display: 'flex',
          alignItems: 'center',
          position: 'relative',
        }}
        ref={dragHandle}
        className={isSelected ? 'bg-secondary-background' : 'hover:bg-secondary-background'}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleToggle}
        role="button"
        aria-expanded={isExpanded}
      >
        {/* Blue collar indicator for selected nodes - positioned at the very start */}
        {isSelected && <div className="tree-node-selected-indicator" />}

        <TreeHeader
          expanded={isExpanded}
          node={node.data}
          toggleExpand={handleToggle}
          isLoading={node.data.isLoading}
        />
      </div>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.node.id === nextProps.node.id &&
      prevProps.node.isOpen === nextProps.node.isOpen &&
      prevProps.node.data.isLoading === nextProps.node.data.isLoading &&
      prevProps.selectedNodeId === nextProps.selectedNodeId &&
      prevProps.style.paddingLeft === nextProps.style.paddingLeft
    );
  }
);

const ArboristTreeNode: React.FC<ArboristTreeNodeProps> = ({
  data,
  onComponentSelect,
  searchTerm = '',
  calculatedHeight,
}) => {
  const treeContext = useContext(TreeContext);
  const isLoadingRef = useRef(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const lastLoadTime = useRef(0);
  const LOAD_THROTTLE_MS = 500;
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Track directories that can load more for debugging
  const expandedDirectoriesRef = useRef<Set<string>>(new Set());
  const lastSelectedNodeRef = useRef<{
    details: ComponentDetails | null;
    path: string[];
  } | null>(null);

  const debouncedSearchTerm = useDebounce(searchTerm, 500);
  const searchTermLower = useMemo(
    () => debouncedSearchTerm?.toLowerCase() ?? '',
    [debouncedSearchTerm]
  );

  const treeData = useMemo<ArboristData[]>(() => {
    return data.map(
      (node): ArboristData => ({
        ...node,
        path: [node.name],
        children: node.children
          ? node.children.map(
              (child): ArboristData => ({
                ...child,
                path: [node.name, child.name],
                children: undefined,
              })
            )
          : undefined,
      })
    );
  }, [data]);

  const [internalTreeData, setInternalTreeData] = useState<ArboristData[]>(treeData);
  const selectedNodeIdRef = useRef<string | null>(null);
  const treeRef = useRef<any>(null);
  const queryClient = useQueryClient();

  useEffect(() => {
    if (treeData && treeData.length > 0) {
      const enhancedTreeData = treeData.map(node => ({
        ...node,
        isOpen: node.children && node.children.length > 0 ? true : node.isOpen,
      }));

      setInternalTreeData(enhancedTreeData);
    }
  }, [treeData]);

  const filterTree = useCallback(
    (nodes: ArboristData[]): ArboristData[] => {
      if (!searchTermLower) return nodes;
      if (!nodes?.length) return [];

      const result: ArboristData[] = [];

      for (const node of nodes) {
        const nodeMatches = node.name.toLowerCase().includes(searchTermLower);
        let filteredChildren: ArboristData[] = [];

        if (nodeMatches && !node.children?.length) {
          result.push(node);
          continue;
        }

        if (node.children?.length) {
          filteredChildren = filterTree(node.children);
        }

        if (nodeMatches || filteredChildren.length) {
          result.push({
            ...node,
            children: filteredChildren,
          });
        }
      }

      return result;
    },
    [searchTermLower]
  );

  const filteredTreeData = useMemo(
    () => (searchTermLower ? filterTree(internalTreeData) : internalTreeData),
    [internalTreeData, filterTree, searchTermLower]
  );

  const updateNodeInTree = useCallback((updatedNode: ArboristData) => {
    setInternalTreeData(prev => {
      if (!prev.length) return prev;

      const updateNode = (nodes: ArboristData[]): ArboristData[] => {
        return nodes.map(node => {
          if (node.id === updatedNode.id) {
            return { ...node, ...updatedNode };
          }

          if (node.children?.length) {
            return {
              ...node,
              children: updateNode(node.children),
            };
          }

          return node;
        });
      };

      return updateNode(prev);
    });
  }, []);

  const handleComponentSelect = useCallback(
    (details: ComponentDetails | null, path: string[], nodeId: string) => {
      if (details && nodeId) {
        selectedNodeIdRef.current = nodeId;
        lastSelectedNodeRef.current = { details, path };
      }
      onComponentSelect(details, path);
    },
    [onComponentSelect]
  );

  const renderNode = useCallback(
    (props: NodeRendererProps<ArboristData>) => (
      <TreeNodeRenderer
        {...props}
        updateNodeInTree={updateNodeInTree}
        selectedNodeId={selectedNodeIdRef.current}
        onComponentSelect={(details, path) => handleComponentSelect(details, path, props.node.id)}
      />
    ),
    [updateNodeInTree, handleComponentSelect]
  );

  useEffect(() => {
    if (!searchTermLower && lastSelectedNodeRef.current) {
      const { details, path } = lastSelectedNodeRef.current;
      onComponentSelect(details, path);
    }
  }, [searchTermLower, onComponentSelect]);

  // Use calculated height from parent (TreeView component uses useTreeHeight hook)
  const treeHeight = calculatedHeight || 600; // Fallback if no height provided

  // Use the useTreeWidth hook for dynamic width calculation
  const treeWidth = useTreeWidth(scrollContainerRef);

  const findLastNodeThatCanLoadMore = useCallback(() => {
    if (!treeRef.current) {
      return null;
    }

    const visibleNodes = treeRef.current.visibleNodes || [];

    // Find all expanded directories that can load more
    const candidateNodes = [];

    for (let i = 0; i < visibleNodes.length; i++) {
      const node = visibleNodes[i];

      if (node.data.type !== 'Directory') {
        continue;
      }

      if (!node.isOpen) {
        continue;
      }

      const canLoadMore =
        node.data.hasMoreChildren === true && !node.data.isLoading && !node.data.isLoadingMore;

      if (canLoadMore) {
        // Find the last child of this directory in visible nodes
        let lastChildIndex = i;
        for (let j = i + 1; j < visibleNodes.length; j++) {
          const potentialChild = visibleNodes[j];
          // Check if this node is a child of our directory by comparing paths or depth
          if (
            potentialChild.data.path &&
            node.data.path &&
            potentialChild.data.path.length > node.data.path.length &&
            potentialChild.data.path.slice(0, node.data.path.length).join('/') ===
              node.data.path.join('/')
          ) {
            lastChildIndex = j;
          } else {
            break; // No longer a child of this directory
          }
        }

        candidateNodes.push({
          node: node.data,
          treeNode: node,
          lastChildIndex,
          directoryIndex: i,
        });
      }
    }

    // Return the directory whose last child is closest to the bottom of visible area
    if (candidateNodes.length === 0) {
      return null;
    }

    // Sort by lastChildIndex descending to get the directory with children closest to bottom
    candidateNodes.sort((a, b) => b.lastChildIndex - a.lastChildIndex);

    return candidateNodes[0];
  }, []);

  const loadMoreItems = useCallback(async () => {
    if (isLoadingRef.current || isLoadingMore) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[InfiniteScroll] Skipping load - already loading:', {
          isLoadingRef: isLoadingRef.current,
          isLoadingMore,
        });
      }
      return;
    }

    const now = Date.now();
    const timeSinceLastLoad = now - lastLoadTime.current;

    if (timeSinceLastLoad < LOAD_THROTTLE_MS) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[InfiniteScroll] Throttling load request:', {
          timeSinceLastLoad,
          LOAD_THROTTLE_MS,
        });
      }
      return;
    }

    const result = findLastNodeThatCanLoadMore();

    if (!result) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[InfiniteScroll] No node found that can load more');

        // Debug: Log current state of expanded directories
        const visibleNodes = treeRef.current?.visibleNodes || [];
        const expandedDirs = visibleNodes
          .filter((n: any) => n.data.type === 'Directory' && n.isOpen)
          .map((n: any) => ({
            id: n.data.id,
            name: n.data.name,
            hasMoreChildren: n.data.hasMoreChildren,
            isLoading: n.data.isLoading,
            isLoadingMore: n.data.isLoadingMore,
            currentPage: n.data.currentPage,
          }));
        console.log('[InfiniteScroll] Expanded directories:', expandedDirs);
      }
      isLoadingRef.current = false;
      return;
    }

    const { node: nodeToLoadMore } = result;

    // Track this directory
    expandedDirectoriesRef.current.add(nodeToLoadMore.id);

    if (process.env.NODE_ENV === 'development') {
      console.log('[InfiniteScroll] Loading more items for node:', {
        nodeId: nodeToLoadMore.id,
        nodeName: nodeToLoadMore.name,
        currentPage: nodeToLoadMore.currentPage,
        hasMoreChildren: nodeToLoadMore.hasMoreChildren,
      });
    }

    isLoadingRef.current = true;
    lastLoadTime.current = now;

    updateNodeInTree({
      ...nodeToLoadMore,
      isLoadingMore: true,
    });

    try {
      const currentPage = nodeToLoadMore.currentPage || 1;
      const nextPage = currentPage + 1;

      const response = await queryClient.fetchQuery({
        queryKey: ['directoryChildren', nodeToLoadMore.id, nextPage],
        queryFn: () => fetchTreeNodes(nodeToLoadMore.id, nextPage, 50),
      });

      const updatedChildren = [
        ...(nodeToLoadMore.children || []),
        ...response.nodes.map(node => ({
          ...node,
          path: [...nodeToLoadMore.path, node.name],
        })),
      ];

      if (process.env.NODE_ENV === 'development') {
        console.log('[InfiniteScroll] Successfully loaded more items:', {
          nodeId: nodeToLoadMore.id,
          newItemsCount: response.nodes.length,
          totalChildren: updatedChildren.length,
          hasMorePages: response.hasMorePages,
          nextPage,
        });
      }

      updateNodeInTree({
        ...nodeToLoadMore,
        children: updatedChildren,
        hasMoreChildren: response.hasMorePages,
        currentPage: nextPage,
        isLoadingMore: false,
      });
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('[InfiniteScroll] Error loading more items:', {
          nodeId: nodeToLoadMore.id,
          error: error instanceof Error ? error.message : error,
        });
      }

      updateNodeInTree({
        ...nodeToLoadMore,
        isLoadingMore: false,
      });
    } finally {
      isLoadingRef.current = false;
      setIsLoadingMore(false);
    }
  }, [findLastNodeThatCanLoadMore, isLoadingMore, queryClient, updateNodeInTree]);

  // Stable reference to loadMoreItems to avoid event listener re-attachment
  const loadMoreItemsRef = useRef(loadMoreItems);
  loadMoreItemsRef.current = loadMoreItems;

  useEffect(() => {
    const treeInstance = treeRef.current;

    if (
      treeInstance &&
      treeInstance.listEl &&
      treeInstance.listEl.current &&
      typeof treeInstance.listEl.current.addEventListener === 'function'
    ) {
      const treeScrollElement = treeInstance.listEl.current as HTMLElement;

      let ticking = false;

      const handleInnerScroll = () => {
        if (isLoadingRef.current || !treeScrollElement) {
          return;
        }

        const { scrollTop, scrollHeight, clientHeight } = treeScrollElement;
        const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

        if (distanceFromBottom < 300) {
          if (!ticking) {
            if (process.env.NODE_ENV === 'development') {
              console.log('[InfiniteScroll] Triggering load more items - scroll position:', {
                scrollTop,
                scrollHeight,
                clientHeight,
                distanceFromBottom,
              });
            }
            window.requestAnimationFrame(() => {
              // Use ref to get current function without causing re-renders
              loadMoreItemsRef.current();
              ticking = false;
            });
            ticking = true;
          }
        }
      };

      treeScrollElement.addEventListener('scroll', handleInnerScroll, { passive: true });

      return () => {
        if (treeScrollElement && typeof treeScrollElement.removeEventListener === 'function') {
          treeScrollElement.removeEventListener('scroll', handleInnerScroll);
        }
      };
    }
  }, []); // Empty dependency array - event listener only attached once

  const treeComponent = useMemo(() => {
    // Show tree skeleton when loading AND no data (initial load) OR when we have empty data
    const isLoading = treeContext?.loading || false;
    const hasNoData = !filteredTreeData || filteredTreeData.length === 0;
    const isInitialLoad = isLoading && hasNoData;

    // Show simple spinner during initial load (loading + no data)
    // This covers the case where data is [] but we're still loading
    if (isInitialLoad) {
      return (
        <div className="relative" ref={scrollContainerRef} style={{ height: `${treeHeight}px` }}>
          <div className="absolute inset-0 flex-center">
            <div className="text-center">
              <div className="spinner-primary spinner-lg mb-2" />
              <p className="text-border-color-dark text-sm">Loading tree data...</p>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="relative" ref={scrollContainerRef} style={{ height: `${treeHeight}px` }}>
        <Tree<ArboristData>
          ref={treeRef}
          data={filteredTreeData}
          width={treeWidth} // Dynamically calculated: 420px container - 28px reserved space = 392px max
          height={treeHeight}
          indent={15}
          rowHeight={48}
          overscanCount={20}
          disableDrag={true}
          disableDrop={true}
          className="tree-scroll-container"
        >
          {renderNode}
        </Tree>
        {isLoadingMore && (
          <div className="absolute bottom-0 left-0 right-0 flex-center py-2 bg-background">
            <div className="spinner-primary spinner-md" />
          </div>
        )}
      </div>
    );
  }, [filteredTreeData, treeHeight, treeWidth, renderNode, isLoadingMore]);

  return <div className="h-full">{treeComponent}</div>;
};

export default React.memo(ArboristTreeNode);
