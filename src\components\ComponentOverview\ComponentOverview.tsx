import React from 'react';
import { useTranslation } from 'react-i18next';
import CompareResult, {
  backgroundColorForCompareResult,
  comparatorForCompareR<PERSON>ult,
  doCombineResult,
  textColorOnIconForCompareResult,
} from '@/utils/compareResult';
import { DateNow, DateToString } from '@/utils/dateTime';
import {
  CompareResultIconBoxWithText,
  TextPosition,
} from '../shared/CompareResultIcon/CompareResultIcon';
import { CompareResults, ComponentDetails, Job } from '@/utils/types';
import ServerVersionSVG from '@/assets/svgs/light/server.svg';
import LocalVersionSVG from '@/assets/svgs/light/local.svg';
import { Features, isFeatureActive } from '@/utils/featureFlags';
import Breadcrumb from '@/components/Breadcrumb/Breadcrumb';
import { TreeContext } from '@/context/TreeContext';
import { useJobs } from '@/hooks/queries/useJobs';

interface BoxProps {
  children: React.ReactNode;
  className?: string;
}

interface LabeledValueProps {
  label: string;
  value: React.ReactNode;
}

interface VersionBoxProps {
  versionAvailable: boolean;
  versionNumber: number;
  time: Date;
  label: string;
  naLabel: string;
  className: string;
  image: string;
}

interface BackupsBoxProps {
  componentId: string;
}

interface CompareBoxProps {
  compareResult: CompareResult;
}

interface ComponentOverviewProps {
  details: ComponentDetails;
}

const Box: React.FC<BoxProps> = ({ children, className }) => (
  <div className={`p-4 ${className}`}>{children}</div>
);

const CompareBox: React.FC<CompareBoxProps> = ({ compareResult }) => (
  <div
    className={`compare-logo absolute -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2 w-18 h-18 shadow-md flex items-center justify-center ${backgroundColorForCompareResult(compareResult)}`}
  >
    <p className={`m-0 text-6xl ${textColorOnIconForCompareResult(compareResult)}`}>
      {comparatorForCompareResult(compareResult)}
    </p>
  </div>
);

const LabeledValue: React.FC<LabeledValueProps> = ({ label, value }) => (
  <p className="mb-2">
    <span className="uppercase font-bold text-small">{label}</span>
    <br />
    {value}
  </p>
);

const VersionBox: React.FC<VersionBoxProps> = ({
  versionAvailable,
  versionNumber,
  time,
  label,
  naLabel,
  className,
  image,
}) => (
  <Box className={className}>
    <img src={image} className="mb-4 w-10 h-10" alt="versionBoxImage" />
    <LabeledValue
      label={`${label} ${versionAvailable ? ': ' + versionNumber : ''}`}
      value={versionAvailable ? DateToString(time) : naLabel}
    />
  </Box>
);

const BackupsBox: React.FC<BackupsBoxProps> = ({ componentId }) => {
  const { t } = useTranslation();
  const { data: jobsData, isLoading, error } = useJobs(componentId);

  const areJobsAvailable = (): boolean => {
    if (isLoading || error) {
      //We dont know, so let latestCompareResults show "Unknown"
      return true;
    }
    const count = jobsData?.jobs?.length;
    return count != undefined && count > 0;
  };

  const combinedResults: CompareResults = {
    versionVsBackup: CompareResult.Unknown,
    backupVsBackup: CompareResult.Unknown,
  };
  if (!isLoading && !error && jobsData) {
    const latestResults = jobsData.jobs
      .map(job => job)
      .filter((job): job is Job => job.isDeactivated === false && job.latestResult !== null)
      .map(job => job.latestResult);

    latestResults.map((results: CompareResults) => {
      combinedResults.versionVsBackup = doCombineResult(
        combinedResults.versionVsBackup,
        results.versionVsBackup
      );
      combinedResults.backupVsBackup = doCombineResult(
        combinedResults.backupVsBackup,
        results.backupVsBackup
      );
    });
  }

  return (
    <span className="box-right">
      <LabeledValue
        label={t('jobs.versionVsBackup')}
        value={
          areJobsAvailable() ? (
            <CompareResultIconBoxWithText
              compareResult={combinedResults.versionVsBackup}
              size={4}
              textPosition={TextPosition.left}
            />
          ) : (
            <span>{t('componentOverview.noJobConfigured')}</span>
          )
        }
      />
      <LabeledValue
        label={t('jobs.backupVsBackup')}
        value={
          areJobsAvailable() ? (
            <CompareResultIconBoxWithText
              compareResult={combinedResults.backupVsBackup}
              size={4}
              textPosition={TextPosition.left}
            />
          ) : (
            <span>{t('componentOverview.noJobConfigured')}</span>
          )
        }
      />
    </span>
  );
};

export const ComponentOverview: React.FC<ComponentOverviewProps> = ({ details }) => {
  const { t } = useTranslation();
  const treeContext = React.useContext(TreeContext);

  if (!treeContext) {
    throw new Error('ComponentOverview must be used within a TreeProvider');
  }

  const { selectedPath } = treeContext;

  const localVersionAvailable = false;
  const localVersionNumber = 0;
  const localVersionTimestamp = DateNow();

  const sortedVersions = Array.isArray(details.versions)
    ? [...details.versions].sort((a, b) => b.version - a.version)
    : [];

  const serverVersionAvailable = !!sortedVersions[0];
  const serverVersionNumber = sortedVersions[0]?.version || 0;
  const serverVersionTimestamp = sortedVersions[0]?.timestamp || DateNow();

  const localVsServerCompareResult =
    localVersionAvailable && serverVersionAvailable
      ? serverVersionNumber === localVersionNumber
        ? CompareResult.Equal
        : CompareResult.NotEqual
      : CompareResult.Unknown;

  return (
    <>
      <div className="flex flex-col gap-4 py-4">
        <div className="relative">
          <div className="w-full grid grid-cols-4 gap-medium">
            <Box className="box-left">
              <LabeledValue
                label={t('componentOverview.checkInPossible')}
                value={localVersionAvailable ? t('common.yes') : t('common.no')}
              />
              <LabeledValue
                label={t('componentOverview.editStateTimeOfCheck')}
                value={DateToString(DateNow())}
              />
            </Box>

            <VersionBox
              label={t('componentOverview.localVersion')}
              naLabel={t('componentOverview.notLocallyAvailable')}
              versionAvailable={localVersionAvailable}
              versionNumber={localVersionNumber}
              time={localVersionTimestamp}
              className="box-left bg-primary-dark"
              image={LocalVersionSVG}
            />

            <VersionBox
              label={t('componentOverview.serverVersion')}
              naLabel={t('componentOverview.notAvailableOnServer')}
              versionAvailable={serverVersionAvailable}
              versionNumber={serverVersionNumber}
              time={serverVersionTimestamp}
              className="box-right bg-primary-dark"
              image={ServerVersionSVG}
            />

            <BackupsBox componentId={details.id} />
          </div>
          {isFeatureActive(Features.LocalVsServer) && (
            <CompareBox compareResult={localVsServerCompareResult} />
          )}
        </div>
      </div>
      <div className="py-4 border-t border-border-color">
        <Breadcrumb path={selectedPath} />
      </div>
    </>
  );
};
