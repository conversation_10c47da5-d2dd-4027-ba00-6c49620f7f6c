import { useState } from 'react';
import ArrowIcon from '@/components/shared/ArrowIcon';
import FieldLabel from './FieldLabel';

const maxLength = 500;
const isTextTooLarge = (value: string) => {
  return value.length >= 500;
};

// Reusable component for Large Texts with eliding and expand/collapse
interface LargeTextFieldProps {
  label: string;
  icon?: string;
  value: string;
}

const LargeTextField: React.FC<LargeTextFieldProps> = ({ label, icon, value }) => {
  const [expanded, setExpanded] = useState(false);

  const toggleExpand = () => {
    setExpanded(prevState => !prevState);
  };

  let displayText;
  if (!value || value.length <= 0) {
    displayText = '\u00a0';
  } else if (isTextTooLarge(value)) {
    if (expanded) displayText = value;
    else displayText = value.substring(0, maxLength) + '...';
  } else {
    displayText = value;
  }

  const showButton = isTextTooLarge(value) ? true : false;
  return (
    <div className="col-span-full">
      <span className="float-left mr-2">
        <FieldLabel label={label} icon={icon} />
      </span>
      {displayText}

      <span className="float-right">
        {showButton ? (
          <ArrowIcon
            onClick={toggleExpand}
            direction={expanded ? 'up' : 'down'}
            className="flex-shrink-0"
            size={16}
          />
        ) : (
          <></>
        )}
      </span>
    </div>
  );
};

export default LargeTextField;
