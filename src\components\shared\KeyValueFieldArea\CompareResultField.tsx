import { CompareResultAsTextCell } from '@/components/VersionDetails/JobList/LatestCompareResult/LatestCompareResult';
import { CompareResult } from '@/utils/compareResult';
import FieldLabel from './FieldLabel';

// Reusable component for Compare Result fields
interface CompareResultFieldProps {
  label: string;
  icon?: string;
  value: CompareResult;
}

const CompareResultField: React.FC<CompareResultFieldProps> = ({ label, icon, value }) => (
  <div className="flex gap-2">
    <FieldLabel label={label} icon={icon} />
    <div className="mb-1">
      <CompareResultAsTextCell compareResult={value} deactivated={false} />
    </div>
  </div>
);

export default CompareResultField;
