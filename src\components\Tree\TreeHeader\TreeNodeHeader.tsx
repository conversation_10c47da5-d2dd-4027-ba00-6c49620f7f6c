import React from 'react';
import ArrowIcon from '@/components/shared/ArrowIcon';
import openFolderIcon from '@/assets/svgs/dark/folder-open.svg';
import closedFolderIcon from '@/assets/svgs/dark/folder.svg';

import ComponentTypeIcon from '@/components/Tree/TreeNodeComponentIcon/ComponentTypeIcon';
import { TreeNodeType } from '@/utils/types';

interface TreeNodeHeaderProps {
  expanded: boolean;
  node: TreeNodeType;
  toggleExpand: (() => void) | null;
  isLoading?: boolean;
}

/**
 * TreeNodeHeader - Renders the header for an individual tree node
 * Displays the expand/collapse arrow, folder/component icon, and node name
 */
const TreeNodeHeader: React.FC<TreeNodeHeaderProps> = ({
  expanded,
  node,
  toggleExpand,
  isLoading = false,
}) => {
  const handleExpandClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (toggleExpand) {
      toggleExpand();
    }
  };

  return (
    <div
      className="flex items-center h-10 text-base p-small"
      style={{ cursor: toggleExpand ? 'pointer' : 'default' }}
    >
      <div className="flex items-center">
        {node.type !== 'Component' && (
          <>
            {isLoading ? (
              <div className="flex-shrink-0 spinner-primary h-3 w-3 mr-2" />
            ) : (
              <ArrowIcon
                direction={expanded ? 'down' : 'right'}
                className="flex-shrink-0 mr-2"
                onClick={handleExpandClick}
                size={12}
              />
            )}
            <img
              src={expanded ? openFolderIcon : closedFolderIcon}
              alt={expanded ? 'Open Folder' : 'Closed Folder'}
              className="flex-shrink-0 h-3.5 w-4 mr-2"
            />
          </>
        )}
        {node.type === 'Component' && (
          <>
            {isLoading ? (
              <div className="flex-shrink-0 spinner-primary h-3 w-3 mr-2" />
            ) : (
              <div className="flex-shrink-0 h-3 w-3 mr-2" /> // Empty div for spacing consistency
            )}
            {node.componentType?.icon && (
              <div className="flex-shrink-0 mr-2">
                <ComponentTypeIcon size={4} data={node.componentType.icon} />
              </div>
            )}
          </>
        )}
      </div>
      <span className="flex-shrink-0 overflow-hidden whitespace-nowrap text-ellipsis">
        {node.name}
      </span>
    </div>
  );
};

export default React.memo(TreeNodeHeader);
