import React, { useContext } from 'react';
import { TreeContext } from '@/context/TreeContext';
import logoIcon from '@/assets/svgs/light/Octoplant.svg';
import UserSettingsMenu from './UserSettingsMenu';

const Header: React.FC = () => {
  const treeContext = useContext(TreeContext);
  if (!treeContext) {
    throw new Error('Header must be used within a TreeProvider');
  }
  return (
    <header className="grid grid-cols-[18rem_1fr] h-[97px] min-h-[97px] header-gradient shadow-xl relative">
      <div className="relative">
        <img
          src={logoIcon}
          alt=""
          className="absolute top-[27px] left-[30px] w-[168px] h-[43px] opacity-100"
        />
      </div>
      <div className="flex-end gap-medium pr-8">
        <UserSettingsMenu />
      </div>
    </header>
  );
};

export default React.memo(Header);
