import React, { createContext, useCallback, useContext, useEffect, useRef, useState } from 'react';
import { User, UserManager } from 'oidc-client-ts';
import { settings as authSettings } from '../authSettings';
import { setAuthToken } from '../services/httpClient';

// Minimal, focused authentication context
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isTokenReady: boolean; // NEW: Indicates if token is set in HTTP client
  isLoading: boolean;
  error: string | null;
  signinRedirect: () => Promise<void>;
  signinRedirectCallback: () => Promise<User>;
  signinSilentCallback: () => Promise<User | void>;
  signoutRedirect: () => Promise<void>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Minimal state - only what's absolutely necessary
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isTokenReady, setIsTokenReady] = useState(false);

  // Single UserManager instance
  const userManagerRef = useRef<UserManager | null>(null);

  // Initialize UserManager once
  if (!userManagerRef.current) {
    userManagerRef.current = new UserManager(authSettings);
  }

  const userManager = userManagerRef.current;

  // Simple error handler
  const handleError = useCallback((error: unknown, source: string) => {
    const message = error instanceof Error ? error.message : String(error);
    console.error(`[Auth] ${source}:`, message);
    setError(message);
    setIsLoading(false);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Initialize authentication state once
  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      try {
        console.log('[Auth] Initializing...');
        const currentUser = await userManager.getUser();

        if (mounted) {
          // CRITICAL FIX: Set token IMMEDIATELY when setting user
          if (currentUser && currentUser.access_token && !currentUser.expired) {
            console.log('[Auth] Setting authentication token during initialization');
            setAuthToken(currentUser.access_token);
            setIsTokenReady(true);
          } else {
            console.log('[Auth] Clearing authentication token during initialization');
            setAuthToken('');
            setIsTokenReady(false);
          }

          setUser(currentUser);
          setIsLoading(false);
          console.log('[Auth] Initialized:', currentUser ? 'authenticated' : 'not authenticated');
        }
      } catch (err) {
        if (mounted) {
          handleError(err, 'initialization');
        }
      }
    };

    initializeAuth();

    return () => {
      mounted = false;
    };
  }, [userManager, handleError]);

  // Set up event handlers once
  useEffect(() => {
    const handleUserLoaded = (user: User) => {
      console.log('[Auth] User loaded via automatic renewal');

      // FIXED: Update HTTP client token when user is loaded via automatic renewal
      if (user && user.access_token && !user.expired) {
        console.log('[Auth] Setting token from automatic renewal');
        setAuthToken(user.access_token);
        setIsTokenReady(true);
      } else {
        console.log('[Auth] Clearing token - invalid user from automatic renewal');
        setAuthToken('');
        setIsTokenReady(false);
      }

      setUser(user);
      setError(null);
      setIsLoading(false);
    };

    const handleUserUnloaded = () => {
      console.log('[Auth] User unloaded');
      setAuthToken('');
      setIsTokenReady(false);
      setUser(null);
      setIsLoading(false);
    };

    const handleAccessTokenExpired = async () => {
      console.log('[Auth] Token expired - attempting silent refresh before clearing user');

      try {
        // FIXED: Attempt silent refresh before clearing user
        const refreshedUser = await userManager.signinSilent();

        if (refreshedUser && refreshedUser.access_token && !refreshedUser.expired) {
          console.log('[Auth] Silent refresh successful after token expiration');
          setAuthToken(refreshedUser.access_token);
          setIsTokenReady(true);
          setUser(refreshedUser);
          setError(null);
          setIsLoading(false);
        } else {
          console.log('[Auth] Silent refresh failed - clearing user');
          setAuthToken('');
          setIsTokenReady(false);
          setUser(null);
          setIsLoading(false);
        }
      } catch (err) {
        console.error('[Auth] Silent refresh failed after token expiration:', err);
        setAuthToken('');
        setIsTokenReady(false);
        setUser(null);
        setIsLoading(false);
      }
    };

    userManager.events.addUserLoaded(handleUserLoaded);
    userManager.events.addUserUnloaded(handleUserUnloaded);
    userManager.events.addAccessTokenExpired(handleAccessTokenExpired);

    return () => {
      userManager.events.removeUserLoaded(handleUserLoaded);
      userManager.events.removeUserUnloaded(handleUserUnloaded);
      userManager.events.removeAccessTokenExpired(handleAccessTokenExpired);
    };
  }, [userManager]);

  // Token is now set synchronously during initialization and callbacks
  // This useEffect is kept as a safety net for any edge cases
  useEffect(() => {
    if (user && user.access_token && !user.expired) {
      console.log('[Auth] Safety net: Setting authentication token in HTTP client');
      setAuthToken(user.access_token);
      setIsTokenReady(true);
    } else {
      console.log('[Auth] Safety net: Clearing authentication token from HTTP client');
      setAuthToken('');
      setIsTokenReady(false);
    }
  }, [user]);

  // Periodic token expiry monitoring - logs token status every 2 minutes
  useEffect(() => {
    const logTokenExpiry = () => {
      if (user && user.expires_at) {
        const now = Math.floor(Date.now() / 1000);
        const expiresAt = user.expires_at;
        const timeUntilExpiration = expiresAt - now;
        const expiryDate = new Date(expiresAt * 1000);

        if (timeUntilExpiration > 0) {
          const minutes = Math.floor(timeUntilExpiration / 60);
          const seconds = timeUntilExpiration % 60;
          console.log(
            `[Auth] Token Status: Expires in ${minutes}m ${seconds}s (${expiryDate.toLocaleString()})`
          );
        } else {
          console.warn(
            `[Auth] Token Status: EXPIRED ${Math.abs(timeUntilExpiration)}s ago (${expiryDate.toLocaleString()})`
          );
        }
      } else if (user) {
        console.log('[Auth] Token Status: User exists but no expiration time available');
      } else {
        console.log('[Auth] Token Status: No user/token present');
      }
    };

    // Log immediately
    logTokenExpiry();

    // Set up interval to log every 2 minutes (120000ms)
    const interval = setInterval(logTokenExpiry, 120000);

    return () => {
      clearInterval(interval);
    };
  }, [user]);

  // Authentication methods
  const signinRedirect = useCallback(async () => {
    try {
      console.log('[Auth] Starting signin redirect');
      setIsLoading(true);
      setError(null);
      await userManager.signinRedirect();
    } catch (err) {
      handleError(err, 'signin-redirect');
    }
  }, [userManager, handleError]);

  const signinRedirectCallback = useCallback(async (): Promise<User> => {
    try {
      console.log('[Auth] Processing callback');
      setIsLoading(true);
      setError(null);

      const user = await userManager.signinRedirectCallback();
      console.log('[Auth] Callback successful');

      // Set the token immediately in HTTP client
      if (user && user.access_token && !user.expired) {
        console.log('[Auth] Setting token from callback');
        setAuthToken(user.access_token);
        setIsTokenReady(true);
      } else {
        setAuthToken('');
        setIsTokenReady(false);
      }

      setUser(user);
      setIsLoading(false);
      return user;
    } catch (err) {
      handleError(err, 'callback');
      throw err;
    }
  }, [userManager, handleError]);

  const signinSilentCallback = useCallback(async (): Promise<User | void> => {
    try {
      console.log('[Auth] Processing silent callback');
      const user = await userManager.signinSilentCallback();

      // FIXED: Update context state with renewed user data
      // Note: signinSilentCallback can return void, so we need to check if user exists
      if (user !== undefined && user !== null) {
        const typedUser = user as User;
        if (typedUser.access_token && !typedUser.expired) {
          console.log('[Auth] Silent callback successful - updating context');
          setAuthToken(typedUser.access_token);
          setIsTokenReady(true);
          setUser(typedUser);
          setError(null);
          setIsLoading(false);
        }
      }

      return user;
    } catch (err) {
      handleError(err, 'silent-callback');
      throw err;
    }
  }, [userManager, handleError]);

  const signoutRedirect = useCallback(async () => {
    try {
      console.log('[Auth] Starting signout');
      setIsLoading(true);
      setError(null);

      // Clear the token immediately
      setAuthToken('');
      setIsTokenReady(false);

      // Use removeUser() instead of signoutRedirect() to avoid server endpoint dependency
      // This matches the old working implementation
      await userManager.removeUser();

      console.log('[Auth] Logout completed successfully');
      setIsLoading(false);
    } catch (err) {
      console.error('[Auth] Logout error:', err);
      // Even if there's an error, ensure we clear everything
      setAuthToken('');
      setIsTokenReady(false);
      setUser(null);
      setIsLoading(false);
      handleError(err, 'signout');
    }
  }, [userManager, handleError]);

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user && !user.expired,
    isTokenReady,
    isLoading,
    error,
    signinRedirect,
    signinRedirectCallback,
    signinSilentCallback,
    signoutRedirect,
    clearError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
