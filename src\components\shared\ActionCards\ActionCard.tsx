import React from 'react';

interface ActionCardProps {
  iconName: string;
  title: string;
  description: React.ReactNode;
  buttonText: string;
  buttonIconName: string;
  onButtonClick: () => void;
}

export const ActionCard = React.memo(
  ({
    iconName,
    title,
    description,
    buttonText,
    buttonIconName,
    onButtonClick,
  }: ActionCardProps) => (
    <article className="flex flex-col h-[400px] w-96 bg-primary text-background shadow-lg p-x-large">
      <header>
        <img
          src={`/src/assets/svgs/light/${iconName}.svg`}
          alt={`${title} icon`}
          className="h-16"
          loading="lazy"
        />
        <h3 className="mt-8 text-2xl font-semibold uppercase">{title}</h3>
      </header>
      <div className="flex flex-col justify-between flex-1">
        <p className="mb-8 mt-8 text-background leading-snug">{description}</p>
        <button
          className="h-16 py-2 px-4 text-primary bg-secondary hover:bg-hover text-large flex items-center justify-center gap-2 transition-colors uppercase"
          onClick={onButtonClick}
          aria-label={buttonText}
          type="button"
        >
          <img
            src={`/src/assets/svgs/dark/${buttonIconName}.svg`}
            alt=""
            className="w-6 h-[19px]"
            loading="lazy"
            width="24"
            height="19"
          />
          <span className={buttonText === 'Browse Help' ? 'text-primary' : ''}>{buttonText}</span>
        </button>
      </div>
    </article>
  )
);
