import React from 'react';

interface LoaderProps {
  size?: number;
  color?: string;
  speed?: number;
}

const Loader: React.FC<LoaderProps> = ({ size = 40, color = '#0B4A82', speed = 1 }) => {
  return (
    <div className="flex-center">
      <svg
        width={size}
        height={size}
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
        className="animate-spin"
        style={{ animationDuration: `${1 / speed}s` }}
      >
        <circle
          cx="12"
          cy="12"
          r="10"
          stroke={color}
          strokeWidth="4"
          fill="none"
          strokeDasharray="31.4 31.4"
          transform="rotate(-90 12 12)"
        />
      </svg>
    </div>
  );
};

export default Loader;
