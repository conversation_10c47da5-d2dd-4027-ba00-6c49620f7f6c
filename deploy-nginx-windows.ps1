# PowerShell script for nginx deployment on Windows
# Run this script as Administrator

param(
    [string]$NginxPath = "C:\nginx",
    [string]$WebPath = "C:\nginx\html\octoplant-web"
)

Write-Host "Starting nginx deployment for Octoplant Web Client on Windows..." -ForegroundColor Green

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires Administrator privileges. Please run PowerShell as Administrator." -ForegroundColor Red
    exit 1
}

# Step 1: Check if nginx is installed
if (-not (Test-Path "$NginxPath\nginx.exe")) {
    Write-Host "nginx not found at $NginxPath" -ForegroundColor Yellow
    Write-Host "Please install nginx first:" -ForegroundColor Yellow
    Write-Host "1. Download nginx from http://nginx.org/en/download.html" -ForegroundColor Yellow
    Write-Host "2. Extract to C:\nginx" -ForegroundColor Yellow
    Write-Host "3. Or install via Chocolatey: choco install nginx" -ForegroundColor Yellow
    exit 1
}

Write-Host "nginx found at $NginxPath" -ForegroundColor Green

# Step 2: Check if we're in the project directory
if (-not (Test-Path "package.json")) {
    Write-Host "package.json not found. Please run this script from the project root directory." -ForegroundColor Red
    exit 1
}

# Step 3: Build the application
Write-Host "Building the application..." -ForegroundColor Cyan
try {
    npm install
    npm run build
    
    if (-not (Test-Path "dist")) {
        Write-Host "Dist directory not found. Build may have failed." -ForegroundColor Red
        exit 1
    }
    
    Write-Host "Application built successfully" -ForegroundColor Green
} catch {
    Write-Host "Build failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 4: Stop nginx if running
Write-Host "Stopping nginx if running..." -ForegroundColor Cyan
try {
    $nginxProcess = Get-Process -Name "nginx" -ErrorAction SilentlyContinue
    if ($nginxProcess) {
        Stop-Process -Name "nginx" -Force
        Start-Sleep -Seconds 2
        Write-Host "nginx stopped" -ForegroundColor Green
    } else {
        Write-Host "nginx was not running" -ForegroundColor Green
    }
} catch {
    Write-Host "Could not stop nginx: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 5: Create web directory and deploy files
Write-Host "Creating web directory and deploying files..." -ForegroundColor Cyan
try {
    # Create directory if it doesn't exist
    if (-not (Test-Path $WebPath)) {
        New-Item -ItemType Directory -Path $WebPath -Force | Out-Null
    } else {
        # Clear existing files
        Remove-Item "$WebPath\*" -Recurse -Force -ErrorAction SilentlyContinue
    }
    
    # Copy build files
    Copy-Item -Path "dist\*" -Destination $WebPath -Recurse -Force
    
    Write-Host "Files deployed to $WebPath" -ForegroundColor Green
} catch {
    Write-Host "Failed to deploy files: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 6: Update nginx configuration
Write-Host "Updating nginx configuration..." -ForegroundColor Cyan
try {
    $configPath = "$NginxPath\conf\nginx.conf"
    $siteConfigPath = "$NginxPath\conf\octoplant-web.conf"
    
    # Copy our site configuration
    if (Test-Path "nginx-windows.conf") {
        Copy-Item "nginx-windows.conf" $siteConfigPath -Force
        
        # Update the root path in the configuration
        $configContent = Get-Content $siteConfigPath -Raw
        $configContent = $configContent -replace "C:/nginx/html/octoplant-web", $WebPath.Replace('\', '/')
        Set-Content $siteConfigPath $configContent
        
        Write-Host "Site configuration copied to $siteConfigPath" -ForegroundColor Green
    } else {
        Write-Host "nginx-windows.conf not found in current directory" -ForegroundColor Red
        exit 1
    }
    
    # Update main nginx.conf to include our site
    $mainConfig = Get-Content $configPath -Raw
    if ($mainConfig -notmatch "include.*octoplant-web\.conf") {
        # Add include directive in http block using [regex]::Replace
        $mainConfig = [regex]::Replace($mainConfig, '(http\s*\{)', '$1`n    include octoplant-web.conf;')
        Set-Content $configPath $mainConfig
        Write-Host "Main nginx configuration updated" -ForegroundColor Green
    } else {
        Write-Host "Main nginx configuration already includes our site" -ForegroundColor Green
    }
    
} catch {
    Write-Host "Failed to update nginx configuration: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 7: Test nginx configuration
Write-Host "Testing nginx configuration..." -ForegroundColor Cyan
try {
    $testResult = & "$NginxPath\nginx.exe" -t 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "nginx configuration is valid" -ForegroundColor Green
    } else {
        Write-Host "nginx configuration test failed:" -ForegroundColor Red
        Write-Host $testResult -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Failed to test nginx configuration: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 8: Start nginx
Write-Host "Starting nginx..." -ForegroundColor Cyan
try {
    Start-Process -FilePath "$NginxPath\nginx.exe" -WorkingDirectory $NginxPath -WindowStyle Hidden
    Start-Sleep -Seconds 3
    
    # Check if nginx is running
    $nginxProcess = Get-Process -Name "nginx" -ErrorAction SilentlyContinue
    if ($nginxProcess) {
        Write-Host "nginx started successfully" -ForegroundColor Green
    } else {
        Write-Host "nginx failed to start" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Failed to start nginx: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 9: Show status
Write-Host ""
Write-Host "Deployment completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Status:" -ForegroundColor Cyan
Write-Host "  - nginx path: $NginxPath" -ForegroundColor White
Write-Host "  - Web directory: $WebPath" -ForegroundColor White
Write-Host "  - Configuration: $NginxPath\conf\octoplant-web.conf" -ForegroundColor White
Write-Host "  - Application URL: http://localhost" -ForegroundColor White
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "  1. Open browser and go to http://localhost" -ForegroundColor White
Write-Host "  2. Update server_name in nginx config if using a domain" -ForegroundColor White
Write-Host "  3. Configure SSL certificate for HTTPS" -ForegroundColor White
Write-Host "  4. Update backend API URLs if they are different" -ForegroundColor White
Write-Host ""
Write-Host "Useful commands:" -ForegroundColor Cyan
Write-Host "  - Stop nginx: taskkill /f /im nginx.exe" -ForegroundColor White
Write-Host "  - Start nginx: cd $NginxPath && nginx.exe" -ForegroundColor White
Write-Host "  - Reload nginx: cd $NginxPath && nginx.exe -s reload" -ForegroundColor White
Write-Host "  - Test config: cd $NginxPath && nginx.exe -t" -ForegroundColor White
Write-Host "  - View logs: Get-Content $NginxPath\logs\error.log -Tail 20" -ForegroundColor White
