import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import svgr from 'vite-plugin-svgr';
import path from 'path';
import { readFileSync } from 'fs';

export default defineConfig(({ mode }) => {
  // Load env file based on mode
  const env = loadEnv(mode, process.cwd());
  const isProduction = mode === 'production';

  // Get version from package.json
  const packageJson = JSON.parse(readFileSync('./package.json', 'utf8'));
  const version = packageJson.version;

  console.log('Environment loaded:', {
    mode,
    version,
  });

  return {
    plugins: [react(), svgr({ svgrOptions: { icon: true } })],
    define: {
      'import.meta.env.VITE_APP_VERSION': JSON.stringify(version),
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@components': '@/components',
        '@assets': '@/assets',
        '@utils': '@/utils',
        '@styles': '@/styles',
        '@shared': '@/components/shared',
      },
    },
    server: {
      host: '0.0.0.0',
      port: env.PORT ?? 3000,
    },
    build: {
      outDir: 'build',
      sourcemap: !isProduction,
      minify: isProduction,
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
          },
        },
      },
    },
  };
});
