import { VersionDetailsCurrentPage } from '@/components/VersionDetails/types';
import { TreeContext } from '@/context/TreeContext';
import { useContext } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';

export interface TabButtonProps {
  page: VersionDetailsCurrentPage;
}

const TabButton: React.FC<TabButtonProps> = ({ page }) => {
  const treeContext = useContext(TreeContext);
  if (!treeContext) {
    throw new Error('TreeDetails must be used within a TreeProvider');
  }

  const { setCurrentDetailsPage, currentDetailsPage } = treeContext;
  const { t } = useTranslation();
  const active = currentDetailsPage === page;

  // Get translated text directly in the component
  const getTabText = (page: VersionDetailsCurrentPage): string => {
    switch (page) {
      case VersionDetailsCurrentPage.JobList:
        return t('details.jobs');
      case VersionDetailsCurrentPage.Details:
        return t('details.details');
      case VersionDetailsCurrentPage.ChangeHistory:
      default:
        return t('details.changeHistory');
    }
  };

  const text = getTabText(page);

  const borderDefault = ' border-b-4';
  let style = 'uppercase cursor-pointer m-0 border-0 px-0 text-large hover:bg-secondary-hover';
  style += active ? ' font-bold border-b-8 border-secondary' : borderDefault;

  return (
    <>
      <motion.div
        className={style}
        onClick={() => {
          setCurrentDetailsPage(page);
        }}
        layout
        transition={{ type: 'spring', stiffness: 400, damping: 30 }}
        animate={{ scale: active ? 0.95 : 1 }}
      >
        {text}
      </motion.div>
      <div className={`${borderDefault} w-16 text-large`}>&nbsp;</div>
    </>
  );
};

export default TabButton;
