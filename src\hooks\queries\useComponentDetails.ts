import { useQuery } from '@tanstack/react-query';
import { fetchComponentDetails } from '@/services/api/index';
import { ComponentDetails } from '@/utils/types';

interface UseComponentDetailsOptions {
  enabled?: boolean;
  fetchFullDetails?: boolean;
}

/**
 * Custom hook to fetch and cache component details
 * @param componentId - The ID of the component to fetch details for
 * @param options - Options for the query
 */
export const useComponentDetails = (
  componentId: string | null,
  options: UseComponentDetailsOptions | boolean = true
) => {
  // Handle the case where options is a boolean (for backward compatibility)
  const { enabled = typeof options === 'boolean' ? options : true, fetchFullDetails = true } =
    typeof options === 'object' ? options : {};
  // Create a unique query key that includes the fetchFullDetails option
  const queryKey = fetchFullDetails
    ? ['componentDetails', 'full', componentId]
    : ['componentDetails', 'basic', componentId];

  return useQuery<ComponentDetails, Error>({
    queryKey,
    queryFn: () => {
      if (!componentId) {
        throw new Error('No component ID provided to useComponentDetails hook');
      }
      // Validate the component ID parameter
      if (componentId.trim() === '') {
        throw new Error('Empty component ID provided to useComponentDetails hook');
      }
      return fetchComponentDetails(componentId);
    },
    enabled: !!componentId && enabled,
    // Only fetch if enabled and (fetchFullDetails is true or we're fetching basic details)
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};
