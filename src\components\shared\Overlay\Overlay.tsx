import React from 'react';
import CloseButton from '../CloseButton/CloseButton';

interface OverlayProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
}

const Overlay: React.FC<OverlayProps> = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;

  return (
    <div
      className={'fixed inset-0 flex items-center justify-center bg-semi-transparent'}
      onClick={onClose}
    >
      <div
        className={
          'p-large relative bg-background max-overlay-height max-overlay-width overflow-y-auto opacity-100 p-2 bg-background-color'
        }
        onClick={e => e.stopPropagation()}
      >
        <CloseButton onClick={onClose} />
        {title && <div className={'p-large head-line-2'}>{title}</div>}
        <div className={'pl-4'}>{children}</div>
      </div>
    </div>
  );
};

export default Overlay;
