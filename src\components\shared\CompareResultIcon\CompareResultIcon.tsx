import React from 'react';
import CompareResult, {
  backgroundColorForCompareResult,
  comparatorForCompareResult,
  displayTextForCompareResult,
  hasError,
  hasWarning,
  isEqual,
  isNotEqual,
  textColorOnIconForCompareResult,
} from '@/utils/compareResult';
import { ToolTip } from '../ToolTip/ToolTip';
import { t } from 'i18next';

const warningAnchorName = 'warning-icon-anchor';
const errorAnchorName = 'error-icon-anchor';
const equalAnchorName = 'equal-icon-anchor';
const notEqualAnchorName = 'not-equal-icon-anchor';
const unknownAnchorName = 'unknown-icon-anchor';

export interface CompareResultBoxProps {
  compareResult: CompareResult;
  size?: number;
}

export enum TextPosition {
  left,
  right,
}

export interface CompareResultIconBoxProps {
  compareResult: CompareResult;
  size?: number;
  textPosition?: TextPosition;
}

export const CompareResultIcon: React.FC<CompareResultBoxProps> = React.memo(
  ({ compareResult, size = 7 }) => {
    const sizes = (' w-' + size + ' h-' + size) as string;

    //Place anchors for all possible states. we can assign tooltips to those anchors or not
    let anchor = '';
    if (hasError(compareResult)) anchor = errorAnchorName;
    else if (hasWarning(compareResult)) anchor = warningAnchorName;
    else if (isEqual(compareResult)) anchor = equalAnchorName;
    else if (isNotEqual(compareResult)) anchor = notEqualAnchorName;
    else anchor = '';

    return (
      <span
        className={`flex shadow items-center justify-center font-normal ${sizes} ${textColorOnIconForCompareResult(compareResult)} ${backgroundColorForCompareResult(compareResult)} ${anchor}`}
      >
        {comparatorForCompareResult(compareResult)}
      </span>
    );
  }
);

export const CompareResultIconBoxWithText: React.FC<CompareResultIconBoxProps> = React.memo(
  ({ compareResult, size = 16, textPosition: leftOrRight = TextPosition.right }) => {
    const justify = leftOrRight === TextPosition.left ? 'justify-end' : 'justify-begin';
    const label = <span>{displayTextForCompareResult(compareResult)}</span>;
    return (
      <span className={'flex items-center gap-2 ' + justify}>
        {leftOrRight == TextPosition.left && label}
        <CompareResultIcon compareResult={compareResult} size={size} />
        {leftOrRight == TextPosition.right && label}
      </span>
    );
  }
);

export const CompareResultIconToolTips: React.FC = () => {
  return (
    <>
      <ToolTip
        anchor={warningAnchorName}
        title={t('jobs.resultWarningTitle')}
        text={t('jobs.resultErrorText')}
      />
      <ToolTip
        anchor={errorAnchorName}
        title={t('jobs.resultErrorTitle')}
        text={t('jobs.resultErrorText')}
      />
      <ToolTip
        anchor={unknownAnchorName}
        title={t('common.unknown')}
        text={t('jobs.noCompareResults')}
      />
    </>
  );
};
