import React from 'react';
import { BrowserRouter as Router, Route, Routes, Navigate, useLocation } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import i18n from './i18n';
import AppProviders from './providers/AppProviders';
import BaseLayout from '@/layouts/BaseLayout';
import ErrorBoundary from '@/components/Error/ErrorBoundary';
import ErrorFallback from '@/components/Error/ErrorFallback';
import useOnlineStatus from './hooks/useOnlineStatus';
import { routeConfig } from './routes/routeConfig';
import { useAuth } from './context/AuthContext';
import FixedLoadingScreen from '@/components/Auth/FixedLoadingScreen';
import { AnimatePresence, motion } from 'framer-motion';

// Styles
import 'react-toastify/dist/ReactToastify.css';
import './App.css';
import './index.scss';

// Simplified Protected Route Component - single source of route protection
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  // Show loading screen while authentication state is being determined
  // This prevents premature redirects during initialization
  if (isLoading) {
    return <FixedLoadingScreen show={true} message="Verifying authentication..." />;
  }

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    console.log('[ProtectedRoute] User not authenticated, redirecting to login');
    return <Navigate to="/login" replace />;
  }

  // User is authenticated, render the protected content
  return <>{children}</>;
};

// Main content wrapper with route transitions
const AppContent: React.FC = () => {
  useOnlineStatus();
  const location = useLocation();

  return (
    <AnimatePresence mode="wait" initial={false}>
      <motion.div
        key={location.pathname}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
        style={{ height: '100%' }}
      >
        <Routes location={location}>
          {routeConfig.map(route => {
            // Authentication routes don't need protection
            const isAuthRoute = [
              '/login',
              '/signin-callback',
              '/silent-callback',
              '/signout-callback',
            ].includes(route.path || '');

            if (isAuthRoute) {
              return <Route key={route.path} {...route} />;
            }

            // All other routes are protected
            return (
              <Route
                key={route.path}
                path={route.path}
                element={<ProtectedRoute>{route.element}</ProtectedRoute>}
              />
            );
          })}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </motion.div>
    </AnimatePresence>
  );
};

const App: React.FC = () => {
  return (
    <ErrorBoundary fallback={<ErrorFallback />}>
      <I18nextProvider i18n={i18n}>
        <Router>
          <AppProviders>
            <BaseLayout>
              <AppContent />
            </BaseLayout>
          </AppProviders>
        </Router>
      </I18nextProvider>
    </ErrorBoundary>
  );
};

export default App;
