import { Tooltip as ToolTipEx } from 'react-tooltip';

interface ToolTipProps {
  anchor: string;
  title?: string;
  text?: string;
}

export const ToolTip: React.FC<ToolTipProps> = props => {
  return (
    <ToolTipEx
      className="common-tooltip z-50"
      classNameArrow="invisible"
      anchorSelect={'.' + props.anchor}
      place="top"
    >
      <div className="bg-primary p-4 max-w-56 drop-shadow-lg">
        <div className="pb-2">{props.title && <b>{props.title}</b>}</div>
        {props.text && <div>{props.text}</div>}
      </div>
    </ToolTipEx>
  );
};
