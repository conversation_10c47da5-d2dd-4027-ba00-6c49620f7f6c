// useOnlineStatus.ts
import { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-toastify';

const useOnlineStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Use useCallback to ensure the event handlers are stable references
  const handleOnline = useCallback(() => {
    setIsOnline(true);
    toast.success('You are back online!');
  }, []);

  const handleOffline = useCallback(() => {
    setIsOnline(false);
    toast.error('You are offline. Some features may not work as expected.');
  }, []);

  useEffect(() => {
    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Clean up event listeners when component unmounts
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [handleOnline, handleOffline]); // Include the callbacks in the dependency array

  return isOnline;
};

export default useOnlineStatus;
