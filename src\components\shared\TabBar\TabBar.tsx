import TabButton, { TabButtonProps } from './TabButton';

interface TabBarProps {
  tabs: TabButtonProps[];
}

const TabBar: React.FC<TabBarProps> = ({ tabs }) => {
  return (
    <div className="flex-start mb-medium">
      {tabs.map(tab => {
        return <TabButton key={'tab-' + tab.page} page={tab.page} />;
      })}
      <div className="grow m-0 border-0 px-5 border-b-4 text-large">&nbsp;</div>
    </div>
  );
};

export default TabBar;
