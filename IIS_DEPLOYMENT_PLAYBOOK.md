# IIS Deployment Playbook for Octoplant Web Client

## Overview
This playbook provides step-by-step instructions for deploying the Octoplant Web Client React application to Microsoft IIS (Internet Information Services).

## Prerequisites
- Windows Server with IIS installed
- IIS URL Rewrite Module 2.1 or later
- IIS Application Request Routing (ARR) 3.0 or later (for API proxying)
- SSL certificate for HTTPS
- Node.js 18+ (for building the application)

## Current Application Architecture Analysis

### Build Configuration
- **Build Tool**: Vite with React plugin
- **Output Directory**: `dist/` (configurable in vite.config.js)
- **Build Command**: `npm run build`
- **Static Assets**: Bundled with chunking (vendor chunk for React/React-DOM)

### API Integration Points
Based on vite.config.js analysis:
- **OAuth Endpoints**: `/api/v1/oauth2/*` → Backend OAuth server
- **API Endpoints**: `/api/*` → API Gateway (default: localhost:5256)
- **File Storage**: `/api/Files/*` → Storage service (default: localhost:5157)
- **Direct OAuth**: `/oauth/*`, `/v1/oauth2/*` → OAuth server

### Environment Variables
- `VITE_OAUTH_URL`: OAuth server URL (default: https://[local-ip]:64023)
- `VITE_API_URL`: API Gateway URL (default: http://localhost:5256)
- `PORT`: Development server port (default: 3000)

## Step 1: Build the Application

### 1.1 Prepare Build Environment
```bash
# Clone the repository
git clone [repository-url]
cd web-client

# Install dependencies
npm install

# Set production environment variables
# Create .env.production file with:
VITE_OAUTH_URL=https://your-oauth-server.domain.com
VITE_API_URL=https://your-api-gateway.domain.com
```

### 1.2 Build for Production
```bash
# Clean previous builds
npm run clean

# Build the application
npm run build
```

This creates a `dist/` folder with all static assets ready for deployment.

## Step 2: IIS Site Configuration

### 2.1 Create IIS Application
1. Open IIS Manager
2. Right-click on "Default Web Site" or create a new site
3. Select "Add Application"
4. Set:
   - **Alias**: `octoplant-web` (or desired path)
   - **Physical Path**: Point to the `dist/` folder
   - **Application Pool**: Create new pool with "No Managed Code"

### 2.2 Configure Application Pool
1. Select the application pool
2. Set:
   - **.NET CLR Version**: No Managed Code
   - **Managed Pipeline Mode**: Integrated
   - **Process Model → Identity**: ApplicationPoolIdentity
   - **Enable 32-Bit Applications**: False

## Step 3: URL Rewrite Configuration

Create `web.config` in the `dist/` folder:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <!-- Enable compression -->
    <urlCompression doStaticCompression="true" doDynamicCompression="true" />
    
    <!-- Static file handling -->
    <staticContent>
      <mimeMap fileExtension=".json" mimeType="application/json" />
      <mimeMap fileExtension=".woff" mimeType="application/font-woff" />
      <mimeMap fileExtension=".woff2" mimeType="application/font-woff2" />
    </staticContent>
    
    <!-- URL Rewrite rules -->
    <rewrite>
      <rules>
        <!-- API Proxy Rules -->
        <rule name="OAuth Token Proxy" stopProcessing="true">
          <match url="^api/v1/oauth2/token(.*)" />
          <action type="Rewrite" url="https://your-oauth-server.domain.com/v1/oauth2/token{R:1}" />
        </rule>
        
        <rule name="OAuth UserInfo Proxy" stopProcessing="true">
          <match url="^api/v1/oauth2/userinfo(.*)" />
          <action type="Rewrite" url="https://your-oauth-server.domain.com/v1/oauth2/userinfo{R:1}" />
        </rule>
        
        <rule name="OAuth Proxy" stopProcessing="true">
          <match url="^api/v1/oauth2/(.*)" />
          <action type="Rewrite" url="https://your-oauth-server.domain.com/v1/oauth2/{R:1}" />
        </rule>
        
        <rule name="Files API Proxy" stopProcessing="true">
          <match url="^api/Files/(.*)" />
          <action type="Rewrite" url="https://your-storage-server.domain.com/api/Files/{R:1}" />
        </rule>
        
        <rule name="General API Proxy" stopProcessing="true">
          <match url="^api/(.*)" />
          <action type="Rewrite" url="https://your-api-gateway.domain.com/api/{R:1}" />
        </rule>
        
        <rule name="Direct OAuth Proxy" stopProcessing="true">
          <match url="^oauth/(.*)" />
          <action type="Rewrite" url="https://your-oauth-server.domain.com/{R:1}" />
        </rule>
        
        <rule name="Direct v1 OAuth Proxy" stopProcessing="true">
          <match url="^v1/oauth2/(.*)" />
          <action type="Rewrite" url="https://your-oauth-server.domain.com/v1/oauth2/{R:1}" />
        </rule>
        
        <!-- SPA Routing - Must be last -->
        <rule name="SPA Fallback" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
            <add input="{REQUEST_URI}" pattern="^/(api|oauth|v1)" negate="true" />
          </conditions>
          <action type="Rewrite" url="/index.html" />
        </rule>
      </rules>
    </rewrite>
    
    <!-- CORS Headers -->
    <httpProtocol>
      <customHeaders>
        <add name="Access-Control-Allow-Origin" value="*" />
        <add name="Access-Control-Allow-Methods" value="GET, POST, PUT, DELETE, OPTIONS" />
        <add name="Access-Control-Allow-Headers" value="Content-Type, Authorization, X-Requested-With" />
        <add name="Cross-Origin-Opener-Policy" value="unsafe-none" />
        <add name="Cross-Origin-Embedder-Policy" value="unsafe-none" />
      </customHeaders>
    </httpProtocol>
    
    <!-- Security headers -->
    <httpProtocol>
      <customHeaders>
        <add name="X-Content-Type-Options" value="nosniff" />
        <add name="X-Frame-Options" value="DENY" />
        <add name="X-XSS-Protection" value="1; mode=block" />
        <add name="Referrer-Policy" value="strict-origin-when-cross-origin" />
      </customHeaders>
    </httpProtocol>
    
    <!-- Caching rules -->
    <staticContent>
      <clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="31536000" />
    </staticContent>
    
    <!-- Handle OPTIONS requests -->
    <handlers>
      <add name="OptionsHandler" verb="OPTIONS" path="*" type="System.Web.DefaultHttpHandler" preCondition="integratedMode" />
    </handlers>
  </system.webServer>
</configuration>
```

## Step 4: SSL Configuration

### 4.1 Install SSL Certificate
1. Obtain SSL certificate for your domain
2. In IIS Manager, select your site
3. Click "Bindings" in Actions panel
4. Add HTTPS binding:
   - **Type**: https
   - **Port**: 443
   - **SSL Certificate**: Select your certificate

### 4.2 Force HTTPS Redirect
Add to web.config in `<rewrite><rules>` section (before other rules):

```xml
<rule name="Force HTTPS" stopProcessing="true">
  <match url="(.*)" />
  <conditions>
    <add input="{HTTPS}" pattern="off" ignoreCase="true" />
  </conditions>
  <action type="Redirect" url="https://{HTTP_HOST}/{R:1}" redirectType="Permanent" />
</rule>
```

## Step 5: Application Request Routing (ARR) Setup

### 5.1 Install ARR Module
1. Download and install IIS Application Request Routing 3.0
2. Restart IIS Manager

### 5.2 Configure Server Farm (Optional - for load balancing)
1. In IIS Manager, click on server name
2. Double-click "Application Request Routing Cache"
3. Click "Server Proxy Settings" in Actions panel
4. Check "Enable proxy"
5. Apply changes

## Step 6: Environment-Specific Configuration

### 6.1 Environment Variables Configuration
Since this is a client-side React application, environment variables are baked into the build at compile time. You need to set these before building:

#### 6.1.1 Development Environment (.env.development)
```bash
# Development environment variables
VITE_OAUTH_URL=https://dev-oauth.yourdomain.com/
VITE_API_URL=https://dev-api.yourdomain.com/api/
VITE_STORAGE_URL=https://dev-storage.yourdomain.com/
VITE_API_TIMEOUT=10000
VITE_EXPERIMENTAL=true
```

#### 6.1.2 Staging Environment (.env.staging)
```bash
# Staging environment variables
VITE_OAUTH_URL=https://staging-oauth.yourdomain.com/
VITE_API_URL=https://staging-api.yourdomain.com/api/
VITE_STORAGE_URL=https://staging-storage.yourdomain.com/
VITE_API_TIMEOUT=10000
VITE_EXPERIMENTAL=false
```

#### 6.1.3 Production Environment (.env.production)
```bash
# Production environment variables
VITE_OAUTH_URL=https://oauth.yourdomain.com/
VITE_API_URL=https://api.yourdomain.com/api/
VITE_STORAGE_URL=https://storage.yourdomain.com/
VITE_API_TIMEOUT=15000
VITE_EXPERIMENTAL=false
```

### 6.2 Build Process for Different Environments

#### 6.2.1 Development Build
```bash
# Copy environment file
cp .env.development .env

# Build for development
npm run build

# The dist/ folder now contains the development build
```

#### 6.2.2 Production Build
```bash
# Copy environment file
cp .env.production .env

# Build for production
npm run build

# The dist/ folder now contains the production build
```

### 6.3 IIS Configuration Updates for Each Environment

Update the web.config URL rewrite rules to match your environment's backend servers:

#### 6.3.1 Development web.config URLs
```xml
<!-- Replace these URLs in your development web.config -->
<!-- OAuth Token Proxy - matches vite.config.js '/api/v1/oauth2/token' -->
<rule name="OAuth Token Proxy" stopProcessing="true">
  <match url="^api/v1/oauth2/token(.*)" />
  <action type="Rewrite" url="https://dev-oauth.yourdomain.com/v1/oauth2/token{R:1}" />
</rule>

<!-- OAuth UserInfo Proxy - matches vite.config.js '/api/v1/oauth2/userinfo' -->
<rule name="OAuth UserInfo Proxy" stopProcessing="true">
  <match url="^api/v1/oauth2/userinfo(.*)" />
  <action type="Rewrite" url="https://dev-oauth.yourdomain.com/v1/oauth2/userinfo{R:1}" />
</rule>

<!-- Other OAuth endpoints - matches vite.config.js '/api/v1/oauth2' -->
<rule name="OAuth General Proxy" stopProcessing="true">
  <match url="^api/v1/oauth2/(.*)" />
  <action type="Rewrite" url="https://dev-oauth.yourdomain.com/v1/oauth2/{R:1}" />
</rule>

<!-- Files API Proxy - matches vite.config.js '/api/Files/' (must be before general API) -->
<rule name="Files API Proxy" stopProcessing="true">
  <match url="^api/Files/(.*)" />
  <action type="Rewrite" url="https://dev-storage.yourdomain.com/api/Files/{R:1}" />
</rule>

<!-- General API Proxy - matches vite.config.js '/api' -->
<rule name="General API Proxy" stopProcessing="true">
  <match url="^api/(.*)" />
  <action type="Rewrite" url="https://dev-api.yourdomain.com/api/{R:1}" />
</rule>
```

#### 6.3.2 Production web.config URLs
```xml
<!-- Replace these URLs in your production web.config -->
<!-- OAuth Token Proxy - matches vite.config.js '/api/v1/oauth2/token' -->
<rule name="OAuth Token Proxy" stopProcessing="true">
  <match url="^api/v1/oauth2/token(.*)" />
  <action type="Rewrite" url="https://oauth.yourdomain.com/v1/oauth2/token{R:1}" />
</rule>

<!-- OAuth UserInfo Proxy - matches vite.config.js '/api/v1/oauth2/userinfo' -->
<rule name="OAuth UserInfo Proxy" stopProcessing="true">
  <match url="^api/v1/oauth2/userinfo(.*)" />
  <action type="Rewrite" url="https://oauth.yourdomain.com/v1/oauth2/userinfo{R:1}" />
</rule>

<!-- Other OAuth endpoints - matches vite.config.js '/api/v1/oauth2' -->
<rule name="OAuth General Proxy" stopProcessing="true">
  <match url="^api/v1/oauth2/(.*)" />
  <action type="Rewrite" url="https://oauth.yourdomain.com/v1/oauth2/{R:1}" />
</rule>

<!-- Files API Proxy - matches vite.config.js '/api/Files/' (must be before general API) -->
<rule name="Files API Proxy" stopProcessing="true">
  <match url="^api/Files/(.*)" />
  <action type="Rewrite" url="https://storage.yourdomain.com/api/Files/{R:1}" />
</rule>

<!-- General API Proxy - matches vite.config.js '/api' -->
<rule name="General API Proxy" stopProcessing="true">
  <match url="^api/(.*)" />
  <action type="Rewrite" url="https://api.yourdomain.com/api/{R:1}" />
</rule>
```

## Step 7: PowerShell Deployment Automation (Optional)

### 7.1 Automated IIS Site Creation Script

Create a PowerShell script `Deploy-OctoplantWeb.ps1` for automated deployment:

```powershell
param(
    [Parameter(Mandatory=$true)]
    [string]$SiteName,
    
    [Parameter(Mandatory=$true)]
    [string]$PhysicalPath,
    
    [Parameter(Mandatory=$false)]
    [int]$Port = 443,
    
    [Parameter(Mandatory=$false)]
    [string]$CertificateThumbprint,
    
    [Parameter(Mandatory=$false)]
    [string]$Environment = "Production"
)

# Import WebAdministration module
Import-Module WebAdministration

Write-Host "Starting Octoplant Web Client deployment..." -ForegroundColor Green

# Create Application Pool
$AppPoolName = "$SiteName-AppPool"
if (Get-IISAppPool -Name $AppPoolName -ErrorAction SilentlyContinue) {
    Write-Host "Application pool $AppPoolName already exists, updating..." -ForegroundColor Yellow
    Remove-WebAppPool -Name $AppPoolName
}

New-WebAppPool -Name $AppPoolName
Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "managedRuntimeVersion" -Value ""
Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "enable32BitAppOnWin64" -Value $false
Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"

Write-Host "Application pool $AppPoolName created successfully" -ForegroundColor Green

# Create Website
if (Get-Website -Name $SiteName -ErrorAction SilentlyContinue) {
    Write-Host "Website $SiteName already exists, removing..." -ForegroundColor Yellow
    Remove-Website -Name $SiteName
}

New-Website -Name $SiteName -PhysicalPath $PhysicalPath -ApplicationPool $AppPoolName -Port 80

# Add HTTPS binding if certificate is provided
if ($CertificateThumbprint) {
    New-WebBinding -Name $SiteName -Protocol "https" -Port $Port
    $binding = Get-WebBinding -Name $SiteName -Protocol "https"
    $binding.AddSslCertificate($CertificateThumbprint, "my")
    Write-Host "HTTPS binding added with certificate $CertificateThumbprint" -ForegroundColor Green
}

Write-Host "Website $SiteName created successfully" -ForegroundColor Green

# Set folder permissions
$acl = Get-Acl $PhysicalPath
$accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "ReadAndExecute", "ContainerInherit,ObjectInherit", "None", "Allow")
$acl.SetAccessRule($accessRule)
Set-Acl -Path $PhysicalPath -AclObject $acl

Write-Host "Folder permissions set for IIS_IUSRS" -ForegroundColor Green

Write-Host "Deployment completed successfully!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Copy your built application files to: $PhysicalPath" -ForegroundColor Yellow
Write-Host "2. Update web.config with your environment-specific URLs" -ForegroundColor Yellow
Write-Host "3. Test the application" -ForegroundColor Yellow
```

### 7.2 Build and Deploy Script

Create `Build-And-Deploy.ps1` for complete build and deployment:

```powershell
param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("Development", "Staging", "Production")]
    [string]$Environment,
    
    [Parameter(Mandatory=$true)]
    [string]$DeploymentPath,
    
    [Parameter(Mandatory=$false)]
    [string]$BackupPath = "C:\Deployments\Backups"
)

$ErrorActionPreference = "Stop"

Write-Host "Starting build and deployment for $Environment environment..." -ForegroundColor Green

# Step 1: Backup existing deployment
if (Test-Path $DeploymentPath) {
    $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
    $backupFolder = Join-Path $BackupPath "$Environment-$timestamp"
    
    Write-Host "Creating backup at $backupFolder..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $backupFolder -Force
    Copy-Item -Path "$DeploymentPath\*" -Destination $backupFolder -Recurse -Force
    Write-Host "Backup completed" -ForegroundColor Green
}

# Step 2: Set environment variables
$envFile = ".env.$($Environment.ToLower())"
if (Test-Path $envFile) {
    Copy-Item $envFile ".env" -Force
    Write-Host "Environment file $envFile copied to .env" -ForegroundColor Green
} else {
    Write-Warning "Environment file $envFile not found. Using default .env"
}

# Step 3: Install dependencies and build
Write-Host "Installing dependencies..." -ForegroundColor Yellow
npm ci --production=false

Write-Host "Building application..." -ForegroundColor Yellow
npm run build

# Step 4: Deploy files
Write-Host "Deploying files to $DeploymentPath..." -ForegroundColor Yellow
if (Test-Path $DeploymentPath) {
    Remove-Item "$DeploymentPath\*" -Recurse -Force
} else {
    New-Item -ItemType Directory -Path $DeploymentPath -Force
}

Copy-Item -Path "dist\*" -Destination $DeploymentPath -Recurse -Force

# Step 5: Update web.config with environment-specific URLs
$webConfigPath = Join-Path $DeploymentPath "web.config"
if (Test-Path $webConfigPath) {
    Write-Host "Updating web.config for $Environment environment..." -ForegroundColor Yellow
    
    # Read environment-specific configuration
    $configFile = "config\$Environment.json"
    if (Test-Path $configFile) {
        $config = Get-Content $configFile | ConvertFrom-Json
        
        # Update web.config with actual URLs
        $webConfig = Get-Content $webConfigPath -Raw
        $webConfig = $webConfig -replace "https://your-oauth-server\.domain\.com", $config.OAuthUrl
        $webConfig = $webConfig -replace "https://your-api-gateway\.domain\.com", $config.ApiUrl
        $webConfig = $webConfig -replace "https://your-storage-server\.domain\.com", $config.StorageUrl
        
        Set-Content -Path $webConfigPath -Value $webConfig
        Write-Host "web.config updated with $Environment URLs" -ForegroundColor Green
    }
}

Write-Host "Deployment completed successfully!" -ForegroundColor Green
```

### 7.3 Environment Configuration Files

Create configuration files for each environment in a `config/` directory:

#### config/development.json
```json
{
  "OAuthUrl": "https://dev-oauth.yourdomain.com",
  "ApiUrl": "https://dev-api.yourdomain.com",
  "StorageUrl": "https://dev-storage.yourdomain.com"
}
```

#### config/staging.json
```json
{
  "OAuthUrl": "https://staging-oauth.yourdomain.com",
  "ApiUrl": "https://staging-api.yourdomain.com",
  "StorageUrl": "https://staging-storage.yourdomain.com"
}
```

#### config/production.json
```json
{
  "OAuthUrl": "https://oauth.yourdomain.com",
  "ApiUrl": "https://api.yourdomain.com",
  "StorageUrl": "https://storage.yourdomain.com"
}
```

## Step 8: Testing and Validation

### 8.1 Pre-Deployment Testing Checklist

#### 8.1.1 Build Verification
```bash
# Verify build completes without errors
npm run build

# Check that dist/ folder contains expected files
ls -la dist/
# Should contain: index.html, assets/, favicon.ico, etc.

# Verify environment variables are baked into build
grep -r "VITE_" dist/ || echo "Environment variables properly replaced"
```

#### 8.1.2 Configuration Validation
- [ ] Environment-specific .env file is correctly configured
- [ ] web.config contains correct backend server URLs
- [ ] SSL certificate is valid and properly configured
- [ ] IIS Application Pool is set to "No Managed Code"
- [ ] URL Rewrite Module is installed and enabled
- [ ] Application Request Routing (ARR) is installed and enabled

### 8.2 Post-Deployment Testing

#### 8.2.1 Basic Functionality Test
1. **Application Loading**
   - Navigate to `https://your-domain.com/octoplant-web`
   - Verify the application loads without errors
   - Check browser console for JavaScript errors
   - Verify all static assets (CSS, JS, fonts) load correctly

2. **Routing Test**
   - Test navigation between different routes
   - Verify direct URL access works (e.g., `https://your-domain.com/octoplant-web/some-route`)
   - Confirm browser refresh works on any route (SPA routing)

3. **Static Asset Verification**
   - Check that CSS styles are applied correctly
   - Verify fonts are loading properly
   - Confirm images and icons display correctly

#### 8.2.2 API Integration Test
1. **OAuth Authentication Flow**
   ```bash
   # Monitor IIS logs during login
   tail -f C:\inetpub\logs\LogFiles\W3SVC1\*.log
   ```
   - Test login process
   - Verify OAuth redirects work correctly
   - Check that tokens are properly exchanged
   - Confirm user information is retrieved

2. **API Proxy Verification**
   - Test API calls are properly proxied to backend
   - Verify CORS headers are correctly set
   - Check that authentication headers are passed through
   - Monitor for 404 errors on API endpoints

3. **File Operations Test**
   - Test file upload functionality (if applicable)
   - Verify file download works correctly
   - Check storage API proxy is functioning

#### 8.2.3 Performance Test
1. **Load Time Analysis**
   - Use browser dev tools to check load times
   - Verify static asset caching is working (304 responses)
   - Check for unnecessary network requests
   - Confirm gzip compression is active

2. **Concurrent User Testing**
   - Test with multiple concurrent users
   - Monitor server performance during load
   - Check for memory leaks or performance degradation

### 8.3 Verification Against Codebase

#### 8.3.1 Proxy Configuration Verification
The IIS web.config URL rewrite rules have been verified against the application's proxy configuration:

**✅ Vite Development Proxy (vite.config.js lines 74-108)**:
- `/api/v1/oauth2/token` → OAuth server `/v1/oauth2/token` ✓
- `/api/v1/oauth2/userinfo` → OAuth server `/v1/oauth2/userinfo` ✓  
- `/api/v1/oauth2` → OAuth server `/v1/oauth2` ✓
- `/api/Files/` → Storage server (localhost:5157) ✓
- `/api` → API Gateway server ✓

**✅ OAuth Configuration (authSettings.ts lines 24-40)**:
- Uses proxy routes for token and userinfo endpoints ✓
- Direct OAuth server for authorization endpoint ✓
- Client credentials configured (client_id: 'octoplant-client') ✓

**✅ CORS Configuration (server.js lines 55-60)**:
- Sets Access-Control-Allow-Origin: '*' ✓
- Configures Cross-Origin policies ✓
- Handles insecure API proxy for CORS ✓

#### 8.3.2 Environment Variables Verification
All environment variables from `.env-example` are properly documented:

**✅ Required Variables**:
- `VITE_API_URL` (default: http://localhost:5256/api/) ✓
- `VITE_OAUTH_URL` (default: https://**************:64023/) ✓
- `VITE_STORAGE_URL` (default: http://localhost:5157/) ✓
- `VITE_API_TIMEOUT` (default: 10000) ✓
- `VITE_EXPERIMENTAL` (default: true) ✓

### 8.4 Troubleshooting Common Issues

#### 8.4.1 Application Won't Load
**Symptoms**: Blank page or loading errors
**Solutions**:
- Check IIS logs for errors
- Verify web.config syntax is correct
- Ensure Application Pool is running
- Check file permissions on deployment folder
- Verify all static assets are properly deployed to wwwroot

#### 8.4.2 API Calls Failing
**Symptoms**: Network errors in browser console
**Solutions**:
- Verify ARR proxy configuration is enabled
- Check backend server URLs in web.config match your environment
- Confirm CORS headers are properly set in web.config
- Test backend servers are accessible from IIS server
- Ensure proxy rules are in correct order (Files API before general API)

#### 8.4.3 OAuth Login Issues
**Symptoms**: Authentication redirects fail or loop
**Solutions**:
- Verify OAuth server URL configuration matches VITE_OAUTH_URL
- Check SSL certificate validity on OAuth server
- Ensure proper CORS configuration for OAuth endpoints
- Confirm client_id ('octoplant-client') and redirect_uri match OAuth server configuration
- Verify token and userinfo endpoints use proxy routes as configured in authSettings.ts

#### 8.4.4 Static Assets Not Loading
**Symptoms**: CSS/JS files return 404 or MIME type errors
**Solutions**:
- Verify MIME types in web.config include .js, .css, .woff, .woff2, .svg
- Check file permissions on static assets
- Ensure proper caching headers are set
- Confirm URL rewrite rules don't interfere with static assets
- Verify Vite build output is correctly deployed to IIS wwwroot

#### 8.4.5 Environment-Specific Issues
**Symptoms**: Application works locally but fails in IIS
**Solutions**:
- Verify environment variables were properly set during build process
- Check that .env file was correctly configured before npm run build
- Ensure web.config URLs match the environment's actual backend servers
- Confirm SSL certificates are valid for all backend services
- Test each proxy endpoint individually using browser dev tools

### 7.1 Basic Functionality Test
1. Navigate to `https://your-domain.com/octoplant-web`
2. Verify the application loads
3. Check browser console for errors
4. Test navigation between routes

### 7.2 API Integration Test
1. Test OAuth login flow
2. Verify API calls are properly proxied
3. Check file upload/download functionality
4. Monitor IIS logs for proxy errors

### 7.3 Performance Test
1. Use browser dev tools to check load times
2. Verify static asset caching
3. Test with multiple concurrent users

## Step 8: Advanced IIS Configuration

### 8.1 Additional MIME Types
Add these MIME types to web.config for modern web assets:

```xml
<staticContent>
  <!-- Standard web assets -->
  <mimeMap fileExtension=".json" mimeType="application/json" />
  <mimeMap fileExtension=".woff" mimeType="application/font-woff" />
  <mimeMap fileExtension=".woff2" mimeType="application/font-woff2" />
  <mimeMap fileExtension=".ttf" mimeType="application/font-sfnt" />
  <mimeMap fileExtension=".eot" mimeType="application/vnd.ms-fontobject" />
  <mimeMap fileExtension=".otf" mimeType="application/font-sfnt" />
  
  <!-- Modern JavaScript modules -->
  <mimeMap fileExtension=".mjs" mimeType="application/javascript" />
  <mimeMap fileExtension=".js" mimeType="application/javascript" />
  
  <!-- Source maps for debugging -->
  <mimeMap fileExtension=".map" mimeType="application/json" />
  
  <!-- Web app manifest -->
  <mimeMap fileExtension=".webmanifest" mimeType="application/manifest+json" />
  
  <!-- SVG images -->
  <mimeMap fileExtension=".svg" mimeType="image/svg+xml" />
</staticContent>
```

### 8.2 Detailed ARR Configuration for API Proxying

#### 8.2.1 Enable Server Proxy
1. Open IIS Manager
2. Click on server name (root level)
3. Double-click "Application Request Routing Cache"
4. Click "Server Proxy Settings" in Actions panel
5. Check "Enable proxy"
6. Set these recommended settings:
   - **Response buffer limit (KB)**: 0 (unlimited)
   - **Response buffer threshold (KB)**: 64
   - **Maximum response header size (KB)**: 64
   - **Time-out (seconds)**: 300
   - **Connection time-out (seconds)**: 120

#### 8.2.2 Configure Proxy Preserve Host Header
In the Server Proxy Settings:
- Check "Preserve client IP in the following header": X-Forwarded-For
- Check "Preserve original host header in back-end request"

#### 8.2.3 Advanced Proxy Rules for OAuth
Add these advanced proxy configurations to web.config:

```xml
<rewrite>
  <rules>
    <!-- Enhanced OAuth Token Proxy with error handling -->
    <rule name="OAuth Token Proxy Enhanced" stopProcessing="true">
      <match url="^api/v1/oauth2/token(.*)" />
      <conditions>
        <add input="{REQUEST_METHOD}" pattern="POST" />
      </conditions>
      <action type="Rewrite" url="https://your-oauth-server.domain.com/v1/oauth2/token{R:1}" />
      <serverVariables>
        <set name="HTTP_X_FORWARDED_PROTO" value="https" />
        <set name="HTTP_X_FORWARDED_HOST" value="{HTTP_HOST}" />
      </serverVariables>
    </rule>
    
    <!-- OAuth Authorization with state preservation -->
    <rule name="OAuth Authorize Proxy" stopProcessing="true">
      <match url="^api/v1/oauth2/authorize(.*)" />
      <action type="Rewrite" url="https://your-oauth-server.domain.com/v1/oauth2/authorize{R:1}" />
      <serverVariables>
        <set name="HTTP_X_FORWARDED_PROTO" value="https" />
        <set name="HTTP_X_FORWARDED_HOST" value="{HTTP_HOST}" />
      </serverVariables>
    </rule>
  </rules>
</rewrite>
```

### 8.3 Performance Optimization

#### 8.3.1 Output Caching
Add output caching rules to web.config:

```xml
<system.webServer>
  <caching>
    <profiles>
      <add extension=".js" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="365.00:00:00" />
      <add extension=".css" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="365.00:00:00" />
      <add extension=".png" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="365.00:00:00" />
      <add extension=".jpg" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="365.00:00:00" />
      <add extension=".gif" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="365.00:00:00" />
      <add extension=".svg" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="365.00:00:00" />
      <add extension=".woff" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="365.00:00:00" />
      <add extension=".woff2" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" duration="365.00:00:00" />
    </profiles>
  </caching>
</system.webServer>
```

#### 8.3.2 Compression Configuration
Enhanced compression settings:

```xml
<system.webServer>
  <httpCompression directory="%SystemDrive%\inetpub\temp\IIS Temporary Compressed Files">
    <scheme name="gzip" dll="%Windir%\system32\inetsrv\gzip.dll" />
    <dynamicTypes>
      <add mimeType="text/*" enabled="true" />
      <add mimeType="message/*" enabled="true" />
      <add mimeType="application/javascript" enabled="true" />
      <add mimeType="application/json" enabled="true" />
      <add mimeType="application/xml" enabled="true" />
      <add mimeType="*/*" enabled="false" />
    </dynamicTypes>
    <staticTypes>
      <add mimeType="text/*" enabled="true" />
      <add mimeType="message/*" enabled="true" />
      <add mimeType="application/javascript" enabled="true" />
      <add mimeType="application/json" enabled="true" />
      <add mimeType="application/xml" enabled="true" />
      <add mimeType="*/*" enabled="false" />
    </staticTypes>
  </httpCompression>
  <urlCompression doStaticCompression="true" doDynamicCompression="true" />
</system.webServer>
```

## Step 9: Monitoring and Maintenance

### 9.1 IIS Logging Configuration
Enable detailed logging in IIS:
1. Select your site in IIS Manager
2. Double-click "Logging"
3. Set log file format to "W3C Extended Log File Format"
4. Click "Select Fields" and ensure these are checked:
   - Date, Time
   - Client IP (c-ip)
   - User Name (cs-username)
   - Method (cs-method)
   - URI Stem (cs-uri-stem)
   - URI Query (cs-uri-query)
   - Protocol Status (sc-status)
   - Protocol Substatus (sc-substatus)
   - Bytes Sent (sc-bytes)
   - Bytes Received (cs-bytes)
   - Time Taken (time-taken)
   - User Agent (cs(User-Agent))
   - Referrer (cs(Referer))

### 9.2 Failed Request Tracing
1. Enable Failed Request Tracing for debugging:
   - Right-click site → "Configure Failed Request Tracing"
   - Enable tracing
   - Set maximum number of trace files: 50
2. Add tracing rules:
   - All content (*), Status codes: 400-999
   - All content (*), Time taken > 30 seconds
3. Monitor trace logs at: `%SystemDrive%\inetpub\logs\FailedReqLogFiles`

### 9.3 Performance Monitoring
Set up performance counters to monitor:
- **Web Service Cache\File Cache Hits %**: Should be > 80%
- **ASP.NET Applications\Requests/Sec**: Monitor request throughput
- **Process(w3wp)\% Processor Time**: Should be < 80%
- **Process(w3wp)\Working Set**: Monitor memory usage

### 9.4 Health Checks
Create a simple health check endpoint by adding to web.config:

```xml
<rewrite>
  <rules>
    <rule name="Health Check" stopProcessing="true">
      <match url="^health$" />
      <action type="Rewrite" url="/index.html" />
    </rule>
  </rules>
</rewrite>
```

### 9.5 Regular Maintenance Tasks
- **Weekly**: Review IIS logs for errors and performance issues
- **Monthly**: Check disk space for log files and clean up old logs
- **Quarterly**: Update SSL certificates before expiration
- **As needed**: Keep IIS and modules updated with security patches
- **As needed**: Review and update URL rewrite rules based on application changes

## Troubleshooting

### Common Issues

#### 1. 404 Errors on Refresh
**Symptom**: Direct URL access or page refresh returns 404
**Solution**: Verify SPA Fallback rule in web.config is correctly configured

#### 2. API Calls Failing
**Symptom**: Network errors in browser console
**Solution**: 
- Check ARR proxy configuration
- Verify backend server URLs in web.config
- Check CORS headers

#### 3. OAuth Login Issues
**Symptom**: Authentication redirects fail
**Solution**:
- Verify OAuth server URL configuration
- Check SSL certificate validity
- Ensure proper CORS configuration

#### 4. Static Assets Not Loading
**Symptom**: CSS/JS files return 404
**Solution**:
- Verify MIME types in web.config
- Check file permissions
- Ensure proper caching headers

### Log Analysis
Monitor these IIS log fields:
- `sc-status`: HTTP status codes
- `sc-substatus`: Detailed error codes
- `time-taken`: Response times
- `cs-uri-stem`: Requested URLs

## Security Considerations

1. **Content Security Policy**: Configure CSP headers based on application requirements
2. **Authentication**: Ensure OAuth integration is properly secured
3. **HTTPS Only**: Force HTTPS for all communications
4. **Input Validation**: Validate all user inputs on backend
5. **Regular Updates**: Keep IIS and all modules updated

## Performance Optimization

1. **Compression**: Enable static and dynamic compression
2. **Caching**: Configure appropriate cache headers for static assets
3. **CDN**: Consider using CDN for static assets
4. **Minification**: Ensure build process includes minification
5. **Bundle Splitting**: Leverage Vite's code splitting features

---

**Note**: Replace placeholder URLs (your-oauth-server.domain.com, etc.) with actual server addresses in your environment.
