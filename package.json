{"name": "my-appx", "version": "0.1.0", "private": true, "engines": {"node": ">=18.0.0"}, "dependencies": {"@szhsin/react-menu": "^4.1.0", "@tanstack/react-query": "^5.66.11", "@tanstack/react-query-devtools": "^5.66.11", "@tanstack/react-table": "^8.20.5", "axios": "^1.6.7", "framer-motion": "^12.10.4", "i18next": "^23.7.20", "i18next-browser-languagedetector": "^7.2.0", "i18next-http-backend": "^2.4.2", "lodash": "^4.17.21", "nginx": "^1.1.0", "oidc-client-ts": "^3.0.1", "react": "18.3.1", "react-arborist": "3.2.0", "react-dom": "18.3.1", "react-i18next": "^14.0.5", "react-icons": "^5.0.1", "react-router-dom": "^6.21.3", "react-toastify": "^10.0.4", "react-tooltip": "^5.28.0", "react-world-flags": "^1.6.0", "use-resize-observer": "^9.1.0"}, "devDependencies": {"@babel/eslint-parser": "^7.25.9", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.11", "@types/lodash": "^4.14.202", "@types/node": "^20.17.51", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/react-world-flags": "^1.4.5", "@typescript-eslint/eslint-plugin": "^8.24.1", "@typescript-eslint/parser": "^8.24.1", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.20", "eslint": "^8.56.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "express": "^4.18.2", "husky": "^9.0.6", "lint-staged": "^15.2.0", "postcss": "^8.4.47", "prettier": "^3.5.1", "rimraf": "^5.0.5", "sass": "^1.70.0", "tailwindcss": "^3.4.14", "typescript": "^5.3.3", "vite": "^5.4.19", "vite-plugin-compression": "^0.5.1", "vite-plugin-pwa": "^0.17.4", "vite-plugin-svgr": "^4.2.0", "web-vitals": "^3.5.1"}, "scripts": {"start": "vite", "dev": "vite", "build": "vite build", "preview": "vite preview --port 3000", "prod": "npm run clean && vite build --emptyOutDir && vite preview --port 3000", "proxy": "node server.js", "lint": "eslint src --ext .js,.jsx,.ts,.tsx --cache --fix --max-warnings 500", "lint:check": "eslint src --ext .js,.jsx,.ts,.tsx --cache --max-warnings 500", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,scss}\"", "clean": "<PERSON><PERSON><PERSON> dist build", "audit": "npm audit", "audit:fix": "npm audit fix", "validate": "npm run lint && npm run audit && npm outdated", "dev:debug": "vite --debug", "prepare": "husky install", "version:patch": "npm version patch --no-git-tag-version", "version:minor": "npm version minor --no-git-tag-version", "version:major": "npm version major --no-git-tag-version", "version:check": "echo $npm_package_version", "release:prepare": "npm run clean && npm run lint:check && npm run format:check && npm run build", "release:patch": "node scripts/release.js patch", "release:minor": "node scripts/release.js minor", "release:major": "node scripts/release.js major"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{css,scss,json}": ["prettier --write"]}, "type": "module", "eslintConfig": {"extends": ["eslint:recommended", "plugin:react/recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}