import { LockState } from '@/utils/types';
import LockIcon from '@/assets/svgs/light/lock.svg';
import UnlockIcon from '@/assets/svgs/light/lock-open.svg';
import UnderDevelopmentIcon from '@/assets/svgs/light/wrench.svg';
import { t } from 'i18next';

export const displayTextForLockState = (state: LockState): string => {
  const displayTextForLockStateMap: Record<LockState, string> = {
    [LockState.locked]: t('lockState.locked'),
    [LockState.unlocked]: t('lockState.unlocked'),
    [LockState.underDevelopment]: t('lockState.underDevelopment'),
  };

  return displayTextForLockStateMap[state] || t('lockState.locked');
};

export const displayTextForSetLockState = (state: LockState): string => {
  const displayTextForSetLockStateMap: Record<LockState, string> = {
    [LockState.locked]: t('lockState.setLocked'),
    [LockState.unlocked]: t('lockState.setUnlocked'),
    [LockState.underDevelopment]: t('lockState.setUnderDevelopment'),
  };
  return displayTextForSetLockStateMap[state] || t('lockState.setLocked');
};

export const iconForLockState = (state: LockState): string => {
  const iconMap: Record<LockState, string> = {
    [LockState.locked]: LockIcon,
    [LockState.unlocked]: UnlockIcon,
    [LockState.underDevelopment]: UnderDevelopmentIcon,
  };

  return iconMap[state] || LockIcon;
};

export const colorForLockState = (state: LockState): string => {
  const colorMap: Record<LockState, string> = {
    [LockState.locked]: 'locked',
    [LockState.unlocked]: 'unlocked',
    [LockState.underDevelopment]: 'under-development',
  };

  return colorMap[state] || 'locked';
};
