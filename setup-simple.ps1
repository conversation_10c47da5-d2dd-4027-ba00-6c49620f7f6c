# Simple environment setup script
Write-Host "Setting up environment..." -ForegroundColor Green

# Create environment file
$envContent = @"
VITE_OAUTH_URL=http://localhost:64023
VITE_API_URL=http://localhost:5256
VITE_STORAGE_URL=http://localhost:5157
VITE_API_TIMEOUT=15000
VITE_EXPERIMENTAL=false
"@

Set-Content -Path ".env.production" -Value $envContent -Encoding UTF8
Write-Host "Environment file created: .env.production" -ForegroundColor Green
Write-Host "Setup completed!" -ForegroundColor Green 