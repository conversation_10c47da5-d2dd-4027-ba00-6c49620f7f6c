$fontUrls = @(
    # Regular (400)
    @{ Url = "https://fonts.gstatic.com/s/titilliumweb/v15/NaPecZTIAOhVxoMyOr9n_E7fdMPmDaZRbrw.woff2"; File = "titillium-web-400-normal.woff2" },
    @{ Url = "https://fonts.gstatic.com/s/titilliumweb/v15/NaPecZTIAOhVxoMyOr9n_E7fdMPmDQ.woff"; File = "titillium-web-400-normal.woff" },
    
    # Semi-Bold (600)
    @{ Url = "https://fonts.gstatic.com/s/titilliumweb/v15/NaPDcZTIAOhVxoMyOr9n_E7ffBzCGIVzY5abuWIGxA.woff2"; File = "titillium-web-600-normal.woff2" },
    @{ Url = "https://fonts.gstatic.com/s/titilliumweb/v15/NaPDcZTIAOhVxoMyOr9n_E7ffBzCGItzYw.woff"; File = "titillium-web-600-normal.woff" },
    
    # Bold (700)
    @{ Url = "https://fonts.gstatic.com/s/titilliumweb/v15/NaPDcZTIAOhVxoMyOr9n_E7ffHjDGIVzY5abuWIGxA.woff2"; File = "titillium-web-700-normal.woff2" },
    @{ Url = "https://fonts.gstatic.com/s/titilliumweb/v15/NaPDcZTIAOhVxoMyOr9n_E7ffHjDGItzYw.woff"; File = "titillium-web-700-normal.woff" }
)

# Create font directory if it doesn't exist
$fontDir = ".\src\assets\fonts\titillium-web"
if (-not (Test-Path $fontDir)) {
    New-Item -ItemType Directory -Force -Path $fontDir
}

# Create WebClient for downloading
$webClient = New-Object System.Net.WebClient

foreach ($font in $fontUrls) {
    $outputPath = Join-Path $fontDir $font.File
    Write-Host "Downloading $($font.Url) to $outputPath"
    
    try {
        $webClient.DownloadFile($font.Url, $outputPath)
        Write-Host "Download successful!"
    } catch {
        Write-Host "Error downloading $($font.Url): $_"
    }
}

$webClient.Dispose()
Write-Host "Font download completed. Please check the font files in $fontDir"
