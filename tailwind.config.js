/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{js,jsx,ts,tsx}'],
  screens: {
    xs: { max: '480px' },
    sm: { max: '768px' },
    md: { max: '1024px' },
  },
  theme: {
    extend: {
      spacing: {
        small: 'var(--spacing-small)',
        medium: 'var(--spacing-medium)',
        large: 'var(--spacing-large)',
        'x-large': 'var(--spacing-x-large)',
        'xx-large': 'var(--spacing-xx-large)',
        'xxx-large': 'var(--spacing-xxx-large)',
      },
      gridTemplateColumns: {
        header: '1fr 2fr 0fr',
      },
      colors: {
        primary: 'var(--theme-primary-blue)',
        'primary-light': 'var(--theme-primary-light-blue)',
        'primary-dark': 'var(--theme-primary-dark-blue)',
        secondary: 'var(--theme-secondary-yellow)',

        'inverted-light': 'var(--white)',
        'inverted-dark': 'var(--light-gray)',

        hover: 'var(--theme-emphasized-background)',
        'secondary-hover': 'var(--theme-secondary-background)',

        background: 'var(--theme-primary-background)',
        'secondary-background': 'var(--theme-secondary-background)',
        'selected-background': 'var(--theme-selected-background)',

        'border-color': 'var(--light-gray)',
        'border-color-dark': 'var(--dark-gray)',

        error: 'var(--red)',
        warning: 'var(--yellow)',
        information: 'var(--white)',

        equal: 'var(--green)',
        'not-equal': 'var(--blue)',
        unknown: ' var(--white)',

        'dropdown-button': 'var(--dropdown-button-color)',
        'semi-transparent': 'var(--semi-transparent)',

        locked: 'var(--red)',
        unlocked: 'var(--green)',
        'under-development': 'var(--yellow)',
      },
      fontSize: {
        tiny: 'var(--font-tiny)',
        small: 'var(--font-small)',
        medium: 'var(--font-medium)',
        large: 'var(--font-large)',
        xlarge: 'var(--font-xlarge)',
        xxlarge: 'var(--font-xxlarge)',
        xxxlarge: 'var(--font-xxxlarge)',
      },
      padding: {
        button: 'var(--button-padding)',
      },
      fontFamily: {
        titillium: ['Titillium Web', 'sans-serif'],
      },
    },
  },
  plugins: [],
};
