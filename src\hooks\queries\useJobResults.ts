import { useQuery } from '@tanstack/react-query';
import { fetchJobResults } from '@/services/api/index';
import { JobResult } from '@/utils/types';

interface UseJobResultOptions {
  pageNumber?: number;
  pageSize?: number;
  enabled?: boolean;
  queryKeyPrefix?: string;
}

/**
 * Custom hook to fetch and cache jobs for a component
 * @param jobId - The ID of the component to fetch jobs for
 * @param options - Query Options
 */

export const useJobResults = (jobId: string | null, options: UseJobResultOptions = {}) => {
  const {
    pageNumber = 1,
    pageSize = 50,
    enabled: customEnabled = true,
    queryKeyPrefix = 'jobResults',
  } = options;

  // Use a type alias for better readability
  type JobResultsResponse = { results: JobResult[]; totalRecords: number };

  return useQuery<JobResultsResponse, Error>({
    queryKey: [queryKeyPrefix, jobId, pageNumber, pageSize],
    queryFn: async () => {
      if (!jobId?.trim()) {
        throw new Error('Invalid or empty component ID provided to use<PERSON><PERSON><PERSON> hook');
      }
      return fetchJobResults(jobId, { pageNumber, pageSize });
    },
    retry: (failureCount, error) => {
      // Only retry network errors, not validation errors
      if (error instanceof Error && error.message.includes('Invalid')) {
        return false;
      }
      return failureCount < 3;
    },
    enabled: !!jobId && customEnabled,
    staleTime: 5 * 60 * 1000, // 5 minutes (like in useJobs)
    gcTime: 10 * 60 * 1000, // 10 minutes - renamed from cacheTime in React Query v5
    refetchOnWindowFocus: false,
  });
};
