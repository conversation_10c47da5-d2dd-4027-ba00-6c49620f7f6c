import React from 'react';
import HLine from '@/components/shared/HLine/HLine';

import ArrowRight from '@/assets/svgs/light/angle-right.svg';
import ArrowLeft from '@/assets/svgs/light/angle-left.svg';

const buttonStyle = 'w-8 h-8 hover:bg-hover';
enum LeftOrRight {
  left,
  right,
}

// No-operation function to use instead of empty functions
const noop = () => {};

interface ArrowButtonProps {
  leftOrRight: LeftOrRight;
  disabled: boolean;
  clickHandler: () => void;
}

const ArrowButton = React.memo<ArrowButtonProps>(props => {
  const background = props.disabled ? 'bg-background' : 'bg-primary';
  const foreground = props.disabled ? 'text-primary' : 'text-background';

  return (
    <button
      className={`${buttonStyle} ${background} ${foreground} p-2.5`}
      disabled={props.disabled}
      onClick={props.clickHandler}
      aria-label={props.leftOrRight === LeftOrRight.left ? 'Previous page' : 'Next page'}
    >
      {props.leftOrRight === LeftOrRight.left ? (
        <img src={ArrowLeft} alt="Previous page" />
      ) : (
        <img src={ArrowRight} alt="Next page" />
      )}
    </button>
  );
});

interface PagingButtonProps {
  text: string;
  isActiveButton?: boolean;
  disabled: boolean;
  clickHandler: () => void;
}

const PagingButton = React.memo<PagingButtonProps>(props => {
  const disabled = props.disabled === true || props.isActiveButton === true;

  const background = props.isActiveButton ? 'bg-background' : 'bg-secondary-background';
  const foreground = 'text-primary';
  const border = props.isActiveButton ? 'border border-primary' : '';

  return (
    <button
      className={`${buttonStyle} ${background} ${foreground} ${border}`}
      disabled={disabled}
      onClick={props.clickHandler}
      aria-label={`Page ${props.text}${props.isActiveButton ? ' (current)' : ''}`}
      aria-current={props.isActiveButton ? 'page' : undefined}
    >
      {props.text}
    </button>
  );
});

interface NumberButtonsProps {
  pageNumber: number;
  isActiveButton?: boolean;
  clickHandler: (page: number) => void;
}

const NumberButton = React.memo<NumberButtonsProps>(props => {
  // Simplified click handler
  const handleClick = () => {
    if (!props.isActiveButton) {
      props.clickHandler(props.pageNumber);
    }
  };

  return (
    <PagingButton
      isActiveButton={props.isActiveButton}
      disabled={props.isActiveButton === true}
      text={props.pageNumber.toString()}
      clickHandler={handleClick}
    />
  );
});

interface PagingButtonsProps {
  currentPage: number;
  pages: number;
  buttons: number;
  clickHandler: (page: number) => void;
}

const Spacer: React.FC = () => {
  return (
    <span className="inline-block text-center w-8 h-8 text-primary" aria-hidden="true">
      ...
    </span>
  );
};

export const PagingButtons: React.FC<PagingButtonsProps> = props => {
  const numButtons = props.buttons % 2 ? props.buttons : props.buttons + 1;
  const halfTail = Math.floor(numButtons / 2);

  const isFirstPage = props.currentPage <= 1;
  const isLastPage = props.currentPage >= props.pages;

  const firstButtonInButtons = Math.max(props.currentPage - halfTail, 1);
  const lastButtonInButtons = Math.min(props.currentPage + halfTail, props.pages);

  const showFirstButton = firstButtonInButtons > 1 && props.pages > numButtons - halfTail;
  const showFirstEllipsis = props.currentPage > numButtons && props.pages > numButtons;

  const showLastButton = lastButtonInButtons < props.pages && props.pages > numButtons - halfTail;
  const showLastEllipsis =
    props.currentPage <= props.pages - numButtons && props.pages > numButtons;

  const back = () => {
    if (!isFirstPage) {
      props.clickHandler(props.currentPage - 1);
    }
  };
  const forward = () => {
    if (!isLastPage) {
      props.clickHandler(props.currentPage + 1);
    }
  };

  const numberButtons = () => {
    const content = [];
    for (let i = firstButtonInButtons; i <= lastButtonInButtons; i++) {
      content.push(
        <NumberButton
          key={`pagingButton-${i}`}
          pageNumber={i}
          isActiveButton={props.currentPage === i}
          clickHandler={props.clickHandler}
        />
      );
    }
    return content;
  };

  // Only show pagination if there's more than one page
  const shouldShowPagination = props.pages > 1;

  return (
    shouldShowPagination && (
      <>
        <HLine />
        <nav aria-label="Pagination navigation" className="mt-4">
          <div className="flex gap-2 m-4">
            <ArrowButton
              key="pagingButton-left-arrow"
              disabled={isFirstPage}
              leftOrRight={LeftOrRight.left}
              clickHandler={back}
            />
            {showFirstButton && (
              <>
                <NumberButton
                  key="pagingButton-first"
                  pageNumber={1}
                  clickHandler={props.clickHandler}
                />
                {showFirstEllipsis && <Spacer />}
              </>
            )}
            {numberButtons()}
            {showLastButton && (
              <>
                {showLastEllipsis && <Spacer />}
                <NumberButton
                  key="pagingButton-last"
                  pageNumber={props.pages}
                  clickHandler={props.clickHandler}
                />
              </>
            )}
            <ArrowButton
              key="pagingButton-right-arrow"
              disabled={isLastPage}
              leftOrRight={LeftOrRight.right}
              clickHandler={forward}
            />
          </div>
        </nav>
      </>
    )
  );
};
