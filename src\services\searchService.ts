import { searchComponentsTree, fetchVersionDetails } from '@/services/api/index';
import { TreeNodeType, ComponentDetails } from '@/utils/types';
import { VersionDetailsCurrentPage } from '@/components/VersionDetails/types';
import { ROUTES } from '@/constants/routes';
import { NavigateFunction } from 'react-router-dom';

/**
 * Service for handling component and directory search operations
 */
export class SearchService {
  /**
   * Search for components and directories
   * @param searchTerm - The search term to use
   * @returns Promise<TreeNodeType[]> - The search results
   */
  static async searchComponents(searchTerm: string): Promise<TreeNodeType[]> {
    if (!searchTerm || searchTerm.length < 2) {
      return [];
    }

    try {
      return await searchComponentsTree(searchTerm);
    } catch (error) {
      console.error('Error searching components:', error);
      return [];
    }
  }

  /**
   * Handle component selection from search results
   * @param result - The selected component
   * @param treeContext - The tree context
   * @param navigate - The navigation function
   */
  static handleComponentSelection(
    result: TreeNodeType,
    treeContext: any,
    navigate: NavigateFunction
  ): void {
    const { setSelectedNode, handleComponentSelect, setCurrentDetailsPage } = treeContext;

    // Update the context immediately with the selected node
    if (setSelectedNode) {
      setSelectedNode(result);
    }

    // Use the path or create one
    const path = result.path || [result.id];

    // Fetch versions and update context
    fetchVersionDetails(result.id)
      .then(versions => {
        // Create component details
        const componentDetails: ComponentDetails = {
          name: result.name,
          id: result.id,
          componentType: result.componentType,
          versions: versions.versions,
          jobs: [],
          localVersion: null,
          backupJobConfigured: false,
          masterData: [],
        };

        // Use handleComponentSelect which will update both selectedDetails and selectedPath
        // in a single context update, preventing multiple re-renders
        if (handleComponentSelect) {
          // Use requestAnimationFrame to defer the update until after navigation
          requestAnimationFrame(() => {
            handleComponentSelect(componentDetails, path);
          });
        }

        // Set current page to ChangeHistory
        if (setCurrentDetailsPage) {
          setCurrentDetailsPage(VersionDetailsCurrentPage.ChangeHistory);
        }
      })
      .catch(error => {
        console.error('Error fetching versions:', error);
      });

    // Navigate to tree view
    navigate(ROUTES.TREE);
  }

  /**
   * Handle directory selection from search results
   * @param result - The selected directory
   * @param treeContext - The tree context
   * @param navigate - The navigation function
   */
  static handleDirectorySelection(
    result: TreeNodeType,
    treeContext: any,
    navigate: NavigateFunction
  ): void {
    const { setSelectedNode, handleDirectorySelect } = treeContext;

    // Update the context with the selected node
    if (setSelectedNode) {
      setSelectedNode(result);
    }

    // Use the path or create one
    const path = result.path || [result.id];

    // Use handleDirectorySelect which handles all necessary state updates in one go
    if (handleDirectorySelect) {
      // Use requestAnimationFrame to defer the update until after navigation
      requestAnimationFrame(() => {
        handleDirectorySelect(result, path);
      });
    }

    // Navigate to tree view
    navigate(ROUTES.TREE);
  }
}
