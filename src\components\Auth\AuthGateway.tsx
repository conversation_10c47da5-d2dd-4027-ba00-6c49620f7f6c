import React, { useEffect, useState, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useTranslation } from 'react-i18next';
import { ROUTES } from '@/constants/routes';
import FixedLoadingScreen from './FixedLoadingScreen';
import { AuthError } from './AuthError';

// Simple, deterministic authentication gateway
const AuthGateway: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const {
    isAuthenticated,
    isLoading,
    error,
    signinRedirect,
    signinRedirectCallback,
    signinSilentCallback,
    clearError,
  } = useAuth();

  // Single state to track callback processing
  const [callbackProcessed, setCallbackProcessed] = useState(false);
  const processingRef = useRef(false);

  // Deterministic status computation
  const getStatus = () => {
    if (error) return 'error';
    if (isLoading) return 'loading';
    if (isAuthenticated) return 'authenticated';
    return 'unauthenticated';
  };

  const status = getStatus();

  // Single effect to handle all routing logic
  useEffect(() => {
    const currentPath = location.pathname;

    console.log('[AuthGateway] Route processing:', {
      path: currentPath,
      status,
      isAuthenticated,
      isLoading,
      callbackProcessed,
      processing: processingRef.current,
    });

    // Prevent multiple simultaneous processing
    if (processingRef.current) {
      console.log('[AuthGateway] Already processing, skipping');
      return;
    }

    const handleRoute = async () => {
      processingRef.current = true;

      try {
        switch (currentPath) {
          case ROUTES.SIGNIN_CALLBACK:
            await handleCallback();
            break;

          case ROUTES.SILENT_CALLBACK:
            await handleSilentCallback();
            break;

          case ROUTES.LOGIN:
            handleLogin();
            break;

          default:
            handleDefault();
            break;
        }
      } catch (err) {
        console.error('[AuthGateway] Route handling error:', err);
      } finally {
        processingRef.current = false;
      }
    };

    const handleCallback = async () => {
      if (callbackProcessed) {
        console.log('[AuthGateway] Callback already processed');
        navigateToTree();
        return;
      }

      try {
        console.log('[AuthGateway] Processing OAuth callback');
        await signinRedirectCallback();
        setCallbackProcessed(true);

        // Navigate after a brief delay to ensure state is stable
        setTimeout(() => {
          navigateToTree();
        }, 100);
      } catch (err) {
        console.error('[AuthGateway] Callback error:', err);
        // On callback error, redirect to login
        setTimeout(() => {
          navigate(ROUTES.LOGIN, { replace: true });
        }, 1000);
      }
    };

    const handleSilentCallback = async () => {
      try {
        console.log('[AuthGateway] Processing silent callback');
        await signinSilentCallback();
        window.close();
      } catch (err) {
        console.error('[AuthGateway] Silent callback error:', err);
        window.close();
      }
    };

    const handleLogin = () => {
      if (isAuthenticated) {
        console.log('[AuthGateway] Already authenticated, navigating to tree');
        navigateToTree();
      } else if (!isLoading) {
        console.log('[AuthGateway] Starting OAuth redirect');
        signinRedirect();
      }
    };

    const handleDefault = () => {
      if (isAuthenticated) {
        console.log('[AuthGateway] Authenticated user on default route, navigating to tree');
        navigateToTree();
      } else if (!isLoading) {
        console.log('[AuthGateway] Unauthenticated user, redirecting to login');
        navigate(ROUTES.LOGIN, { replace: true });
      }
    };

    const navigateToTree = () => {
      console.log('[AuthGateway] Navigating to tree');
      navigate(ROUTES.TREE, { replace: true });
    };

    handleRoute();
  }, [
    location.pathname,
    status,
    isAuthenticated,
    isLoading,
    callbackProcessed,
    navigate,
    signinRedirect,
    signinRedirectCallback,
    signinSilentCallback,
  ]);

  // Error retry handler
  const handleRetry = () => {
    clearError();
    setCallbackProcessed(false);
    navigate(ROUTES.LOGIN, { replace: true });
  };

  // Render based on status
  if (status === 'error') {
    return (
      <>
        <FixedLoadingScreen show={false} />
        <div className="flex items-center justify-center min-h-screen bg-gray-100">
          <div className="max-w-md w-full p-4">
            <AuthError
              customError={new Error(error || 'Authentication error')}
              onRetry={handleRetry}
            />
          </div>
        </div>
      </>
    );
  }

  // Show loading for all other states
  const getMessage = () => {
    switch (status) {
      case 'loading':
        if (location.pathname === ROUTES.SIGNIN_CALLBACK) {
          return t('auth.signingIn', 'Signing in...');
        }
        if (location.pathname === ROUTES.SILENT_CALLBACK) {
          return t('auth.refreshingSession', 'Refreshing session...');
        }
        return t('auth.initializingLogin', 'Initializing...');
      case 'authenticated':
        return t('auth.loadingApplication', 'Loading application...');
      case 'unauthenticated':
        return t('auth.redirectingToLogin', 'Redirecting to login...');
      default:
        return t('common.pleaseWait', 'Please wait...');
    }
  };

  return <FixedLoadingScreen show={true} message={getMessage()} />;
};

export default AuthGateway;
