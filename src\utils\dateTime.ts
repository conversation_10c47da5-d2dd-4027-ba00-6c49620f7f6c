import { time } from 'framer-motion';

export const DateNow = (): Date => new Date(Date.now());
export const SortComparatorASC = (a: Date, b: Date): number => a.getTime() - b.getTime();
export const SortComparatorDESC = (a: Date, b: Date): number => b.getTime() - a.getTime();

/**
 * Convert a timestamp string to a Date object
 * @param timestamp - Timestamp string or Date object
 * @returns Date object or undefined if input is invalid
 */
const parseDate = (timestamp?: string | Date | null): Date | undefined => {
  if (!timestamp) return undefined;

  try {
    const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
    return isNaN(date.getTime()) ? undefined : date;
  } catch (error) {
    console.error('Error parsing date:', error);
    return undefined;
  }
};

/**
 * Format options for date formatting
 */
export enum DateFormatOptions {
  DEFAULT = 'default',
  SHORT = 'short',
  LONG = 'long',
  TIME = 'time',
  DATETIME = 'datetime',
  WEEKDAY_TIME = 'weekday_time',
}

/**
 * Format a date with consistent options
 * @param date - Date to format (string, Date object, or undefined/null)
 * @param format - Format option from DateFormatOptions
 * @returns Formatted date string or empty string if date is invalid
 */
const formatDate = (
  date?: Date | null,
  format: DateFormatOptions = DateFormatOptions.DEFAULT
): string => {
  const localDate = date || new Date();
  const locale = window.localStorage.i18nextLng || 'en-US';

  switch (format) {
    case DateFormatOptions.SHORT:
      return localDate.toLocaleDateString(locale);

    case DateFormatOptions.LONG:
      return localDate.toLocaleDateString(locale, {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });

    case DateFormatOptions.TIME:
      return localDate.toLocaleTimeString(locale);

    case DateFormatOptions.DATETIME:
      return localDate.toLocaleString(locale);

    case DateFormatOptions.WEEKDAY_TIME:
      return localDate.toLocaleString(locale, {
        weekday: 'long',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
      });

    case DateFormatOptions.DEFAULT:
    default:
      return localDate.toLocaleString(locale);
  }
};

export const DateToString = (date?: Date | null): string => {
  return formatDate(date, DateFormatOptions.DEFAULT);
};

export const StringToDate = (timestamp: string | null | undefined): Date => {
  if (timestamp == null || timestamp == undefined) {
    console.log('[DateTime/StringToDate]: empty timestamp');
    return DateNow();
  }

  const parsedDate = parseDate(timestamp);
  if (parsedDate == undefined) {
    console.log('[DateTime/StringToDate]: empty timestamp: ' + timestamp);
    return DateNow();
  }
  return parsedDate;
};
