#!/bin/bash

# Environment setup script for nginx deployment
set -e

echo "🔧 Setting up environment for nginx deployment..."

# Default values - update these for your environment
DEFAULT_OAUTH_URL="http://localhost:64023"
DEFAULT_API_URL="http://localhost:5256"
DEFAULT_STORAGE_URL="http://localhost:5157"

# Get user input or use defaults
read -p "Enter OAuth server URL [$DEFAULT_OAUTH_URL]: " OAUTH_URL
OAUTH_URL=${OAUTH_URL:-$DEFAULT_OAUTH_URL}

read -p "Enter API server URL [$DEFAULT_API_URL]: " API_URL
API_URL=${API_URL:-$DEFAULT_API_URL}

read -p "Enter Storage server URL [$DEFAULT_STORAGE_URL]: " STORAGE_URL
STORAGE_URL=${STORAGE_URL:-$DEFAULT_STORAGE_URL}

read -p "Enter your domain name [localhost]: " DOMAIN
DOMAIN=${DOMAIN:-localhost}

echo ""
echo "📝 Configuration summary:"
echo "  - OAuth URL: $OAUTH_URL"
echo "  - API URL: $API_URL"
echo "  - Storage URL: $STORAGE_URL"
echo "  - Domain: $DOMAIN"
echo ""

read -p "Continue with this configuration? (y/N): " CONFIRM
if [[ ! $CONFIRM =~ ^[Yy]$ ]]; then
    echo "❌ Setup cancelled"
    exit 1
fi

# Create environment file
echo "📄 Creating .env.production file..."
cat > .env.production << EOF
VITE_OAUTH_URL=$OAUTH_URL
VITE_API_URL=$API_URL
VITE_STORAGE_URL=$STORAGE_URL
VITE_API_TIMEOUT=15000
VITE_EXPERIMENTAL=false
EOF

echo "✅ Environment file created: .env.production"

# Update nginx configuration with the domain
if [ "$DOMAIN" != "localhost" ]; then
    echo "🔧 Updating nginx configuration with domain..."
    sed -i "s/server_name localhost;/server_name $DOMAIN;/" nginx-simple.conf
    echo "✅ nginx configuration updated with domain: $DOMAIN"
fi

# Update nginx configuration with backend URLs
echo "🔧 Updating nginx configuration with backend URLs..."

# Extract host and port from URLs
OAUTH_HOST_PORT=$(echo $OAUTH_URL | sed 's|http[s]*://||' | sed 's|/$||')
API_HOST_PORT=$(echo $API_URL | sed 's|http[s]*://||' | sed 's|/$||')
STORAGE_HOST_PORT=$(echo $STORAGE_URL | sed 's|http[s]*://||' | sed 's|/$||')

# Update nginx config
sed -i "s|proxy_pass http://localhost:64023/v1/oauth2/;|proxy_pass $OAUTH_URL/v1/oauth2/;|" nginx-simple.conf
sed -i "s|proxy_pass http://localhost:5256/api/;|proxy_pass $API_URL/api/;|" nginx-simple.conf
sed -i "s|proxy_pass http://localhost:5157/api/Files/;|proxy_pass $STORAGE_URL/api/Files/;|" nginx-simple.conf

echo "✅ nginx configuration updated with backend URLs"

echo ""
echo "🎯 Environment setup completed!"
echo ""
echo "📋 Next steps:"
echo "  1. Run: chmod +x deploy-nginx-simple.sh"
echo "  2. Run: sudo ./deploy-nginx-simple.sh"
echo ""
echo "📁 Files created/updated:"
echo "  - .env.production (environment variables)"
echo "  - nginx-simple.conf (nginx configuration)"
echo ""
