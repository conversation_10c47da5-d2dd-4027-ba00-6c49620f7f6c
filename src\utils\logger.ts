/**
 * Centralized logging utility
 * Provides consistent logging with environment-based filtering
 */

import { DateNow } from './dateTime';

// Set to true to enable debug logs in production (not recommended)
const FORCE_DEBUG = false;

// Log levels
export enum LogLevel {
  ERROR = 'ERROR',
  WARN = 'WARN',
  INFO = 'INFO',
  DEBUG = 'DEBUG',
}

// Only show debug logs in development
const isDev = process.env.NODE_ENV !== 'production';

/**
 * Log a message with the specified level and context
 * @param level - Log level
 * @param context - Context for the log (component/module name)
 * @param message - Message to log
 * @param data - Optional data to include
 */
export function log(level: LogLevel, context: string, message: string, ...data: any[]): void {
  // Skip debug logs in production unless forced
  if (level === LogLevel.DEBUG && !isDev && !FORCE_DEBUG) {
    return;
  }

  const timestamp = DateNow().toISOString();
  const prefix = `[${timestamp}] [${level}] [${context}]`;

  switch (level) {
    case LogLevel.ERROR:
      console.error(prefix, message, ...data);
      break;
    case LogLevel.WARN:
      console.warn(prefix, message, ...data);
      break;
    case LogLevel.DEBUG:
      if (isDev || FORCE_DEBUG) {
        console.debug(prefix, message, ...data);
      }
      break;
    case LogLevel.INFO:
    default:
      console.log(prefix, message, ...data);
      break;
  }
}

// Convenience methods
export const logError = (context: string, message: string, ...data: any[]) =>
  log(LogLevel.ERROR, context, message, ...data);

export const logWarn = (context: string, message: string, ...data: any[]) =>
  log(LogLevel.WARN, context, message, ...data);

export const logInfo = (context: string, message: string, ...data: any[]) =>
  log(LogLevel.INFO, context, message, ...data);

export const logDebug = (context: string, message: string, ...data: any[]) =>
  log(LogLevel.DEBUG, context, message, ...data);

// Default export for convenience
export default {
  error: logError,
  warn: logWarn,
  info: logInfo,
  debug: logDebug,
};
