interface TagProps {
  text: string;
}

interface TagContainerProps {
  texts: string[];
}

const TagContainer: React.FC<TagContainerProps> = ({ texts }) => {
  return (
    <div className={'flex gap-small justify-left'}>
      {texts.map((t: string, index) => {
        return <Tag key={index} text={t} />;
      })}
    </div>
  );
};
const Tag: React.FC<TagProps> = ({ text }) => {
  return (
    <div
      className={
        'flex items-center bg-secondary text-inverted-light h-7 pl-medium pr-medium rounded-md uppercase font-semibold'
      }
    >
      <span>{text}</span>
    </div>
  );
};

export default TagContainer;
