import React, { useState, useEffect, useRef, useContext, useCallback, memo } from 'react';
import { useNavigate } from 'react-router-dom';
import { TreeNodeType } from '@/utils/types';
import { TreeContext } from '@/context/TreeContext';
import SearchInput from '@/components/shared/SearchInput/SearchInput';
import ComponentTypeIcon from '@/components/Tree/TreeNodeComponentIcon/ComponentTypeIcon';
import { SearchService } from '@/services/searchService';
import { useTranslation } from 'react-i18next';
interface ComponentSearchProps {
  className?: string;
}

const ComponentSearch: React.FC<ComponentSearchProps> = ({ className = '' }) => {
  const { t } = useTranslation();
  const treeContext = useContext(TreeContext);
  const navigate = useNavigate();

  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<TreeNodeType[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ left: 0, top: 0, width: 0 });
  const searchContainerRef = useRef<HTMLDivElement>(null);

  // Update dropdown position whenever it's shown
  useEffect(() => {
    if (showDropdown && searchContainerRef.current) {
      const rect = searchContainerRef.current.getBoundingClientRect();
      setDropdownPosition({
        left: rect.left,
        top: rect.bottom + window.scrollY,
        width: rect.width,
      });
    }
  }, [showDropdown, searchResults]);

  // Handle search with debounce
  useEffect(() => {
    const debounceTimeout = setTimeout(() => {
      if (searchTerm && searchTerm.length >= 2) {
        handleSearchRequest();
      } else if (!searchTerm) {
        setSearchResults([]);
        setShowDropdown(false);
      }
    }, 500);

    return () => clearTimeout(debounceTimeout);
  }, [searchTerm]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchContainerRef.current &&
        !searchContainerRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSearchRequest = async () => {
    if (!searchTerm) return;

    setIsSearching(true);
    try {
      const results = await SearchService.searchComponents(searchTerm);
      setSearchResults(results);
      setShowDropdown(true);
    } catch (error) {
      console.error('Error searching components:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const handleSearch = useCallback((value: string) => {
    setSearchTerm(value);
    if (value.length > 0) {
      setShowDropdown(true);
    } else {
      setShowDropdown(false);
    }
  }, []);

  const handleComponentClick = useCallback(
    (result: TreeNodeType) => {
      // Close the dropdown and clear search first
      setShowDropdown(false);
      setSearchTerm('');

      SearchService.handleComponentSelection(result, treeContext, navigate);
    },
    [treeContext, navigate]
  );

  const handleDirectoryClick = useCallback(
    (result: TreeNodeType) => {
      // Close the dropdown and clear search first
      setShowDropdown(false);
      setSearchTerm('');

      SearchService.handleDirectorySelection(result, treeContext, navigate);
    },
    [treeContext, navigate]
  );

  const toggleDropdown = useCallback(() => {
    setShowDropdown(prev => !prev);
  }, []);

  // Memoize the dropdown rendering to prevent unnecessary re-renders
  const renderDropdown = useCallback(() => {
    if (!showDropdown) return null;

    // Group search results by type
    const componentResults = searchResults.filter(result => result.type === 'Component');
    const directoryResults = searchResults.filter(result => result.type !== 'Component');

    return (
      <div
        className="shadow-lg rounded-md border border-border-color max-h-80 overflow-y-auto bg-backgropund fixed z-[999]"
        style={{
          left: `${dropdownPosition.left}px`,
          top: `${dropdownPosition.top}px`,
          width: `${dropdownPosition.width}px`,
        }}
      >
        {searchResults.length > 0 ? (
          <div className="py-1">
            {/* Components Section */}
            {componentResults.length > 0 && (
              <>
                <div className="px-4 py-2 bg-gray-100 font-medium text-gray-700">Components</div>
                {componentResults.map(result => (
                  <div
                    key={result.id}
                    className="px-4 py-2 hover:bg-hover cursor-pointer pl-6"
                    onClick={() => handleComponentClick(result)}
                  >
                    <div className="flex items-center">
                      <div className="w-6 h-6 mr-2 flex-shrink-0 flex items-center justify-center">
                        {result.componentType && result.componentType.icon ? (
                          <ComponentTypeIcon data={result.componentType.icon} size={4} />
                        ) : (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                            className="h-5 w-5"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>
                        )}
                      </div>
                      <div className="flex flex-col">
                        <span className="font-medium">{result.name}</span>
                        <span className="text-tiny text-gray-500">{result.id}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </>
            )}

            {/* Directories Section */}
            {directoryResults.length > 0 && (
              <>
                <div className="px-4 py-2 bg-gray-100 font-medium text-gray-700">Directories</div>
                {directoryResults.map(result => (
                  <div
                    key={result.id}
                    className="px-4 py-2 hover:bg-hover cursor-pointer pl-6"
                    onClick={() => handleDirectoryClick(result)}
                  >
                    <div className="flex items-center">
                      <div className="w-6 h-6 mr-2 flex-shrink-0 flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          className="h-5 w-5"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
                          />
                        </svg>
                      </div>
                      <div className="flex flex-col">
                        <span className="font-medium">{result.name}</span>
                        <span className="text-tiny text-gray-500">{result.id}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </>
            )}
          </div>
        ) : (
          <div className="px-4 py-3 text-center text-gray-500">
            <div className="flex items-center justify-center mb-1">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                className="h-5 w-5 mr-1 text-gray-400"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span>No results found</span>
            </div>
            <div className="text-tiny">Try a different search term</div>
          </div>
        )}
      </div>
    );
  }, [showDropdown, searchResults, dropdownPosition, handleComponentClick, handleDirectoryClick]);

  return (
    <div className={`relative ${className}`} ref={searchContainerRef}>
      <SearchInput
        placeholder={t('header.searchComponentsPlaceholder')}
        onSearch={handleSearch}
        value={searchTerm}
        onDropdownClick={toggleDropdown}
      />
      {renderDropdown()}
    </div>
  );
};

// Export memoized component to prevent unnecessary re-renders
export default memo(ComponentSearch);
