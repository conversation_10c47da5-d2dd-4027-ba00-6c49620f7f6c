import React from 'react';
import VersionDetails from '@/components/VersionDetails/VersionDetails';
import ComponentHeader from '@/components/ComponentHeader/ComponentHeader';
import { ComponentOverview } from '@/components/ComponentOverview/ComponentOverview';
import { useTreeContext } from '@/context/TreeContext';
import WelcomeScreen from '@/components/Welcome/WelcomePage';
import { LoadingSpinner } from '@/components/shared/LoadingSpinner';

const TreeDetails: React.FC = () => {
  const { selectedDetails, contentLoading } = useTreeContext();

  // Extract common classes to reduce duplication
  const baseContainerClasses = 'h-full flex flex-col';
  const contentClasses = 'px-medium py-small md:px-small xs:px-1';

  // If no component is selected, immediately show welcome screen without loading
  if (!selectedDetails) {
    return (
      <div className={`${baseContainerClasses} ${contentClasses}`}>
        <WelcomeScreen />
      </div>
    );
  }

  // Show loading spinner only when actual content is being loaded
  if (contentLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  // Otherwise show the selected component details
  return (
    <div className={`${baseContainerClasses} ${contentClasses}`}>
      <ComponentHeader />
      <ComponentOverview details={selectedDetails} />
      <VersionDetails details={selectedDetails} />
    </div>
  );
};

export default TreeDetails;
