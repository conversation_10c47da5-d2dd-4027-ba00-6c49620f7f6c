//Any change here will probably cause an update in the documentation
//https://auvesy-mdt.atlassian.net/wiki/spaces/RD/pages/830046211/Ignore+List+for+Testing
//Dont forget to update that page!

export enum Features {
  LocalDirectory,
  JobExecution,
  JobResults,
  LockState,
  LocalVsServer,
  PaginationButtons,
  AddComponent,
  WorkingDirectory,
  TagContainer,
  StopJobs,
  TreeSearch,
}

const experimentalFeatures = [
  Features.LocalDirectory,
  Features.JobExecution,
  Features.JobResults,
  Features.LockState,
  Features.LocalVsServer,
  // PaginationButtons removed from experimental features to make it always available
  Features.AddComponent,
  Features.WorkingDirectory,
  Features.TagContainer,
  Features.StopJobs,
  Features.TreeSearch,
];

const featuresNotImplemented = [Features.StopJobs];

export const isFeatureImplemented = (feature: Features): boolean => {
  return !featuresNotImplemented.includes(feature);
};

export const isFeatureActive = (feature: Features): boolean => {
  if (!isFeatureImplemented(feature)) {
    return false;
  }
  const isExperimentalFeature = experimentalFeatures.includes(feature);
  return import.meta.env.VITE_EXPERIMENTAL === 'true' || !isExperimentalFeature;
};
