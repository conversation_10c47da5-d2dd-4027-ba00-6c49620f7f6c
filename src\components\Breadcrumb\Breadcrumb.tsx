import React from 'react';
import homeIconSrc from '@/assets/svgs/dark/home.svg';

interface BreadcrumbProps {
  path?: string[];
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ path = [] }) => {
  return (
    <div className="flex items-left justify-left mt-4">
      {path.length > 0 && (
        <>
          <img src={homeIconSrc} alt="Home" className="mr-small mt-small w-3 h-3" />
          <span className="mx-small text-medium">›</span>
        </>
      )}
      {path.map((node, index) => (
        <React.Fragment key={index}>
          <h2 className="font-light text-medium">{node}</h2>
          {index < path.length - 1 && <span className="mx-small text-medium">›</span>}
        </React.Fragment>
      ))}
    </div>
  );
};

export default Breadcrumb;
