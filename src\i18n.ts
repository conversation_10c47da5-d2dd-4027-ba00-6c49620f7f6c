import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import HttpBackend from 'i18next-http-backend';
import languageDetector from 'i18next-browser-languagedetector';

i18n
  .use(HttpBackend)
  .use(initReactI18next)
  .use(languageDetector)
  .init({
    fallbackLng: 'en',
    debug: false,
    ns: [
      'common',
      'languages',
      'login',
      'header',
      'componentOverview',
      'jobs',
      'tree',
      'details',
      'version',
    ],
    defaultNS: 'common',
    backend: {
      loadPath: '/locales/{{lng}}/translation.json',
      requestOptions: {
        cache: 'no-store',
      },
    },
    interpolation: {
      escapeValue: false,
    },
    react: {
      useSuspense: true,
      bindI18n: 'languageChanged loaded',
      bindI18nStore: 'added removed',
    },
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
      lookupLocalStorage: 'i18nextLng',
    },
    supportedLngs: ['en', 'de'],
    load: 'languageOnly',
  });

export default i18n;
