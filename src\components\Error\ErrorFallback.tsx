// Standardized fallback UI for all error boundaries.
// Usage: Pass as the fallback prop to <ErrorBoundary>.
// Example: <ErrorBoundary fallback={<ErrorFallback />}>
// This component provides a consistent error message and reload button for users.
//
// Do not create custom fallback UIs elsewhere; use this for consistency.

import React from 'react';
import { useTranslation } from 'react-i18next';

const ErrorFallback: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="flex flex-col items-center justify-center h-screen space-y-4">
      <h2 className="text-xlarge font-semibold text-gray-800">{t('error.somethingWentWrong')}</h2>
      <p className="text-gray-600">{t('error.tryRefreshing')}</p>
      <button
        onClick={() => window.location.reload()}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
      >
        {t('error.refreshPage')}
      </button>
    </div>
  );
};

export default ErrorFallback;
