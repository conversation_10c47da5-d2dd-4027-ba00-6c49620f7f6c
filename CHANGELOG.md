# Changelog

All notable changes to the Octoplant Web Application will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Semantic versioning implementation
- Version display component
- Version management utilities
- Enhanced build scripts for releases

### Changed
- Updated package.json with version management scripts
- Enhanced Vite configuration for version injection

## [0.1.0] - 2024-01-01

### Added
- Initial React application setup with TypeScript
- Vite build configuration and development server
- Basic authentication system with OIDC client
- Internationalization support with i18next
- Tree component implementation with react-arborist
- Tailwind CSS styling framework
- ESLint and Prettier code formatting
- <PERSON>sky pre-commit hooks
- GitLab CI/CD pipeline configuration
- IIS deployment documentation and scripts
- Basic UI components and layouts
- Responsive design implementation
- Development proxy server configuration
- Environment-specific configuration support

### Technical Details
- **Framework**: React 18.3.1 with TypeScript
- **Build Tool**: Vite 5.4.19
- **Styling**: Tailwind CSS 3.4.14
- **Authentication**: OIDC Client TS 3.0.1
- **State Management**: TanStack React Query 5.66.11
- **Internationalization**: i18next 23.7.20
- **Tree Component**: React Arborist 3.2.0
- **Testing**: Testing Library with Jest DOM
- **Code Quality**: ESLint + Prettier + Husky

### Infrastructure
- **Deployment**: IIS on Windows Server
- **CI/CD**: GitLab CI with semantic version support
- **Environments**: Development, Staging, Production
- **Proxy Configuration**: Express server for development
- **SSL**: HTTPS support with certificate management

---

## Version History Template

### [X.Y.Z] - YYYY-MM-DD

#### Added
- New features and functionality

#### Changed
- Changes to existing functionality

#### Deprecated
- Features that will be removed in future versions

#### Removed
- Features that have been removed

#### Fixed
- Bug fixes

#### Security
- Security improvements and fixes

---

## Release Types

### Major Version (X.0.0)
- Breaking changes requiring user action
- API contract changes
- Authentication system overhauls
- UI/UX redesigns affecting core workflows
- Removal of deprecated features

### Minor Version (0.X.0)
- New features and enhancements
- New UI components or pages
- Performance improvements
- New integrations
- Enhanced functionality

### Patch Version (0.0.X)
- Bug fixes and maintenance
- Security patches
- Performance optimizations
- Dependency updates
- Code cleanup and refactoring
