# PowerShell script for environment setup on Windows

Write-Host "Setting up environment for nginx deployment on Windows..." -ForegroundColor Green

# Default values - update these for your environment
$OAUTH_URL = "http://localhost:64023"
$API_URL = "http://localhost:5256"
$STORAGE_URL = "http://localhost:5157"
$DOMAIN = "localhost"

Write-Host ""
Write-Host "Configuration summary:" -ForegroundColor Cyan
Write-Host "  - OAuth URL: $OAUTH_URL" -ForegroundColor White
Write-Host "  - API URL: $API_URL" -ForegroundColor White
Write-Host "  - Storage URL: $STORAGE_URL" -ForegroundColor White
Write-Host "  - Domain: $DOMAIN" -ForegroundColor White
Write-Host ""

# Create environment file
Write-Host "Creating .env.production file..." -ForegroundColor Cyan
try {
    $envContent = "VITE_OAUTH_URL=$OAUTH_URL`nVITE_API_URL=$API_URL`nVITE_STORAGE_URL=$STORAGE_URL`nVITE_API_TIMEOUT=15000`nVITE_EXPERIMENTAL=false"
    Set-Content -Path ".env.production" -Value $envContent -Encoding UTF8
    Write-Host "Environment file created: .env.production" -ForegroundColor Green
} catch {
    Write-Host "Failed to create environment file: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Update nginx configuration with the domain
if ($DOMAIN -ne "localhost") {
    Write-Host "Updating nginx configuration with domain..." -ForegroundColor Cyan
    try {
        if (Test-Path "nginx-windows.conf") {
            $configContent = Get-Content "nginx-windows.conf" -Raw
            $configContent = $configContent -replace "server_name localhost;", "server_name $DOMAIN;"
            Set-Content "nginx-windows.conf" $configContent
            Write-Host "nginx configuration updated with domain: $DOMAIN" -ForegroundColor Green
        } else {
            Write-Host "nginx-windows.conf not found, will use default domain" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "Failed to update nginx configuration: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Update nginx configuration with backend URLs
Write-Host "Updating nginx configuration with backend URLs..." -ForegroundColor Cyan
try {
    if (Test-Path "nginx-windows.conf") {
        $configContent = Get-Content "nginx-windows.conf" -Raw
        
        # Update proxy_pass URLs
        $configContent = $configContent -replace "proxy_pass http://localhost:64023/v1/oauth2/;", "proxy_pass $OAUTH_URL/v1/oauth2/;"
        $configContent = $configContent -replace "proxy_pass http://localhost:5256/api/;", "proxy_pass $API_URL/api/;"
        $configContent = $configContent -replace "proxy_pass http://localhost:5157/api/Files/;", "proxy_pass $STORAGE_URL/api/Files/;"
        
        Set-Content "nginx-windows.conf" $configContent
        Write-Host "nginx configuration updated with backend URLs" -ForegroundColor Green
    } else {
        Write-Host "nginx-windows.conf not found, please update URLs manually" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Failed to update nginx configuration: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Environment setup completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "  1. Install nginx if not already installed:" -ForegroundColor White
Write-Host "     - Download from http://nginx.org/en/download.html" -ForegroundColor White
Write-Host "     - Extract to C:\nginx" -ForegroundColor White
Write-Host "     - Or use Chocolatey: choco install nginx" -ForegroundColor White
Write-Host "  2. Run deployment script as Administrator:" -ForegroundColor White
Write-Host "     .\deploy-nginx-windows.ps1" -ForegroundColor White
Write-Host ""
Write-Host "Files created/updated:" -ForegroundColor Cyan
Write-Host "  - .env.production (environment variables)" -ForegroundColor White
Write-Host "  - nginx-windows.conf (nginx configuration)" -ForegroundColor White
Write-Host ""

# Check if nginx is installed
$nginxPaths = @("C:\nginx\nginx.exe", "C:\tools\nginx\nginx.exe")
$nginxFound = $false

foreach ($path in $nginxPaths) {
    if (Test-Path $path) {
        Write-Host "nginx found at: $path" -ForegroundColor Green
        $nginxFound = $true
        break
    }
}

if (-not $nginxFound) {
    Write-Host "nginx not found. Please install nginx before running the deployment script." -ForegroundColor Yellow
    Write-Host "   You can install it via Chocolatey: choco install nginx" -ForegroundColor Yellow
}
