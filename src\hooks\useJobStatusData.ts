import { useContext, useEffect, useState, useCallback } from 'react';
import { JobStatusContext } from '@/context/JobStatusContext';
import { JobStatus } from '@/utils/jobs';

export const useJobStatusData = (
  jobId: string | null | undefined
): { status: JobStatus; isLoading: boolean; error: any; refresh: () => void } => {
  const jobStatusContext = useContext(JobStatusContext);

  if (!jobStatusContext) {
    throw new Error('useJobStatusData must be used within a JobStatusProvider');
  }

  // Effect to register the jobId for tracking when it changes or is first provided
  useEffect(() => {
    if (jobId && jobStatusContext.trackJobId) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`[useJobStatusData] useEffect: Calling trackJobId for ${jobId}`);
      }
      jobStatusContext.trackJobId(jobId);
    }
  }, [jobId, jobStatusContext.trackJobId]); // Ensure trackJobId is stable or correctly listed as a dep

  // getStatus is now safe to call during render as it doesn't cause state updates in the provider
  const {
    data: status,
    isLoading,
    error,
  } = jobId
    ? jobStatusContext.getStatus(jobId)
    : { data: JobStatus.Waiting, isLoading: false, error: null };

  // Cr eate a refresh function that doesn't update state during render
  const refresh = useCallback(() => {
    if (jobId) jobStatusContext.refreshStatus(jobId);
  }, []);

  return {
    status,
    isLoading,
    error,
    refresh,
  };
};
