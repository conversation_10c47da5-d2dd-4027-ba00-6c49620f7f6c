import React from 'react';
import { motion } from 'framer-motion';
import rightArrowSvg from '@/assets/svgs/dark/angle-right.svg';

export type ArrowDirection = 'up' | 'right' | 'down' | 'left';

interface ArrowIconProps {
  direction?: ArrowDirection;
  onClick?: (e: React.MouseEvent) => void;
  className?: string;
  size?: number;
}

/**
 * A minimal arrow icon component that rotates to indicate direction.
 * Uses a single right-pointing arrow SVG and rotates it for different directions.
 */
const ArrowIcon = React.memo(
  ({ direction = 'right', onClick, className = '', size = 24 }: ArrowIconProps) => {
    // Map direction to rotation degree
    const getRotation = (): number => {
      switch (direction) {
        case 'down':
          return 90; // 90 degrees = right arrow rotated down
        case 'left':
          return 180; // 180 degrees = right arrow rotated left
        case 'up':
          return 270; // 270 degrees = right arrow rotated up
        default: // 'right'
          return 0; // 0 degrees = right arrow stays as is
      }
    };

    return (
      <motion.span
        className={`arrow-icon ${className}`}
        style={{
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: onClick ? 'pointer' : 'default',
        }}
        onClick={onClick}
        role={onClick ? 'button' : undefined}
        aria-label={`Arrow pointing ${direction}`}
        tabIndex={onClick ? 0 : undefined}
      >
        <motion.div
          animate={{ rotate: getRotation() }}
          transition={{
            duration: 0.2,
            ease: [0.4, 0, 0.2, 1], // Material Design standard easing
          }}
          style={{
            display: 'flex',
            willChange: 'transform',
          }}
        >
          <img
            src={rightArrowSvg}
            alt={`Arrow pointing ${direction}`}
            width={size}
            height={size}
            style={{ display: 'block' }}
          />
        </motion.div>
      </motion.span>
    );
  }
);

ArrowIcon.displayName = 'ArrowIcon';

export default ArrowIcon;
