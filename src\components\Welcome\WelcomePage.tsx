import { Features, isFeatureActive } from '@/utils/featureFlags';
import { useTranslation } from 'react-i18next';
import { ActionCard } from '@/components/shared/ActionCards/ActionCard';
import { localizedHelpLink } from '@/utils/language';

export default function WelcomeScreen() {
  const { t, i18n } = useTranslation();
  const ACTION_ITEMS = [
    {
      iconName: 'shake-hands',
      title: t('details.needHelpTitle'),
      description: t('details.needHelp'),
      buttonText: t('details.browseHelp'),
      buttonIconName: 'book-open',
      action: () => {
        window.open(localizedHelpLink(i18n.language), '_blank', 'noopener,noreferrer');
      },
    },
  ];

  if (isFeatureActive(Features.AddComponent)) {
    ACTION_ITEMS.unshift({
      iconName: 'server',
      title: t('details.nothingSelectedTitle'),
      description: t('details.nothingSelected'),
      buttonText: t('details.addComponent'),
      buttonIconName: 'add',
      action: () => {},
    });
  }

  return (
    <main className="p-6 font-titillium">
      <header className="mb-12 space-y-4">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold uppercase">
            {t('login.welcome')} {t('common.serverProduct')}
          </h1>
          <h2 className="text-2xl uppercase">{t('common.productName')}</h2>
        </div>
        <p className="text-medium font-light max-w-3xl leading-relaxed">
          {t('login.introduction')}
        </p>
      </header>
      <section className="grid grid-cols-[405px_405px_1fr]">
        {ACTION_ITEMS.map((item, index) => (
          <ActionCard
            key={index}
            iconName={item.iconName}
            title={item.title}
            description={item.description}
            buttonText={item.buttonText}
            buttonIconName={item.buttonIconName}
            onButtonClick={item.action}
          />
        ))}
      </section>
    </main>
  );
}
