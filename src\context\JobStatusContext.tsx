import { fetchJobStatus } from '@/services/api/index';
import { JobStatus } from '@/utils/jobs';
import {
  createContext,
  FC,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useQueries, useQueryClient } from '@tanstack/react-query';

export interface JobStatusContextJobState {
  isLoading: boolean;
  error: Error | null;
  data: JobStatus;
}

export interface JobStatusContextProps {
  getStatus: (id: string) => JobStatusContextJobState;
  refreshStatus: (id: string) => void;
  getStatusMap: () => Map<string, JobStatusContextJobState>;
  trackJobId: (id: string) => void;
}

export const JobStatusContext = createContext<JobStatusContextProps | undefined>(undefined);

export const useJobStatusContext = (): JobStatusContextProps => {
  const context = useContext(JobStatusContext);
  if (!context) {
    throw new Error('useJobStatusContext must be used within a JobContextProvider');
  }
  return context;
};

export const JobStatusProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const [jobIds, setJobIds] = useState<string[]>([]);
  const [pendingJobIds, setPendingJobIds] = useState<string[]>([]);
  const queryClient = useQueryClient();

  useEffect(() => {
    if (pendingJobIds.length > 0) {
      const newJobIds = pendingJobIds.filter(id => !jobIds.includes(id));
      if (newJobIds.length > 0) {
        setJobIds(prev => [...prev, ...newJobIds]);
      }
      setPendingJobIds([]);
    }
  }, [pendingJobIds, jobIds]);

  const queries = jobIds.map(id => ({
    queryKey: ['jobStatus', id],
    queryFn: () => fetchJobStatus(id),
    staleTime: 60 * 1000,
    refetchInterval: 60 * 1000,
    retry: 1,
    retryDelay: 2000,
  }));

  const queryResults = useQueries({
    queries: jobIds.length > 0 ? queries : [],
  });

  const jobStatusMap = useMemo(() => {
    const map = new Map<string, JobStatusContextJobState>();
    jobIds.forEach((id, index) => {
      if (index < queryResults.length) {
        const result = queryResults[index];
        map.set(id, {
          data: result.data || JobStatus.Waiting,
          isLoading: result.isLoading,
          error: result.error as Error | null,
        });
      }
    });
    return map;
  }, [jobIds, queryResults]);

  // Track IDs that need to be added to pending queue
  const [idsToTrack, setIdsToTrack] = useState<string[]>([]);

  // Effect to process idsToTrack and move them to pendingJobIds
  useEffect(() => {
    if (idsToTrack.length > 0) {
      const newIds = idsToTrack.filter(id => !jobIds.includes(id) && !pendingJobIds.includes(id));
      if (newIds.length > 0) {
        if (process.env.NODE_ENV === 'development') {
          console.log(
            '[JobStatusContext] Moving new IDs from idsToTrack to pendingJobIds:',
            newIds
          );
        }
        setPendingJobIds(prev => [...new Set([...prev, ...newIds])]); // Use Set to ensure uniqueness
      }
      setIdsToTrack([]); // Clear the queue after processing
    }
  }, [idsToTrack, jobIds, pendingJobIds, setPendingJobIds]);

  const getStatus = useCallback(
    (id: string): JobStatusContextJobState => {
      if (!id || id.length <= 0) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('[JobStatusContext] getStatus called with invalid id');
        }
        return { data: JobStatus.Waiting, isLoading: false, error: null };
      }

      const existingStatus = jobStatusMap.get(id);
      if (existingStatus) {
        return existingStatus;
      }

      // If not in map, it means we are not actively tracking or it's new.
      // isLoading: true will be shown until trackJobId (called from useEffect in useJobStatusData)
      // eventually updates the jobStatusMap.
      return { data: JobStatus.Waiting, isLoading: true, error: null };
    },
    [jobStatusMap]
  );

  // New method to explicitly request tracking of a job ID
  const trackJobId = useCallback(
    (id: string) => {
      if (id && id.length > 0) {
        const isAlreadyFetchedOrBeingFetched = jobIds.includes(id) || pendingJobIds.includes(id);
        const isAlreadyInQueue = idsToTrack.includes(id);

        if (!isAlreadyFetchedOrBeingFetched && !isAlreadyInQueue) {
          if (process.env.NODE_ENV === 'development') {
            console.log(`[JobStatusContext] Queuing ${id} for tracking via setIdsToTrack.`);
          }
          setIdsToTrack(prev => [...new Set([...prev, id])]); // Use Set to ensure uniqueness
        }
      }
    },
    [jobIds, pendingJobIds, idsToTrack, setIdsToTrack]
  );

  const refreshStatus = useCallback(
    (id: string): void => {
      if (!id || id.length <= 0) {
        return;
      }

      if (!jobIds.includes(id)) {
        setJobIds(prev => [...prev, id]);
      }

      queryClient.invalidateQueries({ queryKey: ['jobStatus', id] });
    },
    [jobIds, queryClient]
  );

  const getStatusMap = useCallback(() => jobStatusMap, [jobStatusMap]);

  const contextValue = useMemo(
    () => ({
      getStatus,
      refreshStatus,
      getStatusMap,
      trackJobId,
    }),
    [getStatus, refreshStatus, getStatusMap, trackJobId]
  );

  // Log status changes for debugging
  useEffect(() => {
    console.log('[JobStatusContext] Current job statuses:', Object.fromEntries(jobStatusMap));
  }, [jobStatusMap]);

  if (process.env.NODE_ENV === 'development') {
    useEffect(() => {
      console.log('[JobStatusContext] Current job IDs:', jobIds);
    }, [jobIds]);
  }

  return <JobStatusContext.Provider value={contextValue}>{children}</JobStatusContext.Provider>;
};
