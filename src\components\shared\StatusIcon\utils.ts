import { EventLogType } from '@/utils/types';
import { t } from 'i18next';

// Mapping objects for display text, background color, and comparator
const displayTextMap: Record<EventLogType, string> = {
  [EventLogType.information]: t('events.information'),
  [EventLogType.warning]: t('events.warning'),
  [EventLogType.error]: t('events.error'),
};

const backgroundColorMap: Record<EventLogType, string> = {
  [EventLogType.information]: 'bg-information',
  [EventLogType.warning]: 'bg-warning',
  [EventLogType.error]: 'bg-error',
};

const foregroundColorMap: Record<EventLogType, string> = {
  [EventLogType.information]: 'text-primary border border-primary',
  [EventLogType.warning]: 'text-primary',
  [EventLogType.error]: 'text-inverted-light',
};

const symbolMap: Record<EventLogType, string> = {
  [EventLogType.information]: 'i',
  [EventLogType.warning]: '\u26A0',
  [EventLogType.error]: 'X',
};

// Utility functions using mapping objects
export const displayTextForEventLogType = (tp: EventLogType): string =>
  displayTextMap[tp] || t('common.unknown');

export const backgroundColorForEventLogType = (tp: EventLogType): string =>
  backgroundColorMap[tp] || 'bg-information';

export const foregroundColorForEventLogType = (tp: EventLogType): string =>
  foregroundColorMap[tp] || 'text-primary border border-primary';

export const symbolForEventLogType = (tp: EventLogType): string => symbolMap[tp] || '?';
