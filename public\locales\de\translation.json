{"about": {"address": "76829 Landau, Fichtenstraße 38B", "copyright": "Copyright AUVESY GmbH", "displayUrl": "amdt.com", "url": "https://www.amdt.com"}, "auth": {"errorOccurred": "Während der Anmeldung ist ein Fehler aufgetreten", "errorSigningIn": "Fehler bei der Anmeldung:", "initializingLogin": "Anmeldung wird initialisiert...", "loadingApplication": "Anwendung wird geladen...", "pleaseWait": "Bitte warten...", "processingLogin": "Anmeldung läuft...", "redirectingToLogin": "Weiterleiten zur Anmeldung...", "refreshingSession": "Sitzung wird aktualisiert...", "signingIn": "Anmelden...", "signingOut": "Abmelden..."}, "clipboard": {"copied": "Der Text wurde in die Zwischenablage kopiert: ", "unableToCopy": "Der Text konnte nicht in die Zwischenablage kopiert werden."}, "common": {"about": "<PERSON><PERSON>", "deactivated": "<PERSON>ak<PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON>", "errorOccurred": "Fehler aufgetreten", "introduction": "octoplant ist die marktführende Versionskontroll- und Backup-Software, die weit mehr kann, als nur Ihre Produktionsumgebung zu verwalten. Entdecken Sie die Kernfunktionen von octoplant.", "no": "<PERSON><PERSON>", "notAvailable": "<PERSON><PERSON>", "productName": "WebClient", "select": "auswählen", "serverProduct": "Octoplant", "sorry": "Entschuldigung!", "start": "Start", "stop": "Stop", "unknown": "Unbekannt", "version": "Version", "yes": "<PERSON>a"}, "componentOverview": {"checkInPossible": "Check-In möglich", "editStateTimeOfCheck": "Bearbeitungsstatus (Prüfzeitpunkt)", "jobNotRunYet": "Job wurde noch nicht ausgeführt", "localVersion": "Lokale Version", "noJobConfigured": "<PERSON><PERSON>", "notAvailableOnServer": "Nicht auf dem <PERSON> verfügbar", "notLocallyAvailable": "Lokal nicht verfügbar", "serverVersion": "Serverversion", "versionDetails": "Versionsdetails"}, "components": {"componentTypeID": "Komponententyp-ID", "id": "Komponenten-ID", "masterData": "Stammdaten", "workingDir": "Arbeitsverzeichnis"}, "details": {"addComponent": "Komponente hinzufügen", "browseHelp": "<PERSON><PERSON><PERSON> durch<PERSON>", "changeHistory": "Änderungshistorie", "details": "Details", "jobs": "Jobs", "needHelp": "<PERSON><PERSON><PERSON> in unsere Online-Dokumenation im Usercenter my.amdt.com", "needHelpTitle": "Benötigen Sie Hilfe?", "nothingSelected": "Bitte wählen Sie eine Komponente aus oder fügen Sie eine neue Komponente hinzu.", "nothingSelectedTitle": "<PERSON>ine Komponente ausgewählt"}, "events": {"error": "ERR", "information": "INF", "message": "Ereignistext", "occured": "Zeitpunkt", "type": "Ereignistyp", "warning": "WRN"}, "header": {"changeLanguage": "<PERSON><PERSON><PERSON>", "componentDetails": "Komponentendetails", "help": "<PERSON><PERSON><PERSON>", "loggingOut": "Abmelden...", "logout": "Abmelden", "searchComponentsPlaceholder": "Komponenten suchen...", "searchVersionsPlaceholder": "Versionshistorie durchsuchen", "settings": "Einstellungen", "userTitle": "<PERSON><PERSON><PERSON>"}, "jobs": {"backup": "Backup", "backupClient": "mittels BackupClient", "backupVsBackup": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> vs. <PERSON>up", "comapareAgent": "Compare Agent", "deactivatedToolTipTitle": "Job ist deaktiviert", "equal": "<PERSON><PERSON><PERSON>", "eventLog": "Ereignisanzeige", "executedOn": "Ausgeführt am", "execution": "Ausführung", "executionState": "Ausführungsstatus", "failedToStart": "Starten des Jobs fehlgeschlagen", "failedToStop": "Stoppen des Jobs fehlgeschlagen", "id": "Job-ID", "lastExecuted": "Letzte Ausführung", "lastResults": "Letzte Ergebnisse", "latestResults": "Letzte Jobergebnisse", "location": "Ausführungsort", "name": "Jobname", "nextStart": "Nächster Start", "noCompareResults": "Es sind keine Vergleichsergebnisse verfügbar. Entweder wurde kein Vergleich ausgeführt, oder es wurde kein Backup erzeugt.", "noEventLog": "Es sind keine Ereignisanzeigedaten verfügbar.", "noJobResults": "Der Job wurde noch nicht ausgeführt.", "noJobs": "<PERSON>s wurde noch kein Job konfiguriert.", "notEqual": "Unterschiedlich", "notExecuted": "Nicht ausgeführt", "notRepeated": "Keine Aktion definiert", "notScheduled": "Nicht nach Zeitplan", "onError": "<PERSON><PERSON>", "repeated": "Erneuter Versuch {{minutes}} Minute(n) nach Fehlschlag, {{repeats}} Mal", "resultErrorText": "Wärend der Jobausführung sind Fehler aufgetreten. Bitte überprüfen Sie die Ereignisanzeige der Jobausführung.", "resultErrorTitle": "Fehler aufgetreten", "results": "Jobergebnisse", "resultWarningText": "Wärend der Jobausführung sind Warnungen aufgetreten. Bitte überprüfen Sie die Ereignisanzeige der Jobausführung.", "resultWarningTitle": "Warnung aufgetreten", "running": "wird ausgefrührt", "schedule": "Zeitplan", "scheduled": "<PERSON><PERSON>", "start": "Starten", "status": "Status", "stop": "Stoppen", "stopNotImplemented": "<PERSON><PERSON> von Jobs ist noch nicht implementiert.", "triggeredByUrl": "Von einer URL getriggert", "uploadAgent": "Upload Agent", "uploadType": "Uploadtyp", "versionVsBackup": "Serverversion vs. Backup", "waiting": "wartet", "whoToNotify": "Benachrichtigung an", "withWarning": "<PERSON>"}, "languages": {"english": "<PERSON><PERSON><PERSON>", "german": "De<PERSON>ch"}, "lockState": {"locked": "<PERSON><PERSON><PERSON><PERSON>", "lockedBy": "<PERSON><PERSON><PERSON><PERSON> durch", "setLocked": "Komponente sperren", "setUnderDevelopment": "In Bearbeitung", "setUnlocked": "Komponente entsperren", "underDevelopment": "In Bearbeitung", "underDevelopmentBy": "In Bearbeitung durch", "unlocked": "<PERSON>cht ges<PERSON>rt"}, "login": {"errorSigningIn": "Fehler beim An<PERSON>den:", "introduction": "octoplant ist die marktführende Versionskontroll- und Backup-Software, die weit mehr kann, als nur Ihre Produktionsumgebung zu verwalten. Entdecken Sie die Kernfunktionen von octoplant.", "login": "Anmelden", "orLoginWith": "oder anmelden mit", "password": "Passwort", "signingIn": "Anmelden...", "signingOut": "Abmelden...", "signInWithOAuth": "octoplant OAuth", "username": "<PERSON><PERSON><PERSON><PERSON>", "welcome": "Will<PERSON>mmen bei"}, "masterdata": {"masterdata1": "Kunde", "masterdata10": "Anmerkung 3", "masterdata2": "Projekt", "masterdata3": "Land", "masterdata4": "Stadt", "masterdata5": "Werk", "masterdata6": "Halle", "masterdata7": "<PERSON><PERSON><PERSON>", "masterdata8": "Anmerkung 1", "masterdata9": "Anmerkung 2"}, "tree": {"project": "Projekt", "searchPlaceholder": "Suche Komponenten...", "tree": "Baum"}, "version": {"archive": "Archiv", "changes": "Änderungen", "comments": "Kommentare", "compressedSize": "Komprimierte Größe", "computername": "Rechnername", "download": "Download", "size": "Größe", "storage": "<PERSON><PERSON><PERSON><PERSON>", "tags": "Tags", "timestampLocal": "Zeitstempel (lokal)", "username": "<PERSON><PERSON><PERSON><PERSON>", "version": "Version"}}