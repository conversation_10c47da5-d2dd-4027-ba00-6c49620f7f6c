import { t } from 'i18next';
import { Job, JobResult } from './types';
import { SortComparatorDESC as SortComparatorDate } from './dateTime';

/**
 * Enum for job execution states that match the API values
 */
export enum ExecutionState {
  Waiting = 'waiting',
  Running = 'running',
  Failed = 'failed',
  Completed = 'completed',
}

export enum ExecutionConfiguration {
  Scheduled = 'scheduled',
  NotScheduled = 'notScheduled',
  Url = 'url',
  BackupClient = 'backupClient',
}

export enum JobStatus {
  Waiting = 'waiting',
  Running = 'running',
}

const apiStatusString: Record<string, JobStatus> = {
  ['Waiting']: JobStatus.Waiting,
  ['Running']: JobStatus.Running,
};

/**
 * Get display text for a job execution state
 */
export const displayTextForJobStatus = (state: string): string => {
  // Convert to lowercase for case-insensitive comparison
  const normalizedState = state?.toLowerCase();

  // Map of state values to translation keys
  const stateTranslations: Record<string, string> = {
    waiting: 'jobs.waiting',
    running: 'jobs.running',
  };

  // Return the translated text or fallback to notScheduled
  return t(stateTranslations[normalizedState] || 'jobs.waiting');
};

/**
 * Get the latest job result from an array of results
 */
export const latestJobResult = (results?: JobResult[]): JobResult | null => {
  if (!results || results.length === 0) {
    return null;
  }

  // Sort by timestamp descending and return the first (most recent) result
  return [...results].sort((a, b) => SortComparatorDate(a.timestamp, b.timestamp))[0];
};

export const executed = (job: Job): boolean => {
  return job.lastExecution != undefined && job.lastExecution != null;
};

export const transformApiStatus = (res: string): JobStatus =>
  apiStatusString[res] || JobStatus.Waiting;

export const transformApiExecutionConfiguration = (cfg: number): ExecutionConfiguration => {
  const configurationTranslations: Record<number, ExecutionConfiguration> = {
    0: ExecutionConfiguration.NotScheduled,
    1: ExecutionConfiguration.Scheduled,
    2: ExecutionConfiguration.BackupClient,
    4: ExecutionConfiguration.Url,
  };
  console.log('Execution configuration: ' + cfg + ' - ' + configurationTranslations[cfg]);
  return configurationTranslations[cfg] || ExecutionConfiguration.NotScheduled;
};

export const displayTextForExecutionConfiguration = (cfg: ExecutionConfiguration): string => {
  const confurationTexts: Record<ExecutionConfiguration, string> = {
    scheduled: 'jobs.scheduled',
    notScheduled: 'jobs.notScheduled',
    backupClient: 'jobs.backupClient',
    url: 'jobs.triggeredByUrl',
  };

  return t(confurationTexts[cfg] || 'jobs.notScheduled');
};
