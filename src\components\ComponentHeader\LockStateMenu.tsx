import { ToolTip } from '@/components/shared/ToolTip/ToolTip';
import {
  displayTextForLockState,
  displayTextForSetLockState,
  iconForLockState,
} from '@/utils/lockState';
import { LockState } from '@/utils/types';
import { Menu, MenuItem } from '@szhsin/react-menu';
import { t } from 'i18next';
import { useMemo } from 'react';
import ToolButton, { ToolButtonStyle } from '../shared/ToolButton/ToolButton';

interface LockStateMenuProps {
  state: LockState;
  message?: string;
  user?: string;
}

const anchorName = 'lockstate-menu-tooltip-anchor';

const LockStateMenuItem: React.FC<{ state: LockState }> = props => {
  let menuItemIconClassName = '';
  switch (props.state) {
    case LockState.unlocked:
    default: {
      menuItemIconClassName = 'bg-unlocked w-10 h-10 p-2 cursor-pointer';
      break;
    }
    case LockState.locked: {
      menuItemIconClassName = 'bg-locked w-10 h-10 p-2 cursor-pointer';
      break;
    }
    case LockState.underDevelopment: {
      menuItemIconClassName = 'bg-under-development w-10 h-10 p-2 cursor-pointer';
      break;
    }
  }

  const menuItemClassName =
    'group grid grid-cols-[40px_1fr] gap-small m-1 items-center content-center';
  const menuItemLabelClassName =
    'h-10 p-1 pl-4 pr-2 uppercase bg-secondary-background whitespace-nowrap content-center group-hover:bg-hover';
  const clickHandler = () => {};

  return (
    <MenuItem className={menuItemClassName} onClick={() => clickHandler()}>
      <img className={menuItemIconClassName} src={iconForLockState(props.state)} alt="menuItem" />
      <span className={menuItemLabelClassName}>{displayTextForSetLockState(props.state)}</span>
    </MenuItem>
  );
};

const LockStateMenuToolTip: React.FC<LockStateMenuProps> = ({ state, message, user }) => {
  const tooltipTitleMap: Record<LockState, string> = {
    [LockState.locked]: t('lockState.lockedBy'),
    [LockState.underDevelopment]: t('lockState.underDevelopmentBy'),
    [LockState.unlocked]: '',
  };
  return state === LockState.unlocked ? (
    <></>
  ) : (
    <ToolTip anchor={anchorName} title={tooltipTitleMap[state] + ' ' + user} text={message || ''} />
  );
};

const LockStateMenu: React.FC<LockStateMenuProps> = ({ state, message, user }) => {
  const menu = useMemo(() => {
    const label = displayTextForLockState(state);
    const styleMap: Record<LockState, ToolButtonStyle> = {
      [LockState.locked]: ToolButtonStyle.Locked,
      [LockState.unlocked]: ToolButtonStyle.Unlocked,
      [LockState.underDevelopment]: ToolButtonStyle.UnderDevelopment,
    };
    const menuButton = (
      <div className={'flex content-baseline ' + anchorName}>
        <ToolButton
          type={styleMap[state]}
          text={label}
          icon={iconForLockState(state)}
          dropDownIndicator={true}
        />
        <LockStateMenuToolTip state={state} message={message} user={user} />
      </div>
    );
    return (
      <span>
        <Menu menuClassName={'z-50 bg-background drop-shadow-lg'} menuButton={menuButton}>
          <LockStateMenuItem key={'lockStateMenu-' + LockState.locked} state={LockState.locked} />
          <LockStateMenuItem
            key={'lockStateMenu-' + LockState.unlocked}
            state={LockState.unlocked}
          />
          <LockStateMenuItem
            key={'lockStateMenu-' + LockState.underDevelopment}
            state={LockState.underDevelopment}
          />
        </Menu>
      </span>
    );
  }, [state, message, user]);
  return menu;
};
export default LockStateMenu;
