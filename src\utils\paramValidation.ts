// src/utils/paramValidation.ts
// Utility functions for parameter validation

/**
 * Validates that a parameter is not null, undefined, or an empty string
 * @param param - The parameter to validate
 * @param paramName - The name of the parameter for error messages
 * @param functionName - The name of the function for error messages
 * @throws Error if the parameter is invalid
 */
export const validateRequiredParam = <T>(
  param: T | null | undefined,
  paramName: string,
  functionName: string
): T => {
  if (param === null || param === undefined) {
    const error = new Error(`Missing required parameter '${paramName}' in ${functionName}`);
    console.error(`[API] ${error.message}`);
    throw error;
  }

  // Additional validation for string parameters
  if (typeof param === 'string' && param.trim() === '') {
    const error = new Error(
      `Empty string provided for required parameter '${paramName}' in ${functionName}`
    );
    console.error(`[API] ${error.message}`);
    throw error;
  }

  return param;
};

/**
 * Validates that a numeric parameter is not null, undefined, and is a valid number
 * @param param - The parameter to validate
 * @param paramName - The name of the parameter for error messages
 * @param functionName - The name of the function for error messages
 * @param minValue - Optional minimum value
 * @param maxValue - Optional maximum value
 * @throws Error if the parameter is invalid
 */
export const validateNumericParam = (
  param: number | null | undefined,
  paramName: string,
  functionName: string,
  minValue?: number,
  maxValue?: number
): number => {
  if (param === null || param === undefined || isNaN(param)) {
    const error = new Error(`Invalid numeric parameter '${paramName}' in ${functionName}`);
    console.error(`[API] ${error.message}`);
    throw error;
  }

  if (minValue !== undefined && param < minValue) {
    const error = new Error(
      `Parameter '${paramName}' in ${functionName} must be at least ${minValue}`
    );
    console.error(`[API] ${error.message}`);
    throw error;
  }

  if (maxValue !== undefined && param > maxValue) {
    const error = new Error(
      `Parameter '${paramName}' in ${functionName} must be at most ${maxValue}`
    );
    console.error(`[API] ${error.message}`);
    throw error;
  }

  return param;
};

/**
 * Validates an optional parameter, applying validation only if the parameter is provided
 * @param param - The parameter to validate
 * @param validator - The validation function to apply
 * @param defaultValue - The default value to use if the parameter is not provided
 * @returns The validated parameter or the default value
 */
export const validateOptionalParam = <T>(
  param: T | null | undefined,
  validator: (param: T) => T,
  defaultValue: T
): T => {
  if (param === null || param === undefined) {
    return defaultValue;
  }
  return validator(param);
};

/**
 * Validates a node ID parameter, which can be an empty string (for root node)
 * @param id - The ID parameter to validate
 * @param paramName - The name of the parameter for error messages
 * @param functionName - The name of the function for error messages
 * @returns The validated ID
 * @throws Error if the ID is null or undefined
 */
export const validateNodeId = (
  id: string | null | undefined,
  paramName: string,
  functionName: string
): string => {
  if (id === null || id === undefined) {
    const error = new Error(`Missing ${paramName} parameter in ${functionName}`);
    console.error(`[API] ${error.message}`);
    throw error;
  }

  // Empty string is allowed for root node
  return id;
};
