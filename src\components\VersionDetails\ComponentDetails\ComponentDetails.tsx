import React from 'react';
import { VersionDetailsProps } from '../types';
import { useTranslation } from 'react-i18next';
import { useComponentDetails } from '@/hooks/queries/useComponentDetails';
import { useTreeContext } from '@/context/TreeContext';
import { VersionDetailsCurrentPage } from '../types';
import { LoadingSpinner } from '@/components/shared/LoadingSpinner/LoadingSpinner';
import { ComponentDetails as ComponentDetailsType } from '@/utils/types';

import componentIdSvg from '@/assets/svgs/dark/component.svg';
import componentTypeIdSvg from '@/assets/svgs/dark/component-type.svg';
import masterDataSvg from '@/assets/svgs/dark/data.svg';
import MasterData from './MasterData/MasterData';
import IdField from '@/components/shared/KeyValueFieldArea/IdField';
import KeyValueFieldArea, {
  FieldType,
} from '@/components/shared/KeyValueFieldArea/KeyValueFieldArea';

const ComponentDetails: React.FC<VersionDetailsProps> = ({ details }) => {
  const { t } = useTranslation();
  const { currentDetailsPage } = useTreeContext();

  // Only fetch component details when the Details tab is selected
  const isDetailsTabSelected = currentDetailsPage === VersionDetailsCurrentPage.Details;

  const {
    data: componentDetails,
    isLoading,
    error,
  } = useComponentDetails(details?.id || null, {
    enabled: isDetailsTabSelected,
    fetchFullDetails: true, // Explicitly request full details
  });

  // Use the fetched details if available, otherwise fall back to the passed details
  const displayDetails = (componentDetails || details) as ComponentDetailsType;

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return <div className="text-error p-4">Error loading component details: {error.message}</div>;
  }

  const idFields = [
    {
      label: t('components.id'),
      value: displayDetails.id || 'not available',
      icon: componentIdSvg,
      fieldType: FieldType.idField,
    },
    {
      label: t('components.componentTypeID'),
      value: displayDetails.componentType?.id || 'not available',
      icon: componentTypeIdSvg,
      fieldType: FieldType.idField,
    },
  ];
  return (
    <div className={'details-container mt-10'}>
      <div className="flex">
        <KeyValueFieldArea fields={idFields} columnCount={2} />
      </div>

      <div className="flex items-center gap-2 text-primary font-bold mt-2 mb-2">
        <img src={masterDataSvg} alt="Master Data" width="18" height="18" />
        <span>{t('components.masterData')}</span>
      </div>
      <MasterData details={displayDetails} />
    </div>
  );
};

export default ComponentDetails;
