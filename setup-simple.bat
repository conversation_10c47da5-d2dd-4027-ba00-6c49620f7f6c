@echo off
echo 🔧 Setting up environment for nginx deployment...

REM Create .env.production with default values
echo Creating .env.production file...
(
echo VITE_OAUTH_URL=https://localhost:64023
echo VITE_API_URL=http://localhost:5256
echo VITE_STORAGE_URL=http://localhost:5157
echo VITE_API_TIMEOUT=15000
echo VITE_EXPERIMENTAL=false
) > .env.production

echo ✅ Environment file created: .env.production

REM Update nginx configuration if it exists
if exist nginx-windows.conf (
    echo 🔧 nginx-windows.conf found - ready for deployment
) else (
    echo ⚠️  nginx-windows.conf not found - make sure you have the nginx config file
)

echo.
echo 🎯 Environment setup completed!
echo.
echo 📋 Next steps:
echo   1. Install nginx if not already installed
echo   2. Run: deploy-nginx-windows.ps1 (as Administrator)
echo.
echo 📁 Files created:
echo   - .env.production (environment variables)
echo.

pause
