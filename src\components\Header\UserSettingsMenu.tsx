import { useAuth } from '@/context/AuthContext';
import { useCallback, useMemo, useState } from 'react';
import {
  languageCodeToShortCountryCode,
  languageCodeToString,
  localizedHelpLink,
} from '@/utils/language';
import Flag from 'react-world-flags';
import { Menu, MenuItem, SubMenu } from '@szhsin/react-menu';
import { useTranslation } from 'react-i18next';
import VersionDisplay from '@/components/VersionDisplay/VersionDisplay';

// Import your icons
import userIcon from '@/assets/svgs/light/user.svg';
import logoutIcon from '@/assets/svgs/light/logout.svg';
import languagesIcon from '@/assets/svgs/light/world.svg';
import arrowRight from '@/assets/svgs/dark/angle-right.svg';
import settingsIcon from '@/assets/svgs/light/gear.svg';
import shakeHandsIcon from '@/assets/svgs/light/shake-hands.svg';
import informationIcon from '@/assets/svgs/light/information.svg';
import { AboutDialog } from '../AboutDialog/AboutDialog';

// Styles (unchanged)
const menuItemClassName =
  'group grid grid-cols-[40px_1fr] gap-small m-1 items-center content-center';
const menuItemIconClassName = 'bg-primary p-2.5 w-10 h-10 cursor-pointer';
const menuItemLabelClassName =
  'h-10 p-1 pl-4 pr-2 uppercase bg-secondary-background whitespace-nowrap content-center group-hover:bg-hover group';

const MenuItemWithIcon: React.FC<{ icon: string; text: string; clickHandler: () => void }> = ({
  icon,
  text,
  clickHandler,
}) => {
  return (
    <MenuItem className={menuItemClassName} onClick={clickHandler}>
      <img className={menuItemIconClassName} src={icon} alt="menuItem" />
      <span className={menuItemLabelClassName}>{text}</span>
    </MenuItem>
  );
};

const LanguageMenuItem: React.FC<{
  languageCode: string;
  clickHandler: (code: string) => Promise<void>;
  currentLanguage: string;
}> = ({ languageCode, clickHandler, currentLanguage }) => {
  const { t } = useTranslation();
  const menuItemLanguageIconClassName = `${menuItemIconClassName} p-2`;
  const isActive = currentLanguage === languageCode;
  return (
    <MenuItem
      className={`${menuItemClassName} ${isActive ? 'bg-gray-100' : ''}`}
      onClick={() => clickHandler(languageCode)}
    >
      <Flag
        code={languageCodeToShortCountryCode(languageCode)}
        className={menuItemLanguageIconClassName}
      />
      <span className={menuItemLabelClassName}>
        <span>{t(languageCodeToString(languageCode))}</span>
        {isActive && <span className="pl-5 pr-2">✓</span>}
      </span>
    </MenuItem>
  );
};

const SubMenuLabel: React.FC = () => {
  const { t } = useTranslation();
  const subMenuItemClassName =
    'group grid grid-cols-[40px_1fr_40px] gap-small m-1 items-center content-center';
  const arrowIconClassName =
    'bg-secondary-background w-10 h-10 p-3.5 cursor-pointer group-hover:bg-hover';

  return (
    <span className={subMenuItemClassName}>
      <img className={menuItemIconClassName} src={languagesIcon} alt="menuItem" />
      <span className={menuItemLabelClassName}>{t('header.changeLanguage')}</span>
      <img className={arrowIconClassName} src={arrowRight} alt="menuItem" />
    </span>
  );
};

const UserSettingsMenu: React.FC = () => {
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [isChangingLanguage, setIsChangingLanguage] = useState(false);
  const [isAboutDialogOpen, setIsAboutDialogOpen] = useState(false);
  const auth = useAuth();
  const { t, i18n } = useTranslation();

  const changeLanguageHandler = useCallback(
    async (lang: string) => {
      if (isChangingLanguage || lang === i18n.language) return;

      try {
        setIsChangingLanguage(true);

        // Fix the type error by ensuring namespaces is an array
        const namespaces = Array.isArray(i18n.options.ns)
          ? i18n.options.ns
          : i18n.options.ns
            ? [i18n.options.ns]
            : ['common'];

        // Load namespaces for the new language
        await i18n.loadNamespaces(namespaces);

        // Change the language
        await i18n.changeLanguage(lang);

        // Force update all translation components
        document.dispatchEvent(new Event('languageChanged'));
      } catch (error) {
        console.error('[Language] Failed to change language:', error);
      } finally {
        setIsChangingLanguage(false);
      }
    },
    [i18n, isChangingLanguage]
  );
  const handleLogout = useCallback(async () => {
    if (isLoggingOut) return;

    try {
      setIsLoggingOut(true);

      // Set a timeout to reset the logging out state in case the logout process hangs
      const resetTimeout = setTimeout(() => {
        setIsLoggingOut(false);
        // Force redirect to login page as a fallback
        window.location.href = '/';
      }, 3000); // 3 second timeout

      await auth.signoutRedirect();

      // Clear the timeout since logout completed successfully
      clearTimeout(resetTimeout);
    } catch (error) {
      console.error('[UserSettingsMenu] Logout failed:', error);
      // Force redirect to login page on error
      window.location.href = '/login';
    } finally {
      setIsLoggingOut(false);
    }
  }, [auth, isLoggingOut]);

  const menu = useMemo(() => {
    const name = (auth.user?.profile?.username as string) || 'n/a';
    const title = (auth.user?.profile?.title as string) || t('header.userTitle');
    const showTitle = false; // Set to true to show title, false to center name only

    return (
      <Menu
        menuClassName="mt-3 z-50 bg-background drop-shadow-lg"
        menuButton={
          <div className={`flex ${showTitle ? 'content-baseline' : 'items-center'}`}>
            <span
              className={`text-inverted-light mr-medium text-right uppercase ${showTitle ? 'mt-small' : ''}`}
            >
              <span className="font-bold">{name}</span>
              {showTitle && (
                <>
                  <br />
                  {title}
                </>
              )}
            </span>
            <span className="h-14 w-14 rounded-full border border-white">
              <img src={userIcon} className="h-14 p-3.5" alt="User" />
            </span>
          </div>
        }
      >
        <SubMenu label={<SubMenuLabel />} arrow={true} menuClassName="z-50 bg-background">
          <LanguageMenuItem
            languageCode="en-US"
            clickHandler={changeLanguageHandler}
            currentLanguage={i18n.language}
          />
          <LanguageMenuItem
            languageCode="de-DE"
            clickHandler={changeLanguageHandler}
            currentLanguage={i18n.language}
          />
        </SubMenu>

        <MenuItemWithIcon
          text={t('header.settings')}
          clickHandler={() => {
            // TODO: Implement settings functionality
            console.warn('Settings functionality not yet implemented');
          }}
          icon={settingsIcon}
        />

        <MenuItemWithIcon
          text={t('header.help')}
          clickHandler={() => {
            window.open(localizedHelpLink(i18n.language), '_blank', 'noopener,noreferrer');
          }}
          icon={shakeHandsIcon}
        />

        <MenuItemWithIcon
          text={t('common.about')}
          clickHandler={() => {
            setIsAboutDialogOpen(true);
          }}
          icon={informationIcon}
        />

        <MenuItemWithIcon
          text={isLoggingOut ? t('header.loggingOut') : t('header.logout')}
          clickHandler={handleLogout}
          icon={logoutIcon}
        />
      </Menu>
    );
  }, [auth.user, isLoggingOut, t, changeLanguageHandler, handleLogout, i18n.language]);

  return (
    <>
      {menu}
      <AboutDialog
        isOpen={isAboutDialogOpen}
        onClose={() => {
          setIsAboutDialogOpen(false);
        }}
      />
    </>
  );
};

export default UserSettingsMenu;
