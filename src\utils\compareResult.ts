import { useTranslation } from 'react-i18next';

export enum CompareResult {
  Equal = 1,
  EqualWithWarning,
  NotEqual,
  NotEqualWithWarning,
  Unknown,
  UnknownWithWarning,
  Error,
  Deactivated,
  None,
}

// Sets for condition checks
const equalResults = new Set<CompareResult>([CompareResult.Equal, CompareResult.EqualWithWarning]);

const notEqualResults = new Set<CompareResult>([
  CompareResult.NotEqual,
  CompareResult.NotEqualWithWarning,
]);

const warningResults = new Set<CompareResult>([
  CompareResult.EqualWithWarning,
  CompareResult.NotEqualWithWarning,
  CompareResult.UnknownWithWarning,
]);

const noResults = new Set<CompareResult>([
  CompareResult.UnknownWithWarning,
  CompareResult.Unknown,
  CompareResult.None,
]);

// Utility functions
export const isEqual = (res: CompareResult): boolean => equalResults.has(res);
export const isNotEqual = (res: CompareResult): boolean => notEqualResults.has(res);
export const noResult = (res: CompareResult): boolean => noResults.has(res);
export const hasWarning = (res: CompareResult): boolean => warningResults.has(res);
export const hasError = (res: CompareResult): boolean => res === CompareResult.Error;

const backgroundColorMap: Record<CompareResult, string> = {
  [CompareResult.Equal]: 'bg-equal',
  [CompareResult.EqualWithWarning]: 'bg-warning',
  [CompareResult.NotEqual]: 'bg-not-equal',
  [CompareResult.NotEqualWithWarning]: 'bg-warning',
  [CompareResult.Unknown]: 'bg-unknown',
  [CompareResult.UnknownWithWarning]: 'bg-warning',
  [CompareResult.Error]: 'bg-error',
  [CompareResult.Deactivated]: '',
  [CompareResult.None]: 'bg-unknown',
};

const textColorMap: Record<CompareResult, string> = {
  [CompareResult.Equal]: 'text-equal',
  [CompareResult.EqualWithWarning]: 'text-equal',
  [CompareResult.NotEqual]: 'text-not-equal',
  [CompareResult.NotEqualWithWarning]: 'text-not-equal',
  [CompareResult.Unknown]: 'text-primary',
  [CompareResult.UnknownWithWarning]: 'text-primary',
  [CompareResult.Error]: 'text-error',
  [CompareResult.Deactivated]: 'text-error',
  [CompareResult.None]: 'text-primary',
};

const textColorOnIconMap: Record<CompareResult, string> = {
  [CompareResult.Equal]: 'text-inverted-light',
  [CompareResult.EqualWithWarning]: 'text-primary',
  [CompareResult.NotEqual]: 'text-inverted-light',
  [CompareResult.NotEqualWithWarning]: 'text-primary',
  [CompareResult.Unknown]: 'text-primary',
  [CompareResult.UnknownWithWarning]: 'text-primary',
  [CompareResult.Error]: 'text-inverted-light',
  [CompareResult.Deactivated]: 'text-inverted-light',
  [CompareResult.None]: 'text-primary',
};

const comparatorMap: Record<CompareResult, string> = {
  [CompareResult.Equal]: '\uA78A',
  [CompareResult.EqualWithWarning]: '\uA78A',
  [CompareResult.NotEqual]: '\u2260',
  [CompareResult.NotEqualWithWarning]: '\u2260',
  [CompareResult.Unknown]: '?',
  [CompareResult.UnknownWithWarning]: '?',
  [CompareResult.Error]: '\uD83D\uDFAB',
  [CompareResult.Deactivated]: '\uD83D\uDEAB',
  [CompareResult.None]: '?',
};

const apiResultString: Record<string, CompareResult> = {
  ['Equal']: CompareResult.Equal,
  ['Equal with warning']: CompareResult.EqualWithWarning,
  ['Different']: CompareResult.NotEqual,
  ['Different with warning']: CompareResult.NotEqualWithWarning,
  ['Unknown']: CompareResult.Unknown,
  ['None']: CompareResult.None,
  ['Error']: CompareResult.Error,
};

// Utility functions using mapping objects
export const displayTextForCompareResult = (res: CompareResult): string => {
  const { t } = useTranslation();
  const displayTextMap: Record<CompareResult, string> = {
    [CompareResult.Equal]: t('jobs.equal'),
    [CompareResult.EqualWithWarning]: t('jobs.equal') + ' ' + t('jobs.withWarning'),
    [CompareResult.NotEqual]: t('jobs.notEqual'),
    [CompareResult.NotEqualWithWarning]: t('jobs.notEqual') + ' ' + t('jobs.withWarning'),
    [CompareResult.Unknown]: t('common.unknown'),
    [CompareResult.UnknownWithWarning]: t('common.unknown') + ' ' + t('jobs.withWarning'),
    [CompareResult.Error]: t('common.error'),
    [CompareResult.Deactivated]: t('common.deactivated'),
    [CompareResult.None]: t('common.unknown'),
  };

  return displayTextMap[res] || t('common.unknown');
};

export const backgroundColorForCompareResult = (res: CompareResult): string =>
  backgroundColorMap[res] || 'bg-yellow';

export const textColorForCompareResult = (res: CompareResult): string =>
  textColorMap[res] || 'text-yellow';

export const textColorOnIconForCompareResult = (res: CompareResult): string =>
  textColorOnIconMap[res] || 'text-inverted-light';

export const comparatorForCompareResult = (res: CompareResult): string => comparatorMap[res] || '?';

export const doCombineResult = (
  currentResult: CompareResult,
  currentCombinedResult: CompareResult
): CompareResult => {
  if (currentResult === CompareResult.Error || currentCombinedResult === CompareResult.Error)
    return CompareResult.Error;
  if (currentCombinedResult === currentResult) return currentResult;

  if (noResult(currentResult)) return currentCombinedResult;
  if (noResult(currentCombinedResult)) return currentResult;

  if (isEqual(currentCombinedResult) && isEqual(currentResult)) {
    return hasWarning(currentCombinedResult) || hasWarning(currentResult)
      ? CompareResult.EqualWithWarning
      : CompareResult.Equal;
  }

  return hasWarning(currentCombinedResult) || hasWarning(currentResult)
    ? CompareResult.NotEqualWithWarning
    : CompareResult.NotEqual;
};

export const transformApiResult = (res: string, hasWarning: boolean): CompareResult => {
  let result = apiResultString[res] || CompareResult.Unknown;
  if (hasWarning && result == CompareResult.Equal) result = CompareResult.EqualWithWarning;
  if (hasWarning && result == CompareResult.NotEqual) result = CompareResult.NotEqualWithWarning;
  if (hasWarning && noResult(result)) result = CompareResult.UnknownWithWarning;

  return result;
};

export default CompareResult;
