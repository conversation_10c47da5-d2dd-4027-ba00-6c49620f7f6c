import { useCallback } from 'react';

const displayTextMap: Record<string, string> = {
  'de-DE': 'languages.german',
  'en-US': 'languages.english',
};

const shortCountryCodeMap: Record<string, string> = {
  'de-DE': 'DE',
  'en-US': 'US',
};

const shortLanguageMap: Record<string, string> = {
  'de-DE': 'de',
  'en-US': 'en',
};

export const languageCodeToShortLanguageCode = (code: string): string => {
  return shortLanguageMap[code] || 'en';
};

export const languageCodeToShortCountryCode = (code: string): string => {
  return shortCountryCodeMap[code] || 'US';
};

export const languageCodeToString = (code: string): string => {
  return displayTextMap[code] || 'languages.english';
};

export const localizedHelpLink = (code: string): string => {
  return (
    'https://info.octoplant.com/octoplant-info/public/' +
    languageCodeToShortLanguageCode(code).toLowerCase() +
    '/main/'
  );
};
