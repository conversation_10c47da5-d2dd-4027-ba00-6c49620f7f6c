/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_API_URL: string;
  readonly VITE_OAUTH_URL: string;
  readonly VITE_STORAGE_URL: string;
  readonly VITE_API_TIMEOUT: string;
  readonly VITE_CACHE_ENABLED: string;
  readonly VITE_EXPERIMENTAL: string;
  readonly VITE_CACHE_SIZE_MB: string;
  readonly VITE_CACH_MAX_AGE: string;
  // Add any other environment variables you might be using
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
