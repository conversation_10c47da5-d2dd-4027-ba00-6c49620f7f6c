import React from 'react';

export const LoadingSpinner: React.FC = () => (
  <div className="flex-center h-screen">
    <div className="spinner-primary spinner-lg" />
  </div>
);

interface LoaduingSpinnerSmallProps {
  size?: number;
}

export const LoadingSpinnerSmall: React.FC<LoaduingSpinnerSmallProps> = ({ size = 4 }) => {
  const sizeClass = size === 4 ? 'spinner-sm' : size === 6 ? 'spinner-md' : 'spinner-lg';
  return <div className={`flex-shrink-0 spinner-primary ${sizeClass}`} />;
};
