# PowerShell script to fix the nginx OAuth loop issue
# This script applies the necessary fixes for the OAuth redirect loop

Write-Host "🔧 Fixing nginx OAuth redirect loop..." -ForegroundColor Green

# Step 1: Backup current nginx configuration
if (Test-Path "nginx.conf") {
    Copy-Item "nginx.conf" "nginx.conf.backup" -Force
    Write-Host "✅ Backed up current nginx.conf to nginx.conf.backup" -ForegroundColor Green
}

# Step 2: Replace nginx configuration with fixed version
if (Test-Path "nginx-fixed.conf") {
    Copy-Item "nginx-fixed.conf" "nginx.conf" -Force
    Write-Host "✅ Applied fixed nginx configuration" -ForegroundColor Green
} else {
    Write-Host "❌ nginx-fixed.conf not found!" -ForegroundColor Red
    exit 1
}

# Step 3: Rebuild the application with correct environment
Write-Host "🔨 Rebuilding application..." -ForegroundColor Cyan
try {
    npm run build
    Write-Host "✅ Application rebuilt successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Build failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 4: Restart nginx (if running)
Write-Host "🔄 Attempting to restart nginx..." -ForegroundColor Cyan
try {
    # Try to stop nginx gracefully
    nginx -s quit 2>$null
    Start-Sleep -Seconds 2
    
    # Start nginx with new configuration
    nginx
    Write-Host "✅ nginx restarted with new configuration" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Could not restart nginx automatically. Please restart manually." -ForegroundColor Yellow
    Write-Host "   Run: nginx -s quit && nginx" -ForegroundColor White
}

Write-Host ""
Write-Host "🎉 OAuth loop fix applied!" -ForegroundColor Green
Write-Host ""
Write-Host "Summary of changes:" -ForegroundColor Cyan
Write-Host "  ✅ Updated authSettings.ts to use nginx proxy endpoints" -ForegroundColor White
Write-Host "  ✅ Fixed nginx configuration to properly handle OAuth paths" -ForegroundColor White
Write-Host "  ✅ Rebuilt application with correct environment" -ForegroundColor White
Write-Host ""
Write-Host "The OAuth flow should now work correctly through nginx!" -ForegroundColor Green
