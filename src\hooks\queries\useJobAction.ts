import { useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/services/httpClient';

type JobAction = 'start' | 'stop';

// Define the expected shape of variables for the mutation function
interface JobActionVariables {
  apiJobId: string; // ID used for the API call
  originalJobId: string; // ID used for query invalidation
  action: JobAction;
}

export const useJobAction = () => {
  const queryClient = useQueryClient();

  return useMutation<unknown, Error, JobActionVariables>({
    mutationFn: async ({ apiJobId, action }: JobActionVariables) => {
      return apiClient.post(`Jobs/${apiJobId}/${action}`);
    },
    onSuccess: (_, variables) => {
      // Use the originalJobId from variables for invalidation
      queryClient.invalidateQueries({ queryKey: ['jobStatus', variables.originalJobId] });
    },
  });
};
