import React, { useContext } from 'react';
import arrowIcon from '@/assets/svgs/dark/treeview-close.svg';
import { TreeContext } from '@/context/TreeContext';

const TreeViewToggle: React.FC = () => {
  const { isTreeVisible, toggleTreeVisibility } = useContext(TreeContext) ?? {};

  if (!toggleTreeVisibility) {
    throw new Error('TreeViewToggle must be used within a TreeProvider');
  }

  return (
    <div className="flex flex-col w-6 h-full bg-background flex-shrink-0">
      <div className="w-[1px] bg-primary h-8 mx-auto" />
      <div className="flex justify-center items-center">
        <img
          src={arrowIcon}
          alt="Toggle Tree View"
          className={`h-6 w-6 p-1 border border-primary rounded-sm transition-transform cursor-pointer ${
            isTreeVisible ? '' : 'rotate-180'
          }`}
          onClick={toggleTreeVisibility}
          role="button"
          tabIndex={0}
        />
      </div>
      <div className="w-[1px] bg-primary grow mx-auto" />
    </div>
  );
};

export default React.memo(TreeViewToggle);
