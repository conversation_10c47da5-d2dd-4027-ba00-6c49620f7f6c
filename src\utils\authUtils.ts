import { settings } from '@/authSettings';

/**
 * Clears all OIDC-related storage items from localStorage
 * This is useful when encountering authority mismatch errors
 * @param preserveState If true, will not clear the state parameter
 */
export const clearOidcStorage = (preserveState = true): void => {
  console.log('[AuthUtils] Clearing OIDC storage (preserveState:', preserveState, ')');

  // Save state if needed
  let savedState = null;
  if (preserveState) {
    savedState = localStorage.getItem('oidc.state') || sessionStorage.getItem('oidc.state');
    console.log('[AuthUtils] Preserving state:', savedState);
  }

  // Clear specific user storage for current client
  localStorage.removeItem(`oidc.user:${settings.authority}:${settings.client_id}`);

  // Clear all items that start with 'oidc.' except state if preserving
  Object.keys(localStorage).forEach(key => {
    if (key.startsWith('oidc.') && (!preserveState || key !== 'oidc.state')) {
      console.log(`[AuthUtils] Removing localStorage item: ${key}`);
      localStorage.removeItem(key);
    }
  });

  // Clear session storage items related to OIDC
  Object.keys(sessionStorage).forEach(key => {
    if (key.startsWith('oidc.') && (!preserveState || key !== 'oidc.state')) {
      console.log(`[AuthUtils] Removing sessionStorage item: ${key}`);
      sessionStorage.removeItem(key);
    }
  });

  // Restore state if needed
  if (preserveState && savedState) {
    console.log('[AuthUtils] Restoring state:', savedState);
    localStorage.setItem('oidc.state', savedState);
    sessionStorage.setItem('oidc.state', savedState);
  }
};

/**
 * Checks if the error is related to authority mismatch
 */
export const isAuthorityMismatchError = (error: Error | unknown): boolean => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  return errorMessage.includes('authority mismatch');
};

/**
 * Checks if the error is related to missing state
 */
export const isNoMatchingStateError = (error: Error | unknown): boolean => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  return errorMessage.includes('No matching state found in storage');
};
