import React from 'react';

interface HLineWithIconProps {
  icon: string;
  clickHandler?: () => void;
}

export const HLineWithIcon: React.FC<HLineWithIconProps> = ({ icon, clickHandler }) => {
  return (
    <div>
      <HLine />
      <img
        className="relative pr-3 pl-3 h-2.5 bg-background left-4 top-[-5px]"
        src={icon}
        onClick={clickHandler}
      />
    </div>
  );
};

const HLine: React.FC = () => {
  return <div className="bg-secondary-background h-[2px] w-full mt-12" />;
};

export default HLine;
